# -*- coding: gbk -*-
"""
ZZdaytrend.py - ???/?????????????????

?????/???????????????????????????????????????

????????
1. ????6λ???????????????????????????
2. ???????????ж??????η??0-2???
3. ????????????ж??????????0-2???
4. ????????????0???1????????????????

???????
- ??η? + ???? ?? 3???????÷?1??
- ?????????????÷?0??

?????????ZZsignal.py??ZZindicator.py??ZZfetch.py????
?????2025-01-28
"""

import sys
import os
import re
from datetime import datetime, timedelta

# ???????????·??
project_root = os.path.abspath(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# ???????????
try:
    from ZZsignal import TechnicalSignalDetector, get_stock_data
    ZZSIGNAL_AVAILABLE = True
except ImportError:
    ZZSIGNAL_AVAILABLE = False
    print("????: ZZsignal???δ???")

try:
    from ZZindicator import calculate_indicators_from_dataframe
    ZZINDICATOR_AVAILABLE = True
except ImportError:
    ZZINDICATOR_AVAILABLE = False
    print("????: ZZindicator???δ???")

try:
    from ZZfetch import get_fetcher, connect_miniqmt, disconnect_miniqmt
    ZZFETCH_AVAILABLE = True
except ImportError:
    ZZFETCH_AVAILABLE = False
    print("????: ZZfetch???δ???")


class TrendScorer:
    """??????????"""
    
    def __init__(self):
        """???????????????"""
        self.stock_code = None
        self.data = None
        self.signals = []
        self.stage_score = 0
        self.strength_score = 0
        self.final_score = 0
        self.stage_reason = ""
        self.strength_reason = ""
        
        # ????????????
        self.trend_start_signals = [
            "DIF???????",
            "RSI6???50", 
            "MA5???????",
            "MACD???",
            "MA5???MA10"
        ]
        
        self.trend_confirm_signals = [
            "DIF?????????",
            "MACD??????????",
            "????????????MA5???",
            "RSI6?????????50????"
        ]
        
        self.trend_end_signals = [
            "DIF???????",
            "DIF????????"
        ]
        
        # ?????????????
        self.strong_trend_signals = [
            "????????MAVOL5????????",
            "3????????K???MAVOL5????",
            "4????????K???VOL????MAVOL5"
        ]
        
        self.weak_trend_signals = [
            "3????????K???MAVOL5???",
            "4????????K???VOLС??MAVOL5"
        ]
    
    def load_stock_data(self, stock_code, timeframe="日线"):
        """??????????"""
        if not ZZFETCH_AVAILABLE:
            print("????: ZZfetch??鲻????")
            return False
        
        self.stock_code = stock_code
        print(f"????????? {stock_code} ??????...")
        
        # ??????????
        self.data = get_stock_data(stock_code, timeframe)
        if self.data is None or self.data.empty:
            print(f"????: ????????? {stock_code} ??????")
            return False
        
        print(f"? ?????? {len(self.data)} ??????")
        return True
    
    def detect_signals(self):
        """????????"""
        if not ZZSIGNAL_AVAILABLE:
            print("????: ZZsignal??鲻????")
            return False
        
        if self.data is None:
            print("????: δ????????")
            return False
        
        print("???????????...")
        
        # ???????????
        detector = TechnicalSignalDetector()
        
        # ????????
        if not detector.load_data(self.data, "日线"):
            print("????: ?????????????????")
            return False
        
        # ?????????
        if not detector.calculate_indicators():
            print("????: ?????????????")
            return False
        
        # ??????????
        self.signals = detector.detect_all_signals()
        
        print(f"? ??? {len(self.signals)} ?????????")
        return True
    
    def get_recent_signals(self, periods=5):
        """??????N??????????????"""
        if not self.signals:
            return []

        # 获取最近N个交易日的日期列表，转换为字符串格式便于比较
        recent_dates = self.data['日期'].tail(periods).tolist()
        recent_date_strs = []
        for date in recent_dates:
            if hasattr(date, 'strftime'):
                recent_date_strs.append(date.strftime('%Y-%m-%d'))
            else:
                recent_date_strs.append(str(date)[:10])  # 取前10个字符作为日期

        recent_signals = []
        for signal in self.signals:
            signal_time = signal.signal_time

            # 转换信号时间为字符串格式
            signal_date_str = ""
            if hasattr(signal_time, 'strftime'):
                signal_date_str = signal_time.strftime('%Y-%m-%d')
            else:
                # 处理numpy.datetime64类型
                signal_date_str = str(signal_time)[:10]

            # 检查信号日期是否在最近的交易日内
            if signal_date_str in recent_date_strs:
                recent_signals.append(signal)

        return recent_signals
    
    def calculate_stage_score(self):
        """?????η?"""
        recent_signals = self.get_recent_signals(5)
        
        if not recent_signals:
            self.stage_score = 0
            self.stage_reason = "δ??????5?????????????"
            return
        
        # ???????
        start_signals = []
        confirm_signals = []
        end_signals = []
        
        for signal in recent_signals:
            signal_name = signal.signal_name
            if signal_name in self.trend_start_signals:
                start_signals.append(signal)
            elif signal_name in self.trend_confirm_signals:
                confirm_signals.append(signal)
            elif signal_name in self.trend_end_signals:
                end_signals.append(signal)
        
        print(f"\n=== ??η???? ===")
        print(f"?????????: {len(start_signals)} ??")
        print(f"????????: {len(confirm_signals)} ??")
        print(f"??????????: {len(end_signals)} ??")
        
        # ????????????
        if start_signals and end_signals:
            # ???????????????
            latest_start = max(start_signals, key=lambda x: x.signal_time)
            latest_end = max(end_signals, key=lambda x: x.signal_time)
            
            if latest_start.signal_time >= latest_end.signal_time:
                # ????????????
                if confirm_signals:
                    self.stage_score = 1  # ?????
                    self.stage_reason = f"???????????????????????????ж??????Σ?1???"
                else:
                    self.stage_score = 2  # ??????
                    self.stage_reason = f"?????????????Σ?{latest_start.signal_name}??2???"
            else:
                # ?????????????
                self.stage_score = 0  # ???????
                self.stage_reason = f"??????????????Σ?{latest_end.signal_name}??0???"
        elif start_signals:
            if confirm_signals:
                self.stage_score = 1  # ?????
                self.stage_reason = f"????????????????????????ж??????Σ?1???"
            else:
                self.stage_score = 2  # ??????
                self.stage_reason = f"?????????????{[s.signal_name for s in start_signals]}??2???"
        elif confirm_signals and end_signals:
            self.stage_score = 0  # ???????????
            self.stage_reason = f"????????????????????????ж????????Σ?0???"
        elif confirm_signals:
            self.stage_score = 1  # ?????
            self.stage_reason = f"????????????{[s.signal_name for s in confirm_signals]}??1???"
        elif end_signals:
            self.stage_score = 0  # ???????
            self.stage_reason = f"??????????????{[s.signal_name for s in end_signals]}??0???"
        else:
            self.stage_score = 0
            self.stage_reason = "δ????????????????0???"
        
        print(f"??η?: {self.stage_score}??")
        print(f"?ж?????: {self.stage_reason}")

    def calculate_strength_score(self):
        """????????"""
        recent_signals = self.get_recent_signals(5)

        if not recent_signals:
            self.strength_score = 1  # ???????
            self.strength_reason = "δ??????5???????????????????????1???"
            return

        # ???????
        strong_signals = []
        weak_signals = []

        for signal in recent_signals:
            signal_name = signal.signal_name
            if signal_name in self.strong_trend_signals:
                strong_signals.append(signal)
            elif signal_name in self.weak_trend_signals:
                weak_signals.append(signal)

        print(f"\n=== ??????? ===")
        print(f"????????: {len(strong_signals)} ??")
        print(f"?????????: {len(weak_signals)} ??")

        # ??????
        if strong_signals:
            self.strength_score = 2  # ?????
            self.strength_reason = f"????????????{[s.signal_name for s in strong_signals]}??2???"
        elif weak_signals:
            self.strength_score = 0  # ??????
            self.strength_reason = f"?????????????{[s.signal_name for s in weak_signals]}??0???"
        else:
            self.strength_score = 1  # ????
            self.strength_reason = "??????????????????????1???"

        print(f"????: {self.strength_score}??")
        print(f"?ж?????: {self.strength_reason}")

    def calculate_final_score(self):
        """????????????"""
        total_score = self.stage_score + self.strength_score

        if total_score >= 3:
            self.final_score = 1
        else:
            self.final_score = 0

        print(f"\n=== ???????? ===")
        print(f"??η?: {self.stage_score}??")
        print(f"????: {self.strength_score}??")
        print(f"???: {total_score}??")
        print(f"????÷?: {self.final_score}?? ({'???' if self.final_score == 1 else '?????'})")

    def print_detailed_analysis(self):
        """?????????????"""
        print(f"\n{'='*60}")
        print(f"??? {self.stock_code} ???????????????")
        print(f"{'='*60}")

        print(f"\n??????÷??: {self.final_score}??")

        print(f"\n????η??: {self.stage_score}??")
        print(f"?ж?????: {self.stage_reason}")

        print(f"\n???????: {self.strength_score}??")
        print(f"?ж?????: {self.strength_reason}")

        print(f"\n?????????:")
        print(f"- ??η? + ???? ?? 3???????÷?1??")
        print(f"- ?????????????÷?0??")
        print(f"- ??????: {self.stage_score + self.strength_score}??")

        # ???????????????
        recent_signals = self.get_recent_signals(5)
        if recent_signals:
            print(f"\n???????????????????5???????:")
            for i, signal in enumerate(recent_signals, 1):
                direction_symbol = "?J" if signal.signal_direction == "????" else "?K" if signal.signal_direction == "????" else "??"
                print(f"{i}. {direction_symbol} {signal.signal_name}")
                if hasattr(signal, 'description') and signal.description:
                    print(f"   {signal.description}")
        else:
            print(f"\n??????????????: ??")

    def analyze(self, stock_code):
        """????????????????"""
        print(f"?????????? {stock_code} ??????????...")

        # 1. ????????
        if not self.load_stock_data(stock_code):
            return None

        # 2. ??????
        if not self.detect_signals():
            return None

        # 3. ?????η?
        self.calculate_stage_score()

        # 4. ????????
        self.calculate_strength_score()

        # 5. ????????????
        self.calculate_final_score()

        # 6. ??????????
        self.print_detailed_analysis()

        return {
            'stock_code': self.stock_code,
            'stage_score': self.stage_score,
            'strength_score': self.strength_score,
            'final_score': self.final_score,
            'stage_reason': self.stage_reason,
            'strength_reason': self.strength_reason
        }


def analyze_stock_trend(stock_code):
    """?????????????????????"""
    scorer = TrendScorer()
    return scorer.analyze(stock_code)


def main():
    """?????????"""
    print("???/?????????????????")
    print("???6λ?????????????????")
    print("???? 'q' ???????\n")

    # ??????????
    if not all([ZZSIGNAL_AVAILABLE, ZZINDICATOR_AVAILABLE, ZZFETCH_AVAILABLE]):
        print("????: ?????????????飬???????????")
        return

    while True:
        try:
            # ??????????
            user_input = input("????????????(6λ????): ").strip()

            if user_input.lower() == 'q':
                print("???????")
                break

            if not user_input:
                continue

            # ????????????
            if not re.match(r'^\d{6}$', user_input):
                print("????: ??????6λ???????????")
                continue

            # ???????????
            result = analyze_stock_trend(user_input)

            if result:
                print(f"\n??????????? {user_input} ???????????: {result['final_score']}??")
            else:
                print(f"?????????????????? {user_input} ??????")

            print(f"\n{'-'*60}")

        except KeyboardInterrupt:
            print("\n????????ж?")
            break
        except Exception as e:
            print(f"???????г???: {e}")
            import traceback
            traceback.print_exc()
            continue


if __name__ == "__main__":
    main()
