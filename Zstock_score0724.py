# -*- coding: gbk -*-
# ??????????????
# ????????
# 1. ?????format_stock_info???????????????????????
# 2. ??????get_historical_data??????????????????????????????????????????????????????
# 3. ?????enhance_tech_info??????????????stock_tech.py??"1. ???20????????K???"?????????????????Z???
# 4. ?????analyze_stock????????????????????????????????????????????????
# 5. ?????????????????????????????????????????????????????

import os
import time
import sys
import requests  # ????requests??????HTTP????
import json  # ????json????????????
from deepseek_client import DeepSeekClient

# ????????????????
CHAN_ANALYSIS_AVAILABLE = False
try:
    import Zstock_find3buy
    CHAN_ANALYSIS_AVAILABLE = True
except ImportError:
    print("???????????????????????????????????????")
import Zstock_tech_qmttdx_bak as stock_tech
import concurrent.futures
import threading
from queue import Queue
import re
import pandas as pd
import traceback  # ??????????????????
import glob
from datetime import datetime, timedelta
import numpy as np
import random
import akshare as ak

# ????????????
# use import * ???????reload???
import Zstock_industry_tdx as industry_tech
import ZJBM  # ???????????????

# ???????3????????????????????
try:
    import Zstock_find3buy
    CHAN_ANALYSIS_AVAILABLE = True
except ImportError as e:
    print(f"??????????y?????: {e}")
    CHAN_ANALYSIS_AVAILABLE = False

def get_timestamp():
    """??????????????????????????????"""
    # ???????????????????????????????
    return datetime.now().strftime("%Y%m%d_%H%M%S")

class VolcesClient:
    """???????????API?????"""
    
    def __init__(self, api_key="e1283034-c29c-41bc-a10f-14e32fbe828b", model="doubao-seed-1-6-250615"):
        self.api_url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
        self.api_key = api_key
        self.model = model
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        self.connected = True  # ????????????
    
    def connect(self):
        """?????????"""
        # ???????API????????????????????????????HTTP????
        # ?????API?????????
        if not self.api_key:
            print("API??????????")
            self.connected = False
            return False
        
        self.connected = True
        return True
    
    def send(self, prompt):
        """?????????????"""
        if not self.connected:
            print("???????????")
            return ""
        
        try:
            payload = {
                "model": self.model,
                "messages": [
                    {
                        "role": "user",
                        "content": [{"type": "text", "text": prompt}]
                    }
                ],
                "temperature": 0.5  # ????temperature?????????????
            }
            
            response = requests.post(
                self.api_url, 
                headers=self.headers, 
                json=payload
            )
            
            if response.status_code != 200:
                print(f"API????: {response.status_code} - {response.text}")
                # ?????????????????
                print(f"????????????: {response.status_code}")
                print(f"???????????: {response.text}")
                
                try:
                    error_data = response.json()
                    if "error" in error_data:
                        error_type = error_data.get('error', {}).get('type', '???')
                        error_msg = error_data.get('error', {}).get('message', '????????')
                        print(f"????????: {error_type}")
                        print(f"???????: {error_msg}")
                    elif "code" in error_data:
                        error_code = error_data.get('code')
                        error_msg = error_data.get('message', '????????')
                        print(f"???????: {error_code}")
                        print(f"???????: {error_msg}")
                except Exception as parse_error:
                    print(f"??????????????: {parse_error}")
                
                return ""
            
            result = response.json()
            # ?????????? - ???????doubao-seed-1-6????????????
            content = ""
            message = result.get("choices", [{}])[0].get("message", {})
            if "content" in message:
                content = message.get("content", "")
            else:
                # ??????????????????
                content_list = message.get("content", [])
                if content_list and isinstance(content_list, list):
                    for item in content_list:
                        if item.get("type") == "text":
                            content += item.get("text", "")
            
            return content
        
        except Exception as e:
            print(f"???????????: {e}")
            print(f"????????: {traceback.format_exc()}")
            return ""
    
    def stream(self, prompt):
        """????????????????"""
        if not self.connected:
            print("???????????")
            return
        
        try:
            payload = {
                "model": self.model,
                "stream": True,
                "messages": [
                    {
                        "role": "user",
                        "content": [{"type": "text", "text": prompt}]
                    }
                ],
                "temperature": 0.5  # ????temperature?????????????
            }
            
            response = requests.post(
                self.api_url, 
                headers=self.headers, 
                json=payload,
                stream=True
            )
            
            if response.status_code != 200:
                print(f"API????: {response.status_code} - {response.text}")
                yield ""
                return
            
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        data = line[6:]
                        if data.strip() == '[DONE]':
                            break
                        try:
                            json_data = json.loads(data)
                            # ??????????????????????
                            delta = json_data.get('choices', [{}])[0].get('delta', {})
                            if 'content' in delta:
                                # ????
                                content = delta.get('content', '')
                                if content:
                                    yield content
                            else:
                                # ???? - ????????????????content
                                content_list = delta.get('content', [])
                                if content_list and isinstance(content_list, list):
                                    for item in content_list:
                                        if item.get('type') == 'text':
                                            text = item.get('text', '')
                                            if text:
                                                yield text
                        except json.JSONDecodeError:
                            pass
        
        except Exception as e:
            print(f"??????????: {e}")
            yield ""
    
    def close(self):
        """???????"""
        self.connected = False
        # ????????????????????????????HTTP????

def find_name_column(columns):
    """?????????????????'????'??'????(x)'???"""
    # ?????????????????'????(x)'???????
    for col in columns:
        if isinstance(col, str) and col.startswith('????(') and col.endswith(')'):
            return col
    
    # ?????????????????'????'????
    for col in columns:
        if isinstance(col, str) and col == '????':
            return col
    
    # ????????????'????'???????
    for col in columns:
        if isinstance(col, str) and col.startswith('????'):
            return col
    
    return None

def clean_stock_code(code):
    """????????????????????????????6?????"""
    if not code:
        return ""
    
    # ???????
    code_str = str(code)
    
    # ????????????????????'='???
    digits_only = ''.join(c for c in code_str if c.isdigit())
    
    # ???6???????????????0
    if digits_only:
        return digits_only.zfill(6)
    
    return ""

def read_stock_data(file_path):
    """??????????????????xls??txt???????????'????'??'????(x)'??"""
    try:
        # ????????????????????
        file_ext = os.path.splitext(file_path)[1].lower()
        
        if file_ext == '.xls' or file_ext == '.xlsx':
            # ???????????????????
            try:
                print(f"?????????: {file_path}")
                with open(file_path, 'r', encoding='gbk', errors='ignore') as f:
                    lines = f.readlines()
                
                if not lines:
                    raise ValueError("??????")
                
                # ???????
                header = [col.strip() for col in lines[0].strip().split('\t') if col.strip()]
                
                # ??????????
                name_col = find_name_column(header)
                if not name_col:
                    raise ValueError("???????????")
                
                # ????????
                stocks = []
                for i, line in enumerate(lines[1:], 1):
                    if not line.strip():
                        continue
                    values = [val.strip() for val in line.strip().split('\t')]
                    # ????????????????????
                    if len(values) >= len(header):
                        stock = dict(zip(header, values))
                        # ??????????????????
                        if '????' in stock and name_col in stock and stock['????'] and stock[name_col]:
                            # ????????????????????
                            stock['????'] = clean_stock_code(stock['????'])
                            # ?????????'????'
                            stock['????'] = stock[name_col]
                            stocks.append(stock)
                    else:
                        print(f"????: ??{i+1}????????????({len(values)})??????????({len(header)})????????")
                
                print(f"?????? {len(stocks)} ????????")
                return header, stocks, name_col
                
            except Exception as e:
                print(f"??????????????: {e}????????Excel??????...")
                # ??????????????????Excel???
                try:
                    if file_ext == '.xlsx':
                        df = pd.read_excel(file_path, engine='openpyxl')
                    else:
                        # ????.xls????????????xlrd????
                        df = pd.read_excel(file_path, engine='xlrd')
                except Exception as e2:
                    # ??????????
                    try:
                        engine = 'openpyxl' if file_ext == '.xlsx' else 'xlrd'
                        df = pd.read_excel(file_path, engine=engine)
                    except Exception as e3:
                        # ???????????
                        raise ValueError(f"???????????????????????: {e}, {e2}, {e3}")
                
                # ??????????
                name_col = find_name_column(df.columns)
                if not name_col:
                    raise ValueError("???????????")
                
                # ??DataFrame???????????
                stocks = []
                for _, row in df.iterrows():
                    stock = row.to_dict()
                    # ??????????????????
                    if '????' in stock and name_col in stock and pd.notna(stock['????']) and pd.notna(stock[name_col]):
                        # ??????????????????
                        stock['????'] = clean_stock_code(stock['????'])
                        # ?????????'????'
                        stock['????'] = stock[name_col]
                        stocks.append(stock)
                
                print(f"?????? {len(stocks)} ????????")
                return df.columns.tolist(), stocks, name_col
            
        else:  # ????txt???
            with open(file_path, 'r', encoding='gbk', errors='ignore') as f:
                lines = f.readlines()
            
            if not lines:
                raise ValueError("??????")
            
            # ???????
            header = [col.strip() for col in lines[0].strip().split('\t') if col.strip()]
            
            # ??????????
            name_col = find_name_column(header)
            if not name_col:
                raise ValueError("???????????")
            
            # ????????
            stocks = []
            for i, line in enumerate(lines[1:], 1):
                if not line.strip():
                    continue
                values = [val.strip() for val in line.strip().split('\t')]
                # ????????????????????
                if len(values) >= len(header):
                    stock = dict(zip(header, values))
                    # ??????????????????
                    if '????' in stock and name_col in stock and stock['????'] and stock[name_col]:
                        # ???????????
                        stock['????'] = clean_stock_code(stock['????'])
                        # ?????????'????'
                        stock['????'] = stock[name_col]
                        stocks.append(stock)
                else:
                    print(f"????: ??{i+1}????????????({len(values)})??????????({len(header)})????????")
            
            print(f"?????? {len(stocks)} ????????")
            return header, stocks, name_col
    except Exception as e:
        print(f"???????????: {e}")
        raise

def get_historical_data(stock_code, original_headers=None, base_dir="D:\\stock\\tdxdata", current_stock=None, use_simulation=False):
    """???????????????????
    
    Args:
        stock_code: ???????
        original_headers: ???????????????????????????????????
        base_dir: ?????????
        current_stock: ????????????????(?????????????)
        use_simulation: ?????????????(?????????????????)
    
    Returns:
        str: ???????????????????????????????
    """
    try:
        if original_headers is None:
            original_headers = []
        
        # ??????????????????????
        date_data_mapping = {}
        
        # ?????????????????????
        current_date_obj = datetime.now()
        current_date = current_date_obj.strftime('%Y%m%d')
        current_date_formatted = current_date_obj.strftime('%Y-%m-%d')
        
        # ????????????????????????(t??)????
        current_main_flow = None
        current_main_buy = None
        current_inner_volume = None
        current_outer_volume = None
        
        # ???current_stock?????????
        if current_stock and isinstance(current_stock, dict):
            for key, value in current_stock.items():
                if value and str(value).strip() and not pd.isna(value):
                    key_lower = key.lower()
                    if '????????' in key_lower:
                        try:
                            current_main_flow = float(str(value).replace(',', ''))
                        except (ValueError, TypeError):
                            current_main_flow = 0
                    elif '?????' in key_lower:
                        try:
                            current_main_buy = float(str(value).replace(',', ''))
                        except (ValueError, TypeError):
                            current_main_buy = 0
                    elif '????' in key_lower and '????' not in key_lower:
                        try:
                            current_inner_volume = float(str(value).replace(',', ''))
                        except (ValueError, TypeError):
                            current_inner_volume = 0
                    elif '????' in key_lower:
                        try:
                            current_outer_volume = float(str(value).replace(',', ''))
                        except (ValueError, TypeError):
                            current_outer_volume = 0
            
            # ????????????
            date_data_mapping[current_date_formatted] = {
                'main_flow': current_main_flow,
                'main_buy': current_main_buy,
                'inner_volume': current_inner_volume,
                'outer_volume': current_outer_volume
            }
        
        # ??????????????????????????????????????
        if not use_simulation and os.path.exists(base_dir):
            try:
                # ?????????????????
                all_stock_files = glob.glob(os.path.join(base_dir, "???????*.xls")) + glob.glob(os.path.join(base_dir, "???????*.txt"))
                
                # ???????????????????????????????????????????
                if not all_stock_files:
                    print("?????????????????????????????????????...")
                    # ???????'????A??*'???
                    all_stock_files = glob.glob(os.path.join(base_dir, "????A??*.xls")) + glob.glob(os.path.join(base_dir, "????A??*.txt"))
                    # ???????'??????*'???
                    if not all_stock_files:
                        all_stock_files = glob.glob(os.path.join(base_dir, "??????*.xls")) + glob.glob(os.path.join(base_dir, "??????*.txt"))
                    # ??????????????????
                    if not all_stock_files:
                        all_stock_files = glob.glob(os.path.join(base_dir, "??? A??*.xls")) + glob.glob(os.path.join(base_dir, "??? A??*.txt"))
                        all_stock_files += glob.glob(os.path.join(base_dir, "???? A??*.xls")) + glob.glob(os.path.join(base_dir, "???? A??*.txt"))
                
                # ??????????????????????????????????
                def extract_date(filename):
                    # ??????????????????
                    # 1. ????????"???????20250101.xls"
                    date_match = re.search(r'???????(\d{8})', os.path.basename(filename))
                    if date_match:
                        date_str = date_match.group(1)
                        return datetime.strptime(date_str, '%Y%m%d')
                    
                    # 2. ????A??????"????A??20250101.xls"
                    date_match = re.search(r'????A??(\d{8})', os.path.basename(filename))
                    if date_match:
                        date_str = date_match.group(1)
                        return datetime.strptime(date_str, '%Y%m%d')
                        
                    # 3. ???????????"??????20250101.xls"
                    date_match = re.search(r'??????(\d{8})', os.path.basename(filename))
                    if date_match:
                        date_str = date_match.group(1)
                        return datetime.strptime(date_str, '%Y%m%d')
                    
                    # 4. ??????????"??? A??20250101.xls" ?? "???? A??20250101.xls"
                    date_match = re.search(r'(???|????)\s+A??(\d{8})', os.path.basename(filename))
                    if date_match:
                        date_str = date_match.group(2)
                        return datetime.strptime(date_str, '%Y%m%d')
                    
                    # 5. ?????????????????????????8?????????????
                    date_match = re.search(r'(\d{8})', os.path.basename(filename))
                    if date_match:
                        date_str = date_match.group(1)
                        try:
                            return datetime.strptime(date_str, '%Y%m%d')
                        except ValueError:
                            pass
                            
                    return datetime.min
                
                sorted_files = sorted(all_stock_files, key=extract_date, reverse=True)
                
                # ????????????????????????
                filtered_files = []
                for file_path in sorted_files:
                    file_date_match = re.search(r'???????(\d{8})', os.path.basename(file_path))
                    if file_date_match and file_date_match.group(1) != current_date:
                        filtered_files.append(file_path)
                
                # ???????4???????????????(???????????????)
                recent_files = filtered_files[:4]
                
                if not recent_files:
                    print(f"????: ?????????????????????????????????????")
                    print(f"????????: {base_dir}")
                    print(f"???????: {current_date}")
                    print(f"????????????: {all_stock_files}")
                    print(f"?????????: {filtered_files}")
                    use_simulation = True
                else:
                    # ?????????????????t-1??t-4??????
                    for file_path in recent_files:
                        file_date = extract_date(file_path)
                        date_str = file_date.strftime('%Y-%m-%d')
                        
                        # ?????????????????Excel???
                        is_real_excel = False
                        if file_path.lower().endswith('.xls'):
                            try:
                                with open(file_path, 'rb') as f:
                                    header = f.read(8)
                                    # ?????????????Excel??????
                                    is_real_excel = header.startswith(b'\xD0\xCF\x11\xE0') or header.startswith(b'PK\x03\x04')
                            except Exception:
                                is_real_excel = False
                        
                        try:
                            stock_row = None
                            
                            if is_real_excel:
                                try:
                                    if file_path.lower().endswith('.xls'):
                                        df = pd.read_excel(file_path, engine='xlrd')
                                    else:
                                        df = pd.read_excel(file_path, engine='openpyxl')
                                        
                                    # ??????????
                                    for _, row in df.iterrows():
                                        if '????' in df.columns and pd.notna(row['????']):
                                            row_code = clean_stock_code(row['????'])
                                            if row_code == stock_code:
                                                stock_row = row.to_dict()
                                                break
                                except Exception as e:
                                    print(f"??Excel???????????????: {e}")
                            else:
                                # ??????????Excel??????????????????
                                try:
                                    with open(file_path, 'r', encoding='gbk', errors='ignore') as f:
                                        lines = f.readlines()
                                    
                                    if not lines:
                                        print(f"??? {file_path} ??????????")
                                        continue
                                    
                                    # ???????
                                    file_header = [col.strip() for col in lines[0].strip().split('\t') if col.strip()]
                                    if not file_header:
                                        print(f"??? {file_path} ??????????")
                                        continue
                                        
                                    # ??????
                                    for line in lines[1:]:
                                        if not line.strip():
                                            continue
                                        
                                        values = [val.strip() for val in line.strip().split('\t')]
                                        if len(values) >= len(file_header):
                                            row_dict = {file_header[i]: values[i] for i in range(len(file_header))}
                                            if '????' in row_dict and row_dict['????']:
                                                row_code = clean_stock_code(row_dict['????'])
                                                if row_code == stock_code:
                                                    stock_row = row_dict
                                                    break
                                except Exception as e:
                                    print(f"????????????????????: {e}")
                            
                            # ??????????????????????????
                            if stock_row:
                                main_flow = None
                                main_buy = None
                                inner_volume = None
                                outer_volume = None
                                
                                for key, value in stock_row.items():
                                    if value and str(value).strip() and not pd.isna(value):
                                        key_lower = key.lower()
                                        if '????????' in key_lower:
                                            try:
                                                main_flow = float(str(value).replace(',', ''))
                                            except (ValueError, TypeError):
                                                main_flow = 0
                                        elif '?????' in key_lower:
                                            try:
                                                main_buy = float(str(value).replace(',', ''))
                                            except (ValueError, TypeError):
                                                main_buy = 0
                                        elif '????' in key_lower and '????' not in key_lower:
                                            try:
                                                inner_volume = float(str(value).replace(',', ''))
                                            except (ValueError, TypeError):
                                                inner_volume = 0
                                        elif '????' in key_lower:
                                            try:
                                                outer_volume = float(str(value).replace(',', ''))
                                            except (ValueError, TypeError):
                                                outer_volume = 0
                                
                                # ??????????????
                                date_data_mapping[date_str] = {
                                    'main_flow': main_flow,
                                    'main_buy': main_buy,
                                    'inner_volume': inner_volume,
                                    'outer_volume': outer_volume
                                }
                        except Exception as e:
                            print(f"?????????? {file_path} ?????: {e}")
            except Exception as e:
                print(f"?????????????????: {e}")
                use_simulation = True
        else:
            use_simulation = True
        
        # ????????????????????????????????
        if use_simulation:
            print("?????????????...")
            # ????????????????t-1??t-4???
            for i in range(1, 5):
                date = (current_date_obj - timedelta(days=i)).strftime('%Y-%m-%d')
                
                # ??????????????????????????
                if current_main_flow is not None:
                    sim_main_flow = current_main_flow * (0.85 + 0.3 * random.random())  # ???????85%-115%?????
                else:
                    sim_main_flow = 5000000 + 5000000 * random.random()  # ??????
                    
                if current_main_buy is not None:
                    sim_main_buy = current_main_buy * (0.85 + 0.3 * random.random())
                else:
                    sim_main_buy = 3000000 + 4000000 * random.random()
                    
                if current_inner_volume is not None:
                    sim_inner_volume = current_inner_volume * (0.85 + 0.3 * random.random())
                else:
                    sim_inner_volume = 10000000 + 5000000 * random.random()
                    
                if current_outer_volume is not None:
                    sim_outer_volume = current_outer_volume * (0.85 + 0.3 * random.random())
                else:
                    sim_outer_volume = 10000000 + 5000000 * random.random()
                
                # ???????????
                date_data_mapping[date] = {
                    'main_flow': sim_main_flow,
                    'main_buy': sim_main_buy,
                    'inner_volume': sim_inner_volume,
                    'outer_volume': sim_outer_volume
                }
        
        # ????????
        result = "????????????\n"
        if use_simulation:
            result += "???????????????????????????????????????????????????????\n"
            
        # ??????
        result += "????\t\t????????\t?????\t????\t\t????\t\t???????\n"
        
        # ?????????????????????
        all_dates = sorted(date_data_mapping.keys(), reverse=True)
        
        # ????????????????
        for date_str in all_dates:
            data = date_data_mapping[date_str]
            # ???data??????????????'str'???????'get'????????
            if not isinstance(data, dict):
                print(f"????????? {date_str} ????????????????: {type(data)}")
                continue
                
            main_flow = data.get('main_flow')
            main_buy = data.get('main_buy')
            inner_volume = data.get('inner_volume')
            outer_volume = data.get('outer_volume')
            
            # ???????????
            inner_outer_ratio = 0
            if outer_volume and outer_volume > 0:
                inner_outer_ratio = inner_volume / outer_volume if inner_volume is not None else 0
            
            # ?????????
            main_flow_str = f"{main_flow/10000:.2f}??" if main_flow is not None else "N/A"
            main_buy_str = f"{main_buy/10000:.2f}??" if main_buy is not None else "N/A"
            inner_volume_str = f"{inner_volume/10000:.2f}??" if inner_volume is not None else "N/A"
            outer_volume_str = f"{outer_volume/10000:.2f}??" if outer_volume is not None else "N/A"
            ratio_str = f"{inner_outer_ratio:.2f}" if inner_outer_ratio > 0 else "N/A"
            
            # ????????????????
            result += f"{date_str}\t{main_flow_str}\t{main_buy_str}\t{inner_volume_str}\t{outer_volume_str}\t{ratio_str}\n"
        
        # ??????????????????????????????
        def is_positive_number(value):
            """????????????"""
            return isinstance(value, (int, float)) and value > 0
        
        positive_main_flow = sum(1 for date in all_dates 
                               if isinstance(date_data_mapping[date], dict) 
                               and is_positive_number(date_data_mapping[date].get('main_flow', 0)))
        positive_main_buy = sum(1 for date in all_dates 
                              if isinstance(date_data_mapping[date], dict) 
                              and is_positive_number(date_data_mapping[date].get('main_buy', 0)))
        
        result += f"\n?????????????????: {positive_main_flow}/{len(all_dates)}"
        result += f"\n??????????????: {positive_main_buy}/{len(all_dates)}"
        
        return result
    except Exception as e:
        print(f"?????????????????: {e}")
        traceback.print_exc()
        return "?????????????????"

def _robust_extract_section(text: str, start_keyword: str) -> str:
    """More robustly extracts a section from a text block.
    A section starts with a given keyword and ends right before the next
    numbered heading (e.g., '2. Some Title') or at the end of the text.
    """
    import re
    lines = text.splitlines()
    found_section_lines = []
    in_section = False

    for line in lines:
        # A section header is a line that starts with digits, a dot, and a space.
        is_header = re.match(r'^\s*\d+\.\s', line)

        if is_header:
            if start_keyword in line:
                # Found the start of the target section.
                in_section = True
                found_section_lines.append(line)
            elif in_section:
                # Found the header of the *next* section, so we stop.
                break
        elif in_section:
            # This line is part of the target section.
            found_section_lines.append(line)
            
    return "\n".join(found_section_lines).strip()

def enhance_tech_info(tech_info, stock, historical_data=None):
    """
    ??????????????????????????????Z????????????????????????????
    
    Args:
        tech_info: ??stock_tech.py??????????????
        stock: ????????????
        historical_data: ???????????????????
    
    Returns:
        str: ???????????????
    """
    if not tech_info:
        return tech_info
    
    # ????K?????????????????????
    section_start = tech_info.find("1. ???20????????K???")
    if section_start == -1:
        # ?????????????
        section_start = tech_info.find("1. ???10?????????K??/?????????")
        if section_start == -1:
            # ?????????????? (Zstock_tech_qmt.py???)
            section_start = tech_info.find("1. ???5?????????K??/?????????")
            if section_start == -1:
                # ????????????????????????????
                return tech_info
    
    section_end = tech_info.find("\n\n2.", section_start)
    if section_end == -1:
        # ???????????????????????b????
        section_end = len(tech_info)
    
    # ???????????????
    kline_section = tech_info[section_start:section_end]
    
    # ???????????????????????Z
    current_volume_ratio = None
    current_turnover_z = None
    
    # ???stock?????????
    if stock and isinstance(stock, dict):
        for key, value in stock.items():
            if value and str(value).strip() and not pd.isna(value):
                key_lower = key.lower()
                if '????' in key_lower:
                    try:
                        current_volume_ratio = float(str(value).replace('%', ''))
                    except (ValueError, TypeError):
                        pass
                elif '????z' in key_lower.replace(' ', ''):
                    try:
                        # ?????????%????????
                        current_turnover_z = str(value)
                        if not current_turnover_z.endswith('%') and not current_turnover_z.strip() == '0':
                            current_turnover_z += '%'
                    except (ValueError, TypeError):
                        pass
    
    # ??????????????????????Z???
    historical_volume_ratio = {}
    historical_turnover_z = {}
    
    if historical_data:
        # ??????????????????????????????????Z
        data_blocks = re.findall(r'??(\d{4}-\d{2}-\d{2})?????????(.*?)(?=??|$)', historical_data, re.DOTALL)
        for date_str, content in data_blocks:
            # ???????
            volume_ratio_match = re.search(r'????[:??]\s*([0-9.]+)', content)
            if volume_ratio_match:
                try:
                    historical_volume_ratio[date_str] = float(volume_ratio_match.group(1))
                except (ValueError, TypeError):
                    pass
            
            # ???????Z
            turnover_z_match = re.search(r'????Z[:??]\s*([0-9.]+%?)', content)
            if turnover_z_match:
                turnover_z = turnover_z_match.group(1)
                if not turnover_z.endswith('%') and not turnover_z.strip() == '0':
                    turnover_z += '%'
                historical_turnover_z[date_str] = turnover_z
    
    # ???K??????????????????Z
    lines = kline_section.split('\n')
    modified_lines = []
    
    # ????????????????
    if lines:
        modified_lines.append(lines[0])
    
    # ??????????
    for i in range(1, len(lines)):
        line = lines[i]
        # ???????????????????
        # 1. "   2025-05-16: ????=29586.57, ..." (??????)
        # 2. "2025-05-16: ????=29586.57, ..." (????????)
        date_match = re.search(r'\s*(\d{4}-\d{2}-\d{2}):', line)
        if date_match:
            date_str = date_match.group(1)
            
            # ???????????????
            is_current_date = False
            try:
                line_date = datetime.strptime(date_str, '%Y-%m-%d')
                current_date = datetime.now()
                is_current_date = (line_date.date() == current_date.date())
            except:
                pass
            
            # ???????????????????Z???
            volume_ratio = None
            turnover_z = None
            
            if is_current_date and current_volume_ratio is not None:
                volume_ratio = current_volume_ratio
            elif date_str in historical_volume_ratio:
                volume_ratio = historical_volume_ratio[date_str]
            
            if is_current_date and current_turnover_z is not None:
                turnover_z = current_turnover_z
            elif date_str in historical_turnover_z:
                turnover_z = historical_turnover_z[date_str]
            
            # ????????????Z?????
            if volume_ratio is not None or turnover_z is not None:
                # ?????????????????
                clean_line = line.rstrip()
                
                # ????????
                if volume_ratio is not None:
                    clean_line += f", ????={volume_ratio:.2f}"
                
                # ???????Z
                if turnover_z is not None:
                    clean_line += f", ????Z={turnover_z}"
                
                # ??I???
                modified_lines.append(clean_line)
            else:
                # ??????????????????????Z?????????????
                modified_lines.append(line)
        else:
            # ???????????????????
            modified_lines.append(line)
    
    # ??I????????????K?????
    modified_kline_section = '\n'.join(modified_lines)
    enhanced_tech_info = tech_info[:section_start] + modified_kline_section + tech_info[section_end:]
    
    return enhanced_tech_info

def format_stock_info(stock):
    """?????????????????Wstock_compare.py???????????????????"""
    # ?????????????????
    return ""

def validate_model_response(response):
    """????????????????????"""
    if not response or not isinstance(response, str):
        return False
    
    # ?????????????????????????????????????
    item_patterns = [
        r'0\.\s*????????????',
        r'1\.\s*????????????',
        r'2\.\s*????????????',
        r'3\.\s*?????????',
        r'4\.\s*?????????',
        r'5\.\s*????????????????',
        r'6\.\s*?????????'
    ]
    
    # ???????????????????
    valid_items = 0
    for pattern in item_patterns:
        if re.search(pattern, response, re.IGNORECASE):
            valid_items += 1
    
    # ?????????4??????????????????
    return valid_items >= 4

def calculate_fundamental_score_detailed(stock_code):
    """
    ??????????????????????????

    ???????
    a. PE??????????50%
    b. ROE???????15%
    c. ?????????????????????????????????50%?????????/???????????????????????

    ????????????????2????????????1???????????

    Args:
        stock_code: 6?????????

    Returns:
        tuple: (????, ???????, ?????????)
    """
    try:
        api_token = "d7c12fcb-2961-41b4-b3ef-1e432b4f2a1c"

        # ???PE????????
        pe_result = ZJBM.get_pe_percentile(stock_code, api_token)

        # ???ROE????
        roe_result = ZJBM.get_roe_data(stock_code, api_token)

        # ?????????????????
        quarterly_result = ZJBM.get_quarterly_growth_rates(stock_code, api_token)

        # ??????????????
        condition_a = False  # PE???? <= 50%
        condition_b = False  # ROE >= 15%
        condition_c = False  # ?????????????????????????? > 50% ?????

        # ?????????
        details = {
            'pe_data': pe_result,
            'roe_data': roe_result,
            'quarterly_data': quarterly_result,
            'condition_a': False,
            'condition_b': False,
            'condition_c': False,
            'conditions_met': 0
        }

        decision_process = f"????????? - ???????: {stock_code}\n"

        # ???????a: PE???? <= 50%
        if 'error' not in pe_result and 'percentile' in pe_result:
            pe_percentile = pe_result['percentile']
            if pe_percentile <= 50:
                condition_a = True
                decision_process += f"- ????a????: PE????({pe_percentile:.2f}%) <= 50%\n"
            else:
                decision_process += f"- ????a??????: PE????({pe_percentile:.2f}%) > 50%\n"
        else:
            decision_process += f"- ????a???????: PE?????????\n"

        # ???????b: ROE >= 15%
        if 'error' not in roe_result and 'roe' in roe_result:
            roe_value = roe_result['roe']
            if roe_value >= 15:
                condition_b = True
                decision_process += f"- ????b????: ROE({roe_value:.2f}%) >= 15%\n"
            else:
                decision_process += f"- ????b??????: ROE({roe_value:.2f}%) < 15%\n"
        else:
            decision_process += f"- ????b???????: ROE?????????\n"

        # ???????c: ?????????????????????????? > 50% ?????
        if 'error' not in quarterly_result and 'quarterly_data' in quarterly_result:
            quarterly_data = quarterly_result['quarterly_data']
            if quarterly_data and len(quarterly_data) >= 2:  # ???????2???????????????????
                latest_quarter = quarterly_data[0]  # ???????
                previous_quarter = quarterly_data[1]  # ?????????

                latest_revenue_growth = latest_quarter.get('revenue_growth')
                latest_profit_growth = latest_quarter.get('profit_growth')
                previous_revenue_growth = previous_quarter.get('revenue_growth')
                previous_profit_growth = previous_quarter.get('profit_growth')

                if (latest_revenue_growth is not None and latest_profit_growth is not None):
                    # ??????????????????50%
                    growth_sum = latest_revenue_growth + latest_profit_growth
                    growth_sum_satisfied = growth_sum > 50

                    # ??????????????
                    revenue_turnaround = False
                    profit_turnaround = False

                    if (previous_revenue_growth is not None and
                        previous_revenue_growth < 0 and latest_revenue_growth > 0):
                        revenue_turnaround = True

                    if (previous_profit_growth is not None and
                        previous_profit_growth < 0 and latest_profit_growth > 0):
                        profit_turnaround = True

                    turnaround_satisfied = revenue_turnaround or profit_turnaround

                    # ???????????????
                    if growth_sum_satisfied or turnaround_satisfied:
                        condition_c = True
                        if growth_sum_satisfied and turnaround_satisfied:
                            decision_process += f"- ????c????: ?????????({growth_sum:.2f}% > 50%) ?? "
                            if revenue_turnaround and profit_turnaround:
                                decision_process += f"?????????????????\n"
                            elif revenue_turnaround:
                                decision_process += f"?????????(??{previous_revenue_growth:.2f}%??{latest_revenue_growth:.2f}%)\n"
                            else:
                                decision_process += f"?????????????(??{previous_profit_growth:.2f}%??{latest_profit_growth:.2f}%)\n"
                        elif growth_sum_satisfied:
                            decision_process += f"- ????c????: ??????????????({latest_revenue_growth:.2f}%) + ???????????({latest_profit_growth:.2f}%) = {growth_sum:.2f}% > 50%\n"
                        else:  # turnaround_satisfied
                            if revenue_turnaround and profit_turnaround:
                                decision_process += f"- ????c????: ?????????????????(???:{previous_revenue_growth:.2f}%??{latest_revenue_growth:.2f}%, ???????:{previous_profit_growth:.2f}%??{latest_profit_growth:.2f}%)\n"
                            elif revenue_turnaround:
                                decision_process += f"- ????c????: ?????????(??{previous_revenue_growth:.2f}%??{latest_revenue_growth:.2f}%)\n"
                            else:
                                decision_process += f"- ????c????: ?????????????(??{previous_profit_growth:.2f}%??{latest_profit_growth:.2f}%)\n"
                    else:
                        decision_process += f"- ????c??????: ?????????({growth_sum:.2f}% <= 50%) ?? ????????\n"
                        if previous_revenue_growth is not None and previous_profit_growth is not None:
                            decision_process += f"  (?????: ???{previous_revenue_growth:.2f}%, ???????{previous_profit_growth:.2f}%; ??????: ???{latest_revenue_growth:.2f}%, ???????{latest_profit_growth:.2f}%)\n"
                else:
                    decision_process += f"- ????c???????: ??????????????????????\n"
            elif quarterly_data and len(quarterly_data) == 1:
                # ?????????????????????????????????????????
                latest_quarter = quarterly_data[0]
                latest_revenue_growth = latest_quarter.get('revenue_growth')
                latest_profit_growth = latest_quarter.get('profit_growth')

                if (latest_revenue_growth is not None and latest_profit_growth is not None):
                    growth_sum = latest_revenue_growth + latest_profit_growth
                    if growth_sum > 50:
                        condition_c = True
                        decision_process += f"- ????c????: ??????????????({latest_revenue_growth:.2f}%) + ???????????({latest_profit_growth:.2f}%) = {growth_sum:.2f}% > 50%\n"
                    else:
                        decision_process += f"- ????c??????: ?????????({growth_sum:.2f}% <= 50%) ?? ??????????(????1??????????)\n"
                else:
                    decision_process += f"- ????c???????: ??????????????????????\n"
            else:
                decision_process += f"- ????c???????: ?????????\n"
        else:
            decision_process += f"- ????c???????: ?????????????\n"

        # ??????????
        details['condition_a'] = condition_a
        details['condition_b'] = condition_b
        details['condition_c'] = condition_c

        # ?????????????????
        conditions_met = sum([condition_a, condition_b, condition_c])
        details['conditions_met'] = conditions_met

        # ?????????????
        score = 1 if conditions_met >= 2 else 0

        decision_process += f"\n????????????: {conditions_met}/3\n"
        decision_process += f"???????????????????{'??2' if conditions_met >= 2 else '<2'}??????????{score}???"

        return score, decision_process, details

    except Exception as e:
        error_process = f"?????????????????????{str(e)}"
        return 0, error_process, {'error': str(e)}

def analyze_stock_locally(stock_index, stock, stock_tech_info, output_queue, output_file_lock, output_file, original_headers, model_log_file, industry_tech_info=None, stock_to_industry=None, stock_resonance_info=None):
    """???????????????????????"""
    
    # ???stock??????????
    if not isinstance(stock, dict):
        print(f"????: stock???????????????????? {type(stock)}: {stock}")
        return
    
    stock_code = stock.get('????', '???')
    stock_name = stock.get('????', '???')
    
    try:
        print(f"???????????{stock_index+1}????: {stock_code}({stock_name})")
        
        # ?????????????
        tech_info = stock_tech_info.get(stock_code, "")
        if not tech_info:
            print(f"????: ??? {stock_code} ?????????????")
            
        # ??????????????
        history_info = get_historical_data(stock_code, original_headers, current_stock=stock, use_simulation=False)
        
        # ?????????
        industry_info = ""
        industry_name = ""
        full_industry_name = ""
        
        if stock_to_industry and stock_code in stock_to_industry:
            full_industry_name = stock_to_industry[stock_code]
            industry_name = full_industry_name.split('-')[-1] if '-' in full_industry_name else full_industry_name
            
            if industry_tech_info and industry_name in industry_tech_info:
                industry_info = industry_tech_info[industry_name]
        
        # ?????????????
        resonance_info = ""
        resonance_count = 0
        if stock_resonance_info and stock_code in stock_resonance_info:
            resonance_data = stock_resonance_info[stock_code]
            resonance_count = resonance_data.get('count', 0)
            industry = resonance_data.get('industry', full_industry_name)
            resonance_info = f"?? {industry} ??????? {resonance_count} ???????????????????"
        
        # === ?????????????????????????????? ===
        raw_market_data = {
            'basic_info': {
                'code': stock_code,
                'name': stock_name,
                'industry_full': full_industry_name,
                'industry_short': industry_name
            },
            'current_stock_data': dict(stock),  # ??????????????????
            'fund_flow_data': history_info,  # ????????????
            'technical_indicators': tech_info,  # ???????????
            'industry_technical': industry_info,  # ??????????
            'resonance_data': {
                'info': resonance_info,
                'count': resonance_count,
                'industry': resonance_data.get('industry', '') if stock_resonance_info and stock_code in stock_resonance_info else ''
            }
        }
        
        # ???????????????????Prompt??????
        prompt_data = f"""?????????
{stock_code}
??????????
{stock_name}
"""
        
        # ????????????
        if history_info:
            prompt_data += f"????????????\n{history_info}\n"
        
        # ?????????????
        prompt_data += f"?????????\n{tech_info}\n\n"
        
        # ?????????????????
        if industry_info:
            prompt_data += f"????????????????\n{industry_info}\n"
            
            # ??????????????
            if resonance_info:
                prompt_data += f"\n??????????????\n{resonance_info}\n"
        
        # ??????????????????
        item_scores = {}
        items = ["?????????????", "????????????", "?????????", "?????????", "????????????????", "??????????", "?????????"]
        
        # ??????????
        detailed_log = f"""
=== ??? {stock_code}({stock_name}) ????????????? ===

?????????????????
??????????:
  ????: {stock_code}
  ????: {stock_name}
  ???????(????): {full_industry_name}
  ???????(???): {industry_name}

??????????(???????):"""
        
        # ????????????????????????
        for key, value in stock.items():
            detailed_log += f"\n  {key}: {value}"
        
        detailed_log += f"""

????????????:
{history_info if history_info else '???????????'}

???????????????:
{tech_info if tech_info else '????????????'}

??????????????:
{industry_info if industry_info else '????????????????'}

???????????:
  ???????: {resonance_info if resonance_info else '????????'}
  ????????: {resonance_count}
  ???????: {resonance_data.get('industry', '') if stock_resonance_info and stock_code in stock_resonance_info else ''}

???????? - ?????????Prompt??
{prompt_data}

??????????????????
"""
        
        # 0. ????????????? (?????????????????????????????)
        score_0, decision_process_0, _ = calculate_multi_period_resonance_score_detailed(tech_info)
        item_scores["?????????????"] = {"score": score_0, "reason": decision_process_0}
        detailed_log += f"0. ??????????????{score_0}??\n   ????????{decision_process_0}\n\n"
        
        # 1. ???????????? (?????)
        score_1, decision_process_1, _ = calculate_trend_score_detailed(tech_info, "????", 10, 5)  # ?????
        item_scores["????????????"] = {"score": score_1, "reason": decision_process_1}
        detailed_log += f"1. ?????????????{score_1}??\n   ????????{decision_process_1}\n\n"
        
        # 2. ?????????
        score_2, decision_process_2, _ = calculate_fund_score_detailed(history_info, stock_code, stock_name)
        item_scores["?????????"] = {"score": score_2, "reason": decision_process_2}
        detailed_log += f"2. ??????????{score_2}??\n   ????????{decision_process_2}\n\n"
        
        # 3. ?????????
        score_3, decision_process_3, _ = calculate_news_score_detailed(tech_info, is_local_calculation=True)
        item_scores["?????????"] = {"score": score_3, "reason": decision_process_3}
        detailed_log += f"3. ??????????{score_3}??\n   ????????{decision_process_3}\n\n"
        
        # 4. ???????????????? (???????????????????????????)
        score_4, decision_process_4, _ = calculate_industry_trend_resonance_score_detailed(industry_info, resonance_count)
        item_scores["????????????????"] = {"score": score_4, "reason": decision_process_4}
        detailed_log += f"4. ?????????????????{score_4}??\n   ????????{decision_process_4}\n\n"

        # 5. ??????????
        score_5, decision_process_5, details_5 = calculate_fundamental_score_detailed(stock_code)
        item_scores["??????????"] = {"score": score_5, "reason": decision_process_5}
        detailed_log += f"5. ???????????{score_5}??\n   ????????{decision_process_5}\n\n"

        # ?????????????????raw_market_data??
        raw_market_data['fundamental_data'] = details_5

        # 6. ?????????
        score_6, decision_process_6, _ = calculate_profit_loss_ratio_score_detailed(tech_info)
        item_scores["?????????"] = {"score": score_6, "reason": decision_process_6}
        detailed_log += f"6. ??????????{score_6}??\n   ????????{decision_process_6}\n\n"
        
        # ???????
        total_score = sum(item_scores[item]["score"] for item in item_scores)
        
        # ??????????????
        detailed_log += f"???????????\n????{total_score}??\n"
        detailed_log += "=" * 80 + "\n\n"
        
        # ?????????????
        rationale = f"????????? - {stock_code}({stock_name}):\n\n"
        for i, item in enumerate(items):
            score = item_scores[item]["score"]
            decision_process = item_scores[item]["reason"]
            rationale += f"{i}. {item}??{score}???{decision_process}\n\n"
        
        rationale += f"???????{total_score}??"
        
        # ??????????? - ???????????????????????model_log_file???None??
        if model_log_file:
            with output_file_lock:
                with open(model_log_file, "a", encoding="utf-8") as logfile:
                    logfile.write(f"="*50 + "\n")
                    logfile.write(f"??????? - ???: {stock_code}({stock_name})\n")
                    logfile.write(f"="*50 + "\n")
                    
                    # === ????????????????????????? ===
                    logfile.write(f"????????????????????\n")
                    logfile.write(f"??????????:\n")
                    logfile.write(f"  ????: {raw_market_data['basic_info']['code']}\n")
                    logfile.write(f"  ????: {raw_market_data['basic_info']['name']}\n")
                    logfile.write(f"  ???????(????): {raw_market_data['basic_info']['industry_full']}\n")
                    logfile.write(f"  ???????(???): {raw_market_data['basic_info']['industry_short']}\n")
                    
                    logfile.write(f"\n??????????(???????):\n")
                    for key, value in raw_market_data['current_stock_data'].items():
                        logfile.write(f"  {key}: {value}\n")
                    
                    logfile.write(f"\n????????????:\n")
                    if raw_market_data['fund_flow_data']:
                        logfile.write(f"{raw_market_data['fund_flow_data']}\n")
                    else:
                        logfile.write("???????????\n")
                    
                    logfile.write(f"\n???????????????:\n")
                    if raw_market_data['technical_indicators']:
                        logfile.write(f"{raw_market_data['technical_indicators']}\n")
                    else:
                        logfile.write("????????????\n")
                    
                    logfile.write(f"\n??????????????:\n")
                    if raw_market_data['industry_technical']:
                        logfile.write(f"{raw_market_data['industry_technical']}\n")
                    else:
                        logfile.write("????????????????\n")
                    
                    logfile.write(f"\n???????????:\n")
                    logfile.write(f"  ???????: {raw_market_data['resonance_data']['info']}\n")
                    logfile.write(f"  ????????: {raw_market_data['resonance_data']['count']}\n")
                    logfile.write(f"  ???????: {raw_market_data['resonance_data']['industry']}\n")

                    # ?????????????????
                    if 'fundamental_data' in raw_market_data:
                        logfile.write(f"\n??????????:\n")
                        fundamental_data = raw_market_data['fundamental_data']

                        # PE???????
                        if 'pe_data' in fundamental_data:
                            pe_data = fundamental_data['pe_data']
                            if 'error' not in pe_data:
                                logfile.write(f"a. ???????????\n")
                                logfile.write(f"- ??????PE: {pe_data.get('current_pe', 'N/A')}\n")
                                logfile.write(f"- PE-TTM??3??????: {pe_data.get('percentile', 'N/A')}%\n")
                                logfile.write(f"- ????????????: {pe_data.get('latest_date', 'N/A')}\n")
                            else:
                                logfile.write(f"a. ???????????\n")
                                logfile.write(f"??????: {pe_data.get('error', '???????')}\n")

                        # ROE???????
                        if 'roe_data' in fundamental_data:
                            roe_data = fundamental_data['roe_data']
                            if 'error' not in roe_data:
                                logfile.write(f"\nc. ROE???????\n")
                                logfile.write(f"- ROE??{roe_data.get('roe', 'N/A')}%\n")
                                logfile.write(f"- ???????{roe_data.get('calculation_date', 'N/A')}\n")
                            else:
                                logfile.write(f"\nc. ROE???????\n")
                                logfile.write(f"??????: ROE?????????\n")

                        # ????????????????
                        if 'quarterly_data' in fundamental_data:
                            quarterly_result = fundamental_data['quarterly_data']
                            if 'error' not in quarterly_result and 'quarterly_data' in quarterly_result:
                                logfile.write(f"\nb. ????????????????\n")
                                logfile.write(f"\n?????????????????\n")
                                for quarter in quarterly_result['quarterly_data']:
                                    quarter_str = quarter['quarter']
                                    revenue_growth = quarter['revenue_growth']
                                    revenue_str = f"{revenue_growth:+.2f}%" if revenue_growth is not None else "N/A"
                                    logfile.write(f"- {quarter_str}??{revenue_str}\n")

                                logfile.write(f"\n??????????????????\n")
                                for quarter in quarterly_result['quarterly_data']:
                                    quarter_str = quarter['quarter']
                                    profit_growth = quarter['profit_growth']
                                    profit_str = f"{profit_growth:+.2f}%" if profit_growth is not None else "N/A"
                                    logfile.write(f"- {quarter_str}??{profit_str}\n")
                            else:
                                logfile.write(f"\nb. ????????????????\n")
                                logfile.write(f"??????: ?????????????\n")

                        logfile.write(f"\n?????????????????????:\n")
                        logfile.write(f"- ????a(PE??????50%): {'????' if fundamental_data.get('condition_a', False) else '??????'}\n")
                        logfile.write(f"- ????b(ROE??15%): {'????' if fundamental_data.get('condition_b', False) else '??????'}\n")
                        logfile.write(f"- ????c(?????????>50%?????): {'????' if fundamental_data.get('condition_c', False) else '??????'}\n")
                        logfile.write(f"- ????????????: {fundamental_data.get('conditions_met', 0)}/3\n")
                    else:
                        logfile.write(f"\n??????????: ?????\n")
                    
                    logfile.write(f"\n??????????????\n")
                    logfile.write(f"{prompt_data}\n")
                    logfile.write(f"-"*50 + "\n")
                    
                    logfile.write(f"??????:\n{rationale}\n")
                    logfile.write(f"="*50 + "\n\n")
            
            with open(output_file, "a", encoding="utf-8") as fout:
                fout.write(f"???????: {stock_code}({stock_name})\n")
                fout.write(f"="*60 + "\n")
                
                # === ?????????????????????????????????????????????? ===
                fout.write(f"?????????????????\n")
                fout.write(f"??????????:\n")
                fout.write(f"  ????: {raw_market_data['basic_info']['code']}\n")
                fout.write(f"  ????: {raw_market_data['basic_info']['name']}\n")
                fout.write(f"  ???????(????): {raw_market_data['basic_info']['industry_full']}\n")
                fout.write(f"  ???????(???): {raw_market_data['basic_info']['industry_short']}\n")
                
                fout.write(f"\n??????????(???????):\n")
                for key, value in raw_market_data['current_stock_data'].items():
                    fout.write(f"  {key}: {value}\n")
                
                fout.write(f"\n????????????:\n")
                if raw_market_data['fund_flow_data']:
                    fout.write(f"{raw_market_data['fund_flow_data']}\n")
                else:
                    fout.write("???????????\n")
                
                fout.write(f"\n???????????????:\n")
                if raw_market_data['technical_indicators']:
                    # ????????????????????????
                    tech_data = raw_market_data['technical_indicators']
                    if len(tech_data) > 5000:
                        fout.write(f"{tech_data[:5000]}...\n[????????????????????????????????: {model_log_file}]\n")
                    else:
                        fout.write(f"{tech_data}\n")
                else:
                    fout.write("????????????\n")
                
                fout.write(f"\n??????????????:\n")
                if raw_market_data['industry_technical']:
                    # ????????????????
                    industry_data = raw_market_data['industry_technical']
                    if len(industry_data) > 3000:
                        fout.write(f"{industry_data[:3000]}...\n[???????????????????????????????????: {model_log_file}]\n")
                    else:
                        fout.write(f"{industry_data}\n")
                else:
                    fout.write("????????????????\n")
                
                fout.write(f"\n???????????:\n")
                fout.write(f"  ???????: {raw_market_data['resonance_data']['info']}\n")
                fout.write(f"  ????????: {raw_market_data['resonance_data']['count']}\n")
                fout.write(f"  ???????: {raw_market_data['resonance_data']['industry']}\n")

                # ????????????????
                if 'fundamental_data' in raw_market_data:
                    fout.write(f"\n??????????:\n")
                    fundamental_data = raw_market_data['fundamental_data']

                    # PE???????
                    if 'pe_data' in fundamental_data:
                        pe_data = fundamental_data['pe_data']
                        if 'error' not in pe_data:
                            fout.write(f"a. ???????????\n")
                            fout.write(f"- ??????PE: {pe_data.get('current_pe', 'N/A')}\n")
                            fout.write(f"- PE-TTM??3??????: {pe_data.get('percentile', 'N/A')}%\n")
                            fout.write(f"- ????????????: {pe_data.get('latest_date', 'N/A')}\n")
                        else:
                            fout.write(f"a. ???????????\n")
                            fout.write(f"??????: {pe_data.get('error', '???????')}\n")

                    # ROE???????
                    if 'roe_data' in fundamental_data:
                        roe_data = fundamental_data['roe_data']
                        if 'error' not in roe_data:
                            fout.write(f"\nc. ROE???????\n")
                            fout.write(f"- ROE??{roe_data.get('roe', 'N/A')}%\n")
                            fout.write(f"- ???????{roe_data.get('calculation_date', 'N/A')}\n")
                        else:
                            fout.write(f"\nc. ROE???????\n")
                            fout.write(f"??????: ROE?????????\n")

                    # ????????????????
                    if 'quarterly_data' in fundamental_data:
                        quarterly_result = fundamental_data['quarterly_data']
                        if 'error' not in quarterly_result and 'quarterly_data' in quarterly_result:
                            fout.write(f"\nb. ????????????????\n")
                            fout.write(f"\n?????????????????\n")
                            for quarter in quarterly_result['quarterly_data']:
                                quarter_str = quarter['quarter']
                                revenue_growth = quarter['revenue_growth']
                                revenue_str = f"{revenue_growth:+.2f}%" if revenue_growth is not None else "N/A"
                                fout.write(f"- {quarter_str}??{revenue_str}\n")

                            fout.write(f"\n??????????????????\n")
                            for quarter in quarterly_result['quarterly_data']:
                                quarter_str = quarter['quarter']
                                profit_growth = quarter['profit_growth']
                                profit_str = f"{profit_growth:+.2f}%" if profit_growth is not None else "N/A"
                                fout.write(f"- {quarter_str}??{profit_str}\n")
                        else:
                            fout.write(f"\nb. ????????????????\n")
                            fout.write(f"??????: ?????????????\n")

                    fout.write(f"\n?????????????????????:\n")
                    fout.write(f"- ????a(PE??????50%): {'????' if fundamental_data.get('condition_a', False) else '??????'}\n")
                    fout.write(f"- ????b(ROE??15%): {'????' if fundamental_data.get('condition_b', False) else '??????'}\n")
                    fout.write(f"- ????c(?????????>50%?????): {'????' if fundamental_data.get('condition_c', False) else '??????'}\n")
                    fout.write(f"- ????????????: {fundamental_data.get('conditions_met', 0)}/3\n")
                else:
                    fout.write(f"\n??????????: ?????\n")
                
                fout.write(f"\n??????????????\n")
                if len(prompt_data) > 2000:
                    fout.write(f"{prompt_data[:2000]}...\n[?????????????????????????????: {model_log_file}]\n")
                else:
                    fout.write(f"{prompt_data}\n")
                
                fout.write(f"\n?????????????\n")
                fout.write(f"{rationale}\n")
                fout.write(f"\n???????????{total_score}\n")
                scores_str = ", ".join([f"{item}??{item_scores[item]['score']}??" for item in item_scores])
                fout.write(f"?????????{scores_str}\n")
                fout.write(f"="*60 + "\n\n")
            
            # === ????????detailed_log_file????????????model_log_file???None??===
            if model_log_file:
                detailed_log_file = "local_calculation_detailed_log.txt"
    
                # ????????????????????????????????
                if not os.path.exists(detailed_log_file):
                    with open(detailed_log_file, "w", encoding="utf-8") as detail_file:
                        detail_file.write("????????????????? - ?????????????????????\n")
                        detail_file.write(f"???????: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                        detail_file.write("??????????????????????????????????????????????\n")
                        detail_file.write("=" * 80 + "\n\n")
    
                # ?????????????????
                with open(detailed_log_file, "a", encoding="utf-8") as detail_file:
                    detail_file.write(detailed_log)
                    detail_file.flush()  # ??????????????????
        
        # ????????????
        output_queue.put((stock_index, stock_code, stock_name, total_score, rationale, item_scores))
        print(f"??{stock_index+1}??????????????: {stock_code}({stock_name}) = {total_score}??")
        
        return total_score, rationale, item_scores
        
    except Exception as e:
        error_msg = f"??{stock_index+1}??????????????: {e}"
        print(error_msg)
        
        with output_file_lock:
            with open(output_file, "a", encoding="utf-8") as fout:
                fout.write(f"{error_msg}\n")
                fout.write(f"????????: {traceback.format_exc()}\n\n")
        
        return None, None, None

def calculate_trend_score_detailed(tech_info, trend_type, high_threshold, low_threshold):
    """?????????????????????????????????????
    
    Args:
        tech_info: ??????????
        trend_type: ???????? ("??????", "????", "????")
        high_threshold: ?????????
        low_threshold: ?????????
    
    Returns:
        tuple: (????, ????, ????????????)
    """
    try:
        # ?????????????????
        detected_signals = []
        
        # ?????????????????????????
        trend_section = ""
        tech_section = ""
        
        # ???????? ("??????", "????", "????")
        if trend_type == "??????":
            trend_section = _robust_extract_section(tech_info, "??????????????")
            tech_section = _robust_extract_section(tech_info, "????????????")
        elif trend_type == "????":
            trend_section = _robust_extract_section(tech_info, "????????????")
            tech_section = _robust_extract_section(tech_info, "??????????")
        elif trend_type == "????":
            trend_section = _robust_extract_section(tech_info, "????????????")
            tech_section = _robust_extract_section(tech_info, "??????????")

        combined_info = (trend_section + "\n" + tech_section).strip()
        
        if not combined_info.strip():
            return 0, f"?????{trend_type}????????????", "???????????"
        
        # ????????????????????????????
        tech_preview = combined_info[:200].replace('\n', ' ') + "..." if len(combined_info) > 200 else combined_info.replace('\n', ' ')
        detected_signals.append(f"[????]??????????????: {tech_preview}")
        
        # 1. ??????????
        trend_stage_score = 2  # ???????
        stage_signals = []
        
        # === ???????? : ??????(3 ??) ===
        start_signals = [
            # DIF
            "DIF???????",
            # RSI
            "RSI6???50",
            # MA5
            "MA5???????",
            # MACD ???
            "MACD???",
            # MA5/MA10 ????
            "MA5???MA10",
        ]
        # ???????????? (2??)  
        confirm_signals = [
            "DIF????????", 
            "MACD??????????",
            "RSI6?????????50????",
        ]
        # ????????????? (-1??)
        end_signals = [
            "DIF???????",
            "MACD???????????",
            "MACD????",
            "MA5???MA10",
            "RSI6???50",
            "DIF???????"
        ]
        
        # ????????????????????? - ??????????????????????
        found_start_signals = []
        found_confirm_signals = []
        found_end_signals = []

        # ??????????????????????
        combined_info_lower = combined_info.lower()

        # ?????????????????????????????????????????????
        def extract_signal_dates(signals, text, trend_type):
            """????????????????????????[(???, ???????), ...]???

            Args:
                signals: ???????
                text: ??????????
                trend_type: ?????????"??????"/"????"/"????"??
            """
            signal_dates = []
            for signal in signals:
                if signal.lower() in text.lower():
                    # ????????????????????
                    signal_pos = text.lower().find(signal.lower())
                    if signal_pos != -1:
                        # ?????????????
                        lines = text.split('\n')
                        signal_line = ""
                        signal_line_index = -1

                        # ?????????????
                        for i, line in enumerate(lines):
                            if signal.lower() in line.lower():
                                signal_line = line
                                signal_line_index = i
                                break

                        if signal_line:
                            # ??????????????????????????
                            if trend_type == "??????":
                                # ??????????????+??????2025-07-14 15:00??
                                datetime_matches = re.findall(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2})', signal_line)
                                if datetime_matches:
                                    signal_datetime = datetime_matches[0]
                                    signal_dates.append((signal, signal_datetime))
                                else:
                                    # ???????????????????????????u????????
                                    found_datetime = False
                                    for j in range(max(0, signal_line_index - 3), signal_line_index):
                                        prev_line = lines[j]
                                        datetime_matches = re.findall(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2})', prev_line)
                                        if datetime_matches:
                                            signal_datetime = datetime_matches[-1]  # ??????????????
                                            signal_dates.append((signal, signal_datetime))
                                            found_datetime = True
                                            break

                                    if not found_datetime:
                                        # ??????u??????????????????????
                                        signal_dates.append((signal, "1900-01-01 00:00"))
                            else:
                                # ??????????????????????2025-07-14??
                                date_matches = re.findall(r'(\d{4}-\d{2}-\d{2})', signal_line)
                                if date_matches:
                                    signal_date = date_matches[0]
                                    signal_dates.append((signal, signal_date))
                                else:
                                    # ?????????????????????u????????
                                    found_date = False
                                    for j in range(max(0, signal_line_index - 3), signal_line_index):
                                        prev_line = lines[j]
                                        date_matches = re.findall(r'(\d{4}-\d{2}-\d{2})', prev_line)
                                        if date_matches:
                                            signal_date = date_matches[-1]  # ???????????
                                            signal_dates.append((signal, signal_date))
                                            found_date = True
                                            break

                                    if not found_date:
                                        # ??????u???????????????????
                                        signal_dates.append((signal, "1900-01-01"))
            return signal_dates

        # ????????????????????
        start_signal_dates = extract_signal_dates(start_signals, combined_info, trend_type)
        confirm_signal_dates = extract_signal_dates(confirm_signals, combined_info, trend_type)
        end_signal_dates = extract_signal_dates(end_signals, combined_info, trend_type)

        # ?????????????????????????
        found_start_signals = [signal for signal, _ in start_signal_dates]
        found_confirm_signals = [signal for signal, _ in confirm_signal_dates]
        found_end_signals = [signal for signal, _ in end_signal_dates]

        has_start = len(found_start_signals) > 0
        has_confirm = len(found_confirm_signals) > 0
        has_end = len(found_end_signals) > 0

        # ??????????
        debug_info = f"[????]???????????:{len(combined_info)}, ??????:{len(found_start_signals)}, ??????:{len(found_confirm_signals)}, ???????:{len(found_end_signals)}"
        detected_signals.append(debug_info)

        # --- ??????????????????????????????????---
        # ????
        # 1. ?????? vs ???????????????????????????
        # 2. ?????? vs ???????????????????????????????
        # 3. ?????? vs ??????????????????????????

        if has_start and has_end:
            # ????????????????????????????????????????
            latest_start_datetime = "1900-01-01 00:00" if trend_type == "??????" else "1900-01-01"
            latest_end_datetime = "1900-01-01 00:00" if trend_type == "??????" else "1900-01-01"

            for signal, signal_datetime in start_signal_dates:
                if signal_datetime > latest_start_datetime:
                    latest_start_datetime = signal_datetime

            for signal, signal_datetime in end_signal_dates:
                if signal_datetime > latest_end_datetime:
                    latest_end_datetime = signal_datetime

            # ???????????????????
            if latest_end_datetime > latest_start_datetime:
                # ??????????????????????
                trend_stage_score = 0
                stage_signals.append(f"???????????????????????????({latest_end_datetime} > {latest_start_datetime})????????????")
                detected_signals.extend([f"???????:{s}" for s in found_end_signals])
            else:
                # ????????????????????????
                if len(found_start_signals) >= 2:
                    trend_stage_score = 3
                    stage_signals.append(f"??????????????????????????????({latest_start_datetime} >= {latest_end_datetime})?????????????>=2????")
                elif len(found_start_signals) == 1:
                    trend_stage_score = 2
                    stage_signals.append(f"??????????????????????????????({latest_start_datetime} >= {latest_end_datetime})?????????????1????")
                else:
                    trend_stage_score = 1
                    stage_signals.append(f"??????????????????????????????({latest_start_datetime} >= {latest_end_datetime})?????????????0????")
                detected_signals.extend([f"??????:{s}" for s in found_start_signals])
        elif has_confirm and has_end:
            # ????????????????????????????????????????????
            trend_stage_score = 0
            stage_signals.append("?????????????????????????????")
            detected_signals.extend([f"???????:{s}" for s in found_end_signals])
        elif has_end:
            # ??????????
            trend_stage_score = 0
            stage_signals.append("?????????????")
            detected_signals.extend([f"???????:{s}" for s in found_end_signals])
        elif has_start and has_confirm:
            # ?????????????????????????????????
            # ?????????????????????????????????
            latest_start_datetime = "1900-01-01 00:00" if trend_type == "??????" else "1900-01-01"
            latest_confirm_datetime = "1900-01-01 00:00" if trend_type == "??????" else "1900-01-01"

            for signal, signal_datetime in start_signal_dates:
                if signal_datetime > latest_start_datetime:
                    latest_start_datetime = signal_datetime

            for signal, signal_datetime in confirm_signal_dates:
                if signal_datetime > latest_confirm_datetime:
                    latest_confirm_datetime = signal_datetime

            # ???????????????????
            if latest_start_datetime > latest_confirm_datetime:
                # ????????????????????
                if len(found_start_signals) >= 2:
                    trend_stage_score = 3
                    stage_signals.append(f"??????????????????????????({latest_start_datetime} > {latest_confirm_datetime})?????????????>=2????")
                elif len(found_start_signals) == 1:
                    trend_stage_score = 2
                    stage_signals.append(f"??????????????????????????({latest_start_datetime} > {latest_confirm_datetime})?????????????1????")
                else:
                    trend_stage_score = 1
                    stage_signals.append(f"??????????????????????????({latest_start_datetime} > {latest_confirm_datetime})?????????????0????")
                detected_signals.extend([f"??????:{s}" for s in found_start_signals])
            else:
                # ?????????????????????????
                trend_stage_score = 2
                stage_signals.append(f"??????????????????????????????({latest_confirm_datetime} >= {latest_start_datetime})????????????")
                detected_signals.extend([f"???????:{s}" for s in found_confirm_signals])
        elif has_confirm:
            # ?????????
            trend_stage_score = 2
            stage_signals.append("?????????????")
            detected_signals.extend([f"???????:{s}" for s in found_confirm_signals])
        elif has_start:
            # ?????????
            if len(found_start_signals) >= 2:
                trend_stage_score = 3
                stage_signals.append("?????????????>=2????")
            elif len(found_start_signals) == 1:
                trend_stage_score = 2
                stage_signals.append("?????????????1????")
            else:
                trend_stage_score = 1
                stage_signals.append("?????????????0????")
            detected_signals.extend([f"??????:{s}" for s in found_start_signals])
        
        # 2. ???????????
        trend_strength_score = 2  # ???????
        strength_signals = []
        
        # ???????-???? (3??) - ????????????????
        strong_signals = [
            # ????
            "????????MAVOL5????????", 
            # ????????????
            "4????????K???MAVOL5????",
            "4????????K???VOL????MAVOL5",
            # ????????
            "MACD??????????",
            "?????????????",
            "MACD?????",
            # ???????
            "????????????MA5???"
        ]
        # ???????-????? (1 ??)
        weak_signals = [
            "3????????K???MAVOL5???",
            "4????????K???VOL????MAVOL5",
            "?????????","????????????",
            "MACD??????????", "MACD??????"
        ]
        
        # ????????? - ???????????????????
        found_strong_signals = []
        found_weak_signals = []
        
        for signal in strong_signals:
            if signal.lower() in combined_info_lower:
                found_strong_signals.append(signal)
        
        for signal in weak_signals:
            if signal.lower() in combined_info_lower:
                found_weak_signals.append(signal)
        
        has_strong = len(found_strong_signals) > 0
        has_weak = len(found_weak_signals) > 0
        
        # ?????????????
        strength_debug = f"[????]??????:{len(found_strong_signals)}, ???????:{len(found_weak_signals)}"
        detected_signals.append(strength_debug)
        
        # ?????????????????????
        if found_strong_signals:
            detected_signals.append(f"[????]???????????: {', '.join(found_strong_signals)}")
        if found_weak_signals:
            detected_signals.append(f"[????]????????????: {', '.join(found_weak_signals)}")
        
        # ????????????????????????????????
        detected_signals.append(f"[????]???????????????: {', '.join(strong_signals)}")
        detected_signals.append(f"[????]???'4????????K???MAVOL5????'?????????????: {'4????????K???MAVOL5????' in combined_info}")
        detected_signals.append(f"[????]???'DIF??????0?????'?????????????: {'DIF??????0?????' in combined_info}")
        detected_signals.append(f"[????]???'DIF??????0?????'?????????????????: {'DIF??????0?????' in strong_signals}")
        
        # ????????????????????
        if "????" in combined_info_lower:
            detected_signals.append(f"[????]??????????????????????")
            # ?????????????
            divergence_lines = [line.strip() for line in combined_info.split('\n') if '????' in line]
            if divergence_lines:
                detected_signals.append(f"[????]?????????: {'; '.join(divergence_lines[:3])}")  # ?????3??
        else:
            detected_signals.append(f"[????]????????????????????????")
        
        # ????????????????????????????????
        if has_strong and has_weak:
            if len(found_strong_signals) >= len(found_weak_signals):
                # ??????????????????????
                has_macd_strong_divergence = "MACD??????????" in found_strong_signals
                has_price_volume_rise = "?????????????" in found_strong_signals
                
                if has_macd_strong_divergence and has_price_volume_rise:
                    # ????????????????????????????????3????????????3??
                    if len(found_strong_signals) >= 3:
                        trend_strength_score = 3
                        strength_signals.append(f"?????????({len(found_strong_signals)})???????????({len(found_weak_signals)})??>=3?????????????????")
                    else:
                        trend_strength_score = 2
                        strength_signals.append(f"?????????({len(found_strong_signals)})???????????({len(found_weak_signals)})??<3???????????????????>=3????")
                else:
                    # ??????????????2????????????3??
                    if len(found_strong_signals) >= 2:
                        trend_strength_score = 3
                        strength_signals.append(f"?????????({len(found_strong_signals)})???????????({len(found_weak_signals)})??>=2??")
                    else:
                        trend_strength_score = 2
                        strength_signals.append(f"?????????({len(found_strong_signals)})???????????({len(found_weak_signals)})??<2??")
                detected_signals.extend([f"???????:{s}" for s in found_strong_signals])
                detected_signals.extend([f"????????:{s}" for s in found_weak_signals])
            else:
                trend_strength_score = 1
                strength_signals.append(f"??????????({len(found_weak_signals)})??????????({len(found_strong_signals)})")
                detected_signals.extend([f"???????:{s}" for s in found_strong_signals])
                detected_signals.extend([f"????????:{s}" for s in found_weak_signals])
        elif has_strong:
            # ??????????????????????
            has_macd_strong_divergence = "MACD??????????" in found_strong_signals
            has_price_volume_rise = "?????????????" in found_strong_signals
            
            if has_macd_strong_divergence and has_price_volume_rise:
                # ????????????????????????????????3????????????3??
                if len(found_strong_signals) >= 3:
                    trend_strength_score = 3
                    strength_signals.append(f"??????????>=3?????????????????")
                else:
                    trend_strength_score = 2
                    strength_signals.append(f"??????????{len(found_strong_signals)}???????????????????>=3????")
            else:
                # ??????????????2????????????3??
                if len(found_strong_signals) >= 2:
                    trend_strength_score = 3
                    strength_signals.append(f"??????????>=2????")
                else:
                    trend_strength_score = 2
                    strength_signals.append(f"??????????1????")
            detected_signals.extend([f"???????:{s}" for s in found_strong_signals])
        elif has_weak:
            trend_strength_score = 1
            strength_signals.append("??????????")
            detected_signals.extend([f"????????:{s}" for s in found_weak_signals])
        
        # ????????????????????????????6???
        total_trend_score = trend_stage_score + trend_strength_score
        final_score = 1 if total_trend_score > 4 else 0
        
        # ??????????????
        decision_process = f"???{trend_type}??????????"
        
        # ????????????? - ????????????????????????
        stage_process = ""
        for signal_msg in stage_signals:
            if stage_process:
                stage_process += "??"
            stage_process += signal_msg

        # ??????????stage_signals???????????
        if not stage_process:
            if has_end:
                stage_process = f"?????????????({len(found_end_signals)}??)??{', '.join(['\"' + s + '\"' for s in found_end_signals])}???????????????????????({trend_stage_score}??)"
            elif has_start and has_confirm:
                stage_process = f"????????????({len(found_start_signals)}??)????????({len(found_confirm_signals)}??)??????????????????????????{trend_stage_score}??"
            elif has_confirm:
                stage_process = f"?????????????({len(found_confirm_signals)}??)??{', '.join(['\"' + s + '\"' for s in found_confirm_signals])}?????????????????????({trend_stage_score}??)"
            elif has_start:
                if len(found_start_signals) >= 2:
                    stage_process = f"????????????({len(found_start_signals)}??)??{', '.join(['\"' + s + '\"' for s in found_start_signals])}?????????>=2????????????????????????({trend_stage_score}??)"
                elif len(found_start_signals) == 1:
                    stage_process = f"????????????({len(found_start_signals)}??)??{', '.join(['\"' + s + '\"' for s in found_start_signals])}?????????=1????????????????????????({trend_stage_score}??)"
                else:
                    stage_process = f"????????????({len(found_start_signals)}??)?????????=0?????????????????????????({trend_stage_score}??)"
            else:
                stage_process = f"?????\"DIF????????\"??\"RSI6???50\"??\"MA5???????\"??\"MACD???\"??\"MA5???MA10\"??????????????????????????????????????????????????????????({trend_stage_score}??)"
        
        # ?????????????
        if has_strong and has_weak:
            if len(found_strong_signals) >= len(found_weak_signals):
                # ???????????????????
                has_macd_strong_divergence = "MACD??????????" in found_strong_signals
                has_price_volume_rise = "?????????????" in found_strong_signals
                
                if has_macd_strong_divergence and has_price_volume_rise:
                    if len(found_strong_signals) >= 3:
                        strength_process = f"?????????({len(found_strong_signals)}??)??{', '.join(['\"' + s + '\"' for s in found_strong_signals])}?????????({len(found_weak_signals)}??)??{', '.join(['\"' + s + '\"' for s in found_weak_signals])}?????????????>=3??????\"MACD??????????\"??\"?????????????\"??????????>=3??????????????????????({trend_strength_score}??)"
                    else:
                        strength_process = f"?????????({len(found_strong_signals)}??)??{', '.join(['\"' + s + '\"' for s in found_strong_signals])}?????????({len(found_weak_signals)}??)??{', '.join(['\"' + s + '\"' for s in found_weak_signals])}???????????<3??????\"MACD??????????\"??\"?????????????\"??????????>=3????????????????????????({trend_strength_score}??)"
                else:
                    if len(found_strong_signals) >= 2:
                        strength_process = f"?????????({len(found_strong_signals)}??)??{', '.join(['\"' + s + '\"' for s in found_strong_signals])}?????????({len(found_weak_signals)}??)??{', '.join(['\"' + s + '\"' for s in found_weak_signals])}?????????????>=2????????????????????({trend_strength_score}??)"
                    else:
                        strength_process = f"?????????({len(found_strong_signals)}??)??{', '.join(['\"' + s + '\"' for s in found_strong_signals])}?????????({len(found_weak_signals)}??)??{', '.join(['\"' + s + '\"' for s in found_weak_signals])}???????????<2??????????????????????({trend_strength_score}??)"
            else:
                strength_process = f"??????????({len(found_weak_signals)}??)??{', '.join(['\"' + s + '\"' for s in found_weak_signals])}????????({len(found_strong_signals)}??)??{', '.join(['\"' + s + '\"' for s in found_strong_signals])}?????????????????????????????({trend_strength_score}??)"
        elif has_strong:
            # ???????????????????
            has_macd_strong_divergence = "MACD??????????" in found_strong_signals
            has_price_volume_rise = "?????????????" in found_strong_signals
            
            if has_macd_strong_divergence and has_price_volume_rise:
                if len(found_strong_signals) >= 3:
                    strength_process = f"?????????({len(found_strong_signals)}??)??{', '.join(['\"' + s + '\"' for s in found_strong_signals])}????\"MACD??????????\"??\"?????????????\"?????????>=3????????????????????({trend_strength_score}??)"
                else:
                    strength_process = f"?????????({len(found_strong_signals)}??)??{', '.join(['\"' + s + '\"' for s in found_strong_signals])}????\"MACD??????????\"??\"?????????????\"????????<3??????>=3????????????????????????({trend_strength_score}??)"
            else:
                if len(found_strong_signals) >= 2:
                    strength_process = f"?????????({len(found_strong_signals)}??)??{', '.join(['\"' + s + '\"' for s in found_strong_signals])}?????????>=2????????????????????({trend_strength_score}??)"
                else:
                    strength_process = f"?????????({len(found_strong_signals)}??)??{', '.join(['\"' + s + '\"' for s in found_strong_signals])}?????????=1??????????????????????({trend_strength_score}??)"
        elif has_weak:
            strength_process = f"??????????({len(found_weak_signals)}??)??{', '.join(['\"' + s + '\"' for s in found_weak_signals])}???????????????????({trend_strength_score}??)"
        else:
            strength_process = f"???????????????????????????????????????({trend_strength_score}??)"
        
        # ????????????
        final_process = f"??????{total_trend_score}???{'????4??' if total_trend_score > 4 else '???????4??'}??????????{final_score}??"
        
        decision_process += f"????????????????{stage_process}???????????????????{strength_process}??{final_process}??"
        
        # ??????????????
        details = {
            'start_cnt': len(found_start_signals),
            'confirm_cnt': len(found_confirm_signals),
            'strong_cnt': len(found_strong_signals),
            'end_cnt': len(found_end_signals),
            'weak_cnt': len(found_weak_signals),
            'subtotal': total_trend_score
        }
        
        return final_score, decision_process, details
        
    except Exception as e:
        return 0, f"????{trend_type}???????????: {str(e)}", {}

def calculate_trend_score(tech_info, trend_type, high_threshold, low_threshold):
    """?????????????????????????????????"""
    score, reason, _ = calculate_trend_score_detailed(tech_info, trend_type, high_threshold, low_threshold)
    return score, reason

def extract_price_space(text, position_type):
    """??????????????????"""
    try:
        # ??????????????????????
        import re
        
        # ????????????/??????????????????????
        pattern = rf"??{position_type}[^0-9]*?([0-9.]+)%"
        match = re.search(pattern, text)
        
        if match:
            return float(match.group(1))
        
        # ??????????????????????????
        pattern = rf"??{position_type}[^0-9]*?([0-9.]+)"
        match = re.search(pattern, text)
        
        if match:
            return float(match.group(1))
            
        return None
        
    except Exception:
        return None

def extract_volume_data_from_chan_analysis(stock_code, stock_name=None):
    """
    ??????????????????????

    ????:
        dict: ?????????????????
    """
    try:
        # ???????
        if not stock_code or not isinstance(stock_code, str) or len(stock_code.strip()) == 0:
            return {"error": "?????????????????"}

        if not CHAN_ANALYSIS_AVAILABLE:
            return {"error": "???????????????"}

        # ?????????????????????????????????????????????
        import logging
        from Common.CEnum import KL_TYPE

        # ???????????
        logger = logging.getLogger()
        original_level = logger.level
        captured_logs = []

        class CaptureHandler(logging.Handler):
            def emit(self, record):
                captured_logs.append(record.getMessage())

        capture_handler = CaptureHandler()
        logger.addHandler(capture_handler)
        logger.setLevel(logging.INFO)

        try:
            # ?????????????????
            result = Zstock_find3buy.check_3buy_conditions_debug(
                stock_code,
                stock_name or stock_code,
                k_type=KL_TYPE.K_DAY
            )

            if not result or not isinstance(result, dict):
                return {"error": "????????????????"}

            # ???????????????????
            zs_start_time = None
            zs_end_time = None

            for log in captured_logs:
                if "??????????????????" in log:
                    import re
                    time_match = re.search(r'(\d{4}/\d{1,2}/\d{1,2})\s*??\s*(\d{4}/\d{1,2}/\d{1,2})', log)
                    if time_match:
                        from datetime import datetime
                        zs_start_str = time_match.group(1)
                        zs_end_str = time_match.group(2)
                        zs_start_time = datetime.strptime(zs_start_str, '%Y/%m/%d')
                        zs_end_time = datetime.strptime(zs_end_str, '%Y/%m/%d')
                        break

            if not zs_start_time or not zs_end_time:
                return {"error": "?????????????????????????"}

            # ???K?????? - ?????Zstock_find3buy.py???????????
            try:
                import xtquant.xtdata as xtdata
                import pandas as pd
                from datetime import timedelta

                # ???????????? - ??Zstock_find3buy.py???????
                formatted_code = f"{stock_code}.SZ" if stock_code.startswith(('0', '3')) else f"{stock_code}.SH"

                # ?????????????????
                try:
                    from Zstock_tech_qmttdx import download_all_data_for_stock
                    download_all_data_for_stock(stock_code)
                except Exception:
                    pass  # ?????????????????

                # ?????Zstock_find3buy.py??????????????K??????
                kline_data_raw = xtdata.get_market_data(
                    field_list=['time', 'open', 'high', 'low', 'close', 'volume', 'amount'],
                    stock_list=[formatted_code],
                    period='1d',
                    count=500,  # ????????????
                    dividend_type='front',
                    fill_data=True
                )

                # ?????Zstock_find3buy.py???????????????
                if kline_data_raw and 'time' in kline_data_raw:
                    dates = pd.to_datetime(kline_data_raw.get('time').loc[formatted_code], unit='ms', utc=True).dt.tz_convert('Asia/Shanghai').dt.tz_localize(None)
                    df_raw = pd.DataFrame({'time': dates})

                    for field in ['open', 'high', 'low', 'close', 'volume', 'amount']:
                        if field in kline_data_raw:
                            field_data = kline_data_raw[field]
                            if isinstance(field_data, pd.DataFrame) and formatted_code in field_data.index:
                                df_raw[field] = field_data.loc[formatted_code].values

                    # ?????volume??
                    if 'volume' not in df_raw.columns:
                        return {"error": "K??????????????????"}

                    # ??????????????????????
                    volume_result = Zstock_find3buy.analyze_volume_in_zs_range(
                        df_raw, zs_start_time, zs_end_time, logger
                    )

                    if volume_result:
                        # ????????????????????
                        volume_result["has_valid_data"] = True
                        return volume_result
                    else:
                        return {"error": "????????????????"}

                else:
                    return {"error": "??????K??????"}

            except Exception as e:
                return {"error": f"???K??????????????????: {str(e)}"}

        finally:
            logger.removeHandler(capture_handler)
            logger.setLevel(original_level)

    except Exception as e:
        return {"error": f"???????????????: {str(e)}"}

    except Exception as e:
        return {"error": f"???????????????: {str(e)}"}

def calculate_fund_activity_score(stock_code, stock_name=None):
    """
    ???????????????

    ???????
    - ????A??????????? / ?????????? > 1.5
    - ????B???????????????? / ??????K?????????? > 1.5
    ?????????????1????????0??
    """
    try:
        # ????????????
        volume_data = extract_volume_data_from_chan_analysis(stock_code, stock_name)

        if "error" in volume_data:
            return 0, f"?????????????: {volume_data['error']}", volume_data

        # ???????A??????B
        condition_a_met = False
        condition_b_met = False

        # ????A??????????? / ?????????? > 1.5
        if (volume_data.get("after_zs_weekly_volume", 0) > 0 and
            volume_data.get("zs_weekly_volume", 0) > 0):
            ratio_a = volume_data["after_zs_weekly_volume"] / volume_data["zs_weekly_volume"]
            condition_a_met = ratio_a > 1.5

        # ????B???????????????? / ??????K?????????? > 1.5
        if (volume_data.get("after_zs_max_volume", 0) > 0 and
            volume_data.get("zs_avg_volume", 0) > 0):
            ratio_b = volume_data["after_zs_max_volume"] / volume_data["zs_avg_volume"]
            condition_b_met = ratio_b > 1.5

        # ??????????????????1??
        score = 1 if (condition_a_met or condition_b_met) else 0

        # ??????????????
        decision_process = f"?????????? - ???????: {stock_code}\n"
        decision_process += f"??????K??????????: {volume_data.get('zs_avg_volume', 0):.0f}\n"
        decision_process += f"??????????: {volume_data.get('zs_weekly_volume', 0):.0f}\n"
        decision_process += f"??????????????: {volume_data.get('after_zs_max_volume', 0):.0f}\n"
        decision_process += f"?????????: {volume_data.get('after_zs_weekly_volume', 0):.0f}\n"

        # ????A????
        if volume_data.get("zs_weekly_volume", 0) > 0:
            if volume_data.get("after_zs_weekly_volume", 0) > 0:
                ratio_a = volume_data["after_zs_weekly_volume"] / volume_data["zs_weekly_volume"]
                decision_process += f"????A: ?????????/?????????? = {ratio_a:.2f} {'> 1.5 (????)' if condition_a_met else '<= 1.5 (??????)'}\n"
            else:
                decision_process += f"????A: ??????????????? (??????)\n"
        else:
            decision_process += f"????A: ???????????????? (??????)\n"

        # ????B????
        if volume_data.get("zs_avg_volume", 0) > 0:
            if volume_data.get("after_zs_max_volume", 0) > 0:
                ratio_b = volume_data["after_zs_max_volume"] / volume_data["zs_avg_volume"]
                decision_process += f"????B: ??????????????/??????K?????????? = {ratio_b:.2f} {'> 1.5 (????)' if condition_b_met else '<= 1.5 (??????)'}\n"
            else:
                decision_process += f"????B: ???????????????????? (??????)\n"
        else:
            decision_process += f"????B: ????????K?????????????? (??????)\n"

        decision_process += f"???????????: {'???????????' if score == 1 else '????????????????'}????{score}??"

        return score, decision_process, volume_data

    except Exception as e:
        error_msg = f"???????????????????????{str(e)}"
        return 0, error_msg, {"error": str(e)}

def calculate_fund_score_detailed(history_info, stock_code=None, stock_name=None):
    """??????????????????????????- ???????????????????????????????????"""
    try:
        # ?????1?????????
        activity_score, activity_process, activity_details = calculate_fund_activity_score(stock_code, stock_name)

        # ?????2??????/??????????????????????????
        flow_score, flow_process, flow_details = calculate_fund_flow_score_detailed(history_info)

        # ??????????????????????????????1??????????????????1??
        final_score = 1 if (activity_score == 1 or flow_score == 1) else 0

        # ?????????????????
        decision_process = f"???????????? - ???????: {stock_code or '???'}\n\n"
        decision_process += f"???????1??????????\n{activity_process}\n\n"
        decision_process += f"???????2??????????????????\n{flow_process}\n\n"
        decision_process += f"??????????\n"
        decision_process += f"??????????: {activity_score}??\n"
        decision_process += f"??????????????????: {flow_score}??\n"
        decision_process += f"??????????????????{'???????????1??' if final_score == 1 else '????0??'}??????????????????{final_score}???"

        # ?????????????
        details = {
            'final_score': final_score,
            'activity_score': activity_score,
            'flow_score': flow_score,
            'activity_details': activity_details,
            'flow_details': flow_details
        }

        return final_score, decision_process, details

    except Exception as e:
        return 0, f"????????????????: {str(e)}", {}

def calculate_fund_flow_score_detailed(history_info):
    """???????????????????????????????"""
    try:
        if not history_info:
            return 0, "???????????", {"error": "???????????"}

        # ??????????????????????????????
        positive_main_flow_days = 0
        positive_main_buy_days = 0
        detailed_data = []

        lines = history_info.split('\n')
        for line in lines:
            # ???????????????????????????
            if ("\t" in line or " | " in line) and not line.startswith("????") and not line.startswith("---"):
                # ????????????????????????
                if "\t" in line:
                    parts = line.split("\t")
                else:
                    parts = line.split(" | ")

                if len(parts) >= 5:
                    date_str = parts[0].strip()
                    try:
                        # ???????????? (??2??)
                        main_flow_str = parts[1].strip()
                        main_flow_positive = False
                        if main_flow_str not in ["???", "N/A"] and "??" in main_flow_str:
                            # ????????
                            main_flow_val = main_flow_str.replace("??", "").replace("-", "")
                            main_flow = float(main_flow_val)
                            if not main_flow_str.startswith("-") and main_flow > 0:
                                positive_main_flow_days += 1
                                main_flow_positive = True

                        # ????????? (??3??)
                        main_buy_str = parts[2].strip()
                        main_buy_positive = False
                        if main_buy_str not in ["???", "N/A"] and "??" in main_buy_str:
                            # ????????
                            main_buy_val = main_buy_str.replace("??", "").replace("-", "")
                            main_buy = float(main_buy_val)
                            if not main_buy_str.startswith("-") and main_buy > 0:
                                positive_main_buy_days += 1
                                main_buy_positive = True

                        # ??????????
                        if main_flow_positive or main_buy_positive:
                            detailed_data.append(f"{date_str}:????????{'??' if main_flow_positive else '??'},?????{'??' if main_buy_positive else '??'}")
                    except (ValueError, IndexError):
                        continue

        total_positive_days = positive_main_flow_days + positive_main_buy_days
        score = 1 if total_positive_days >= 6 else 0

        # ??????????????
        decision_process = f"??5???????????????????????????????{positive_main_flow_days}??????????????????{positive_main_buy_days}????????{total_positive_days}??"
        if detailed_data:
            decision_process += f"???????{'; '.join(detailed_data)}??"
        decision_process += f"?????????????{'??6??' if total_positive_days >= 6 else '<6??'}?????????????????????????{score}???"

        # ?????????????
        details = {
            'positive_days': total_positive_days,
            'main_flow_days': positive_main_flow_days,
            'main_buy_days': positive_main_buy_days
        }

        return score, decision_process, details

    except Exception as e:
        return 0, f"?????????????????????????: {str(e)}", {}

def calculate_fund_score(history_info):
    """??????????????????????????????????"""
    score, reason, _ = calculate_fund_score_detailed(history_info)
    return score, reason

def calculate_news_score_detailed(tech_info, is_local_calculation=False):
    """??????????????????????????"""
    try:
        # ??????????????0??
        if is_local_calculation:
            decision_process = "???????????????????????????????????0???????????????????????????"
            return 0, decision_process, {}
        
        # ??????????????
        news_section = ""
        start_idx = tech_info.find("??????????")
        if start_idx != -1:
            end_idx = tech_info.find("\n\n", start_idx)
            if end_idx == -1:
                end_idx = len(tech_info)
            news_section = tech_info[start_idx:end_idx]
        
        if not news_section:
            decision_process = "??????????????????????????????????????0???"
            return 0, decision_process, {}
        
        # ?????????
        positive_keywords = ["????", "????", "????", "???", "????", "???", "????"]
        # ????????  
        negative_keywords = ["????", "????", "???", "????", "????", "???"]
        
        positive_signals = [keyword for keyword in positive_keywords if keyword in news_section]
        negative_signals = [keyword for keyword in negative_keywords if keyword in news_section]
        
        positive_count = len(positive_signals)
        negative_count = len(negative_signals)
        
        # ??????????????
        decision_process = f"???????????????????????????"
        if positive_signals:
            decision_process += f"???{positive_count}??????????{', '.join(['\"' + s + '\"' for s in positive_signals])}??"
        else:
            decision_process += f"????????????"
        
        if negative_signals:
            decision_process += f"?????{negative_count}??????????{', '.join(['\"' + s + '\"' for s in negative_signals])}??"
        else:
            decision_process += f"??????????????"
        
        if positive_count > negative_count:
            decision_process += f"????????????????????????????1???"
            return 1, decision_process, {}
        elif negative_count > positive_count:
            decision_process += f"????????????????????????????-1???"
            return -1, decision_process, {}
        else:
            decision_process += f"???????????????????????????????????????0???"
            return 0, decision_process, {}
            
    except Exception as e:
        return 0, f"????????????????: {str(e)}", {}

def calculate_news_score(tech_info, is_local_calculation=False):
    """??????????????????????????????????"""
    score, reason, _ = calculate_news_score_detailed(tech_info, is_local_calculation)
    return score, reason

def calculate_industry_trend_score_detailed(industry_info):
    """????????????????????????????"""
    try:
        if not industry_info:
            decision_process = "???????????????????????????????????????????0???"
            return 0, decision_process, decision_process
        
        # ??????????????????????????????????????????
        # -----------------------------------------------------------------
        # 1) ??????????????????????????????????????
        score, decision_process, details = calculate_trend_score_detailed(
            industry_info, "????", 10, 5
        )

        # 2) ????????????????????????????
        import re
        def _extract_name(data: str) -> str:
            patterns = [
                r"???????\s+([^\s??]+)\s*????????",
                r"^([^\s]+)\s+?????????????",
                r"??????\s*'([^']+)'",
            ]
            for pat in patterns:
                m = re.search(pat, data, re.MULTILINE)
                if m:
                    name = m.group(1).strip()
                    return name if not name.isdigit() else "??????"
            return "??????"

        industry_name = _extract_name(industry_info)

        # 3) ?????????"??????????"???"?????????????"?????????????
        industry_decision = decision_process.replace("????????????", "????????????????")
        industry_decision = f"??{industry_name} ?????????" + industry_decision

        # 4) ????????????????????????????????????
        return score, industry_decision, details
    except Exception as e:
        error_process = f"????????????????????: {e}"
        return 0, error_process, {}

def calculate_industry_trend_score(industry_info):
    """????????????????????????????????????"""
    score, reason, _ = calculate_industry_trend_score_detailed(industry_info)
    return score, reason

def calculate_resonance_score_detailed(resonance_count, resonance_info):
    """????????????????????????????"""
    try:
        score = 1 if resonance_count >= 2 else 0
        
        # ??????????????
        decision_process = f"???????????????????????????{resonance_count}????????????????????"
        if resonance_info and "???" in resonance_info:
            decision_process += f"???????{resonance_info}??"
        decision_process += f"????????????????{'??2?' if resonance_count >= 2 else '<2?'}??????????{score}???"
        
        # ?????????????
        details = {
            'resonance_count': resonance_count
        }
        
        return score, decision_process, details
        
    except Exception as e:
        error_process = f"???????????????????????{str(e)}"
        return 0, error_process, {}

def calculate_resonance_score(resonance_count, resonance_info):
    """????????????????????????????????????"""
    score, reason, _ = calculate_resonance_score_detailed(resonance_count, resonance_info)
    return score, reason

def _extract_pressure_space_from_tech_info(tech_info: str) -> float:
    """??????????????????????????????????????"""
    import re
    if not isinstance(tech_info, str): 
        return 0.0
    
    # ??????????????   - ?????1: 856.82 (0.49%??????), ???: 2
    pattern = r"\s*-\s*?????\d+:\s*[\d\.]+\s*\(([\d\.]+)%??????\)"
    matches = re.findall(pattern, tech_info)
    
    if matches:
        try:
            # ????????????????????????
            return float(matches[0])
        except (ValueError, TypeError):
            pass
    
    # ????????????????????????????????
    # ???????????????X.X%
    pattern2 = r"????????[:??]\s*([\d\.]+)%"
    matches2 = re.findall(pattern2, tech_info)
    if matches2:
        try:
            return float(matches2[0])
        except (ValueError, TypeError):
            pass
    
    # ?????????????????X.X%
    pattern3 = r"??????????\s*([\d\.]+)%"
    matches3 = re.findall(pattern3, tech_info)
    if matches3:
        try:
            return float(matches3[0])
        except (ValueError, TypeError):
            pass
    
    return 0.0

def calculate_profit_loss_ratio_new(stock_code, stock_name=None):
    """
    ??????????????????? Zstock_find3buy.py ??????????

    ????:
        stock_code: 6?????????
        stock_name: ?????????????

    ????:
        (????????, ?????????) ?? (0.0, ??????????)
    """
    try:
        # ???????
        if not stock_code or not isinstance(stock_code, str) or len(stock_code.strip()) == 0:
            return 0.0, {"error": "?????????????????", "method": "parameter_error"}

        if not CHAN_ANALYSIS_AVAILABLE:
            return 0.0, {"error": "???????????????", "method": "unavailable"}

        # ??????????????????????
        # ?????????????????
        from libs.chan.Common.CEnum import KL_TYPE

        # ????????3??????????????????
        result = Zstock_find3buy.check_3buy_conditions_debug(
            stock_code,
            stock_name or stock_code,
            k_type=KL_TYPE.K_DAY
        )

        if result and isinstance(result, dict):
            # ??????????????????
            profit_loss_ratio = result.get('profit_loss_ratio', 0.0)
            ratio_details = result.get('ratio_details', {})

            # 修复：允许负数盈亏比，只要是有效数值就接受
            if isinstance(profit_loss_ratio, (int, float)) and not (profit_loss_ratio == 0 and ratio_details.get('error')):
                return float(profit_loss_ratio), {
                    "method": "chan_analysis",
                    "ratio_details": ratio_details,
                    "zs_upper": result.get('zs_upper', 0),
                    "zs_lower": result.get('zs_lower', 0),
                    "current_price": result.get('price_for_judgment', 0),
                    "calculation_case": ratio_details.get('calculation_case', 'N/A') if ratio_details else 'N/A',
                    "original_ratio": profit_loss_ratio  # 保存原始盈亏比用于调试
                }
            else:
                # 缠论分析返回了无效盈亏比（非数值或明确的错误）
                error_detail = f"缠论分析返回无效盈亏比: {profit_loss_ratio}"
                if ratio_details and ratio_details.get('error'):
                    error_detail += f", 详细错误: {ratio_details.get('error')}"

                return 0.0, {
                    "error": error_detail,
                    "method": "chan_analysis_failed",
                    "result": result,
                    "fallback_reason": "盈亏比非数值或存在计算错误"
                }
        else:
            # ?????????????????????
            return _calculate_profit_loss_ratio_fallback(stock_code)

    except Exception as e:
        # ??????????????????
        error_msg = f"?????????: {str(e)}"
        print(f"??? {stock_code} ??????????: {error_msg}")
        return _calculate_profit_loss_ratio_fallback(stock_code)

def _calculate_profit_loss_ratio_fallback(stock_code):
    """
    ????????????????
    ???????????????????????
    """
    try:
        # ?????????????????????
        tech_info = stock_tech.analyze_stock(stock_code)

        if isinstance(tech_info, str) and "error" not in tech_info.lower():
            # ??????????????????????????????????????
            pressure_pct = _extract_pressure_space_from_tech_info(tech_info)

            if pressure_pct > 0:
                # ??????????????????????
                # ???????????????????
                simplified_ratio = pressure_pct / 10.0  # 10%??????????1.0?????
                return simplified_ratio, {
                    "method": "simplified_pressure_space",
                    "pressure_space_pct": pressure_pct,
                    "calculation": f"????????{pressure_pct}% / 10 = {simplified_ratio:.2f}"
                }

        # 如果无法获取任何有效信息，返回默认值
        # 修复：提供更详细的错误信息，帮助调试
        return 0.0, {
            "error": "无法获取技术指标信息",
            "method": "default_fallback",
            "tech_info_available": isinstance(tech_info, str) and len(tech_info) > 0,
            "tech_info_preview": str(tech_info)[:200] if tech_info else "None"
        }

    except Exception as e:
        return 0.0, {
            "error": f"???????????: {str(e)}",
            "method": "fallback_failed"
        }

def _extract_pressure_space_from_tech_info(tech_info: str) -> float:
    """???????????????????????????"""
    import re
    if not isinstance(tech_info, str):
        return 0.0

    # ???????   - ?????1: 856.82 (0.49%??????), ???: 2
    pattern = r"\s*-\s*?????\d+:\s*[\d\.]+\s*\(([\d\.]+)%??????\)"
    matches = re.findall(pattern, tech_info)

    if matches:
        try:
            # ????????????????????????
            return float(matches[0])
        except (ValueError, TypeError):
            pass

    # ???????????????????????????????
    # ???????????????X.X%
    pattern2 = r"????????[??:]\s*([\d\.]+)%"
    matches2 = re.findall(pattern2, tech_info)
    if matches2:
        try:
            return float(matches2[0])
        except (ValueError, TypeError):
            pass

    # ?????????????????X.X%
    pattern3 = r"??????????\s*([\d\.]+)%"
    matches3 = re.findall(pattern3, tech_info)
    if matches3:
        try:
            return float(matches3[0])
        except (ValueError, TypeError):
            pass

    return 0.0

def calculate_profit_loss_ratio_score_detailed(tech_info, stock_code=None):
    """??????????????????????????- ???????????

    ???????
    - ????? > 1.0 ???1??
    - ????? <= 1.0 ???0??

    ????:
        tech_info: ??????????????????????????????????
        stock_code: ???????????????????

    ????:
        (????, ???????, ??????)
    """
    try:
        # ??????????????????????????????????
        if not stock_code:
            # ?????tech_info??????????????????????
            import re
            code_match = re.search(r'(\d{6})', str(tech_info) if tech_info else '')
            if code_match:
                stock_code = code_match.group(1)
            else:
                decision_process = "????????????????????????????????????????????????????0???"
                return 0, decision_process, {"error": "?????????", "method": "no_stock_code"}

        # ????????????????????
        ratio, ratio_details = calculate_profit_loss_ratio_new(stock_code)

        # 修复：改进盈亏比评分逻辑，允许负数盈亏比参与评分
        method_desc = ratio_details.get('method', 'unknown')

        # 区分计算失败和计算成功但结果不理想的情况
        if 'error' in ratio_details:
            # 计算失败的情况
            score = 0
            error_info = ratio_details.get('error', '未知错误')
            decision_process = f"盈亏比分析 - 股票代码: {stock_code}\n计算方法: {method_desc}\n错误信息: {error_info}\n盈亏比计算失败，因此本维度得0分。"
        else:
            # 计算成功的情况，根据盈亏比值评分
            if ratio > 1.0:
                score = 1
                if method_desc == 'chan_analysis':
                    calculation_case = ratio_details.get('calculation_case', 'N/A')
                    decision_process = f"盈亏比分析 - 股票代码: {stock_code}\n使用缠论分析方法计算得到盈亏比: {ratio:.4f}\n计算情况: {calculation_case}\n盈亏比 > 1.0，因此本维度得1分。"
                elif method_desc == 'simplified_pressure_space':
                    pressure_space = ratio_details.get('pressure_space_pct', 0)
                    decision_process = f"盈亏比分析 - 股票代码: {stock_code}\n使用简化压力位空间方法计算得到盈亏比: {ratio:.4f}\n压力位空间: {pressure_space}%\n盈亏比 > 1.0，因此本维度得1分。"
                else:
                    decision_process = f"盈亏比分析 - 股票代码: {stock_code}\n使用{method_desc}方法计算得到盈亏比: {ratio:.4f}\n盈亏比 > 1.0，因此本维度得1分。"
            else:
                score = 0
                # 修复：为负数盈亏比提供更详细的说明
                ratio_desc = "负数" if ratio < 0 else f"{ratio:.4f}"
                if method_desc == 'chan_analysis':
                    calculation_case = ratio_details.get('calculation_case', 'N/A')
                    decision_process = f"盈亏比分析 - 股票代码: {stock_code}\n使用缠论分析方法计算得到盈亏比: {ratio:.4f}\n计算情况: {calculation_case}\n盈亏比 = {ratio_desc} <= 1.0，因此本维度得0分。"
                elif method_desc == 'simplified_pressure_space':
                    pressure_space = ratio_details.get('pressure_space_pct', 0)
                    decision_process = f"盈亏比分析 - 股票代码: {stock_code}\n使用简化压力位空间方法计算得到盈亏比: {ratio:.4f}\n压力位空间: {pressure_space}%\n盈亏比 = {ratio_desc} <= 1.0，因此本维度得0分。"
                else:
                    decision_process = f"盈亏比分析 - 股票代码: {stock_code}\n使用{method_desc}方法计算得到盈亏比: {ratio:.4f}\n盈亏比 = {ratio_desc} <= 1.0，因此本维度得0分。"

        # ?????????????
        details = {
            'ratio': ratio,
            'ratio_details': ratio_details,
            'pressure_space_pct': ratio_details.get('pressure_space_pct', 0.0),
            'method': ratio_details.get('method', 'unknown'),
            'calculation_case': ratio_details.get('calculation_case', 'N/A')
        }

        
        return score, decision_process, details
        
    except Exception as e:
        error_process = f"?????????????????????{str(e)}"
        return 0, error_process, {"error": str(e), "method": "exception"}

def calculate_profit_loss_ratio_score(tech_info, stock_code=None):
    """??????????????????????????????????"""
    score, reason, _ = calculate_profit_loss_ratio_score_detailed(tech_info, stock_code)
    return score, reason

def parse_item_scores(rationale):
    """?????????????????"""
    # ????????6????????
    items = ["?????????????", "????????????", "?????????", "?????????", "????????????????", "?????????"]
    item_scores = {item: {"score": 0, "reason": ""} for item in items}
    
    # 1. ???????????????????
    sections = {}
    section_patterns = [
        (items[0], r'0\.\s*?????????????[^1]*?(?=1\.|$)'),
        (items[1], r'1\.\s*????????????[^2]*?(?=2\.|$)'),
        (items[2], r'2\.\s*?????????[^3]*?(?=3\.|$)'),
        (items[3], r'3\.\s*?????????[^4]*?(?=4\.|$)'),
        (items[4], r'4\.\s*????????????????[^5]*?(?=5\.|$)'),
        (items[5], r'5\.\s*?????????.*?(?=$)')
    ]
    
    # ??????????????????
    for item, pattern in section_patterns:
        match = re.search(pattern, rationale, re.DOTALL | re.IGNORECASE)
        if match:
            sections[item] = match.group(0).strip()
    
    # 2. ??????????????????
    for item, text in sections.items():
        if not text:
            continue  # ????????????????????
            
        # ???????????????
        # ???????X. ????????Y???????Y??-1, 0??1
        score_match = re.search(r'[:??]\s*([-]?[01])\s*??[????\.]', text)
        
        if score_match:
            try:
                score_str = score_match.group(1)
                score = int(score_str)
                # ???????????????????
                if score in [-1, 0, 1]:
                    item_scores[item]["score"] = score
                    item_scores[item]["reason"] = text
                else:
                    print(f"????: ??? {item} ?????????? {score} ?????????????0")
                    item_scores[item]["score"] = 0
                    item_scores[item]["reason"] = text
            except (ValueError, IndexError) as e:
                print(f"????: ??????? {item} ??????????: {e}??????????????")
                # ??????????????????????
                if "????" in text or "????" in text or "????" in text or "????" in text or "???" in text or "???????" in text:
                    item_scores[item]["score"] = 1
                    item_scores[item]["reason"] = text
                elif "????" in text or "???" in text or "????" in text or "????" in text or "???????" in text:
                    item_scores[item]["score"] = 0  # ???0??
                    item_scores[item]["reason"] = text
                else:
                    item_scores[item]["score"] = 0
                    item_scores[item]["reason"] = text
        else:
            # ???????????????????
            alternate_match = re.search(r'[????][???]\s*[:??]\s*([-]?[01])', text)
            if alternate_match:
                try:
                    score = int(alternate_match.group(1))
                    if score in [-1, 0, 1]:
                        item_scores[item]["score"] = score
                        item_scores[item]["reason"] = text
                    else:
                        item_scores[item]["score"] = 0
                        item_scores[item]["reason"] = text
                except (ValueError, IndexError):
                    item_scores[item]["score"] = 0
                    item_scores[item]["reason"] = text
            else:
                # ????????????
                if "????" in text or "????" in text or "????" in text or "????" in text or "???" in text or "???????" in text:
                    item_scores[item]["score"] = 1
                    item_scores[item]["reason"] = text
                elif "????" in text or "???" in text or "????" in text or "????" in text or "???????" in text:
                    item_scores[item]["score"] = 0  # ???0??
                    item_scores[item]["reason"] = text
                else:
                    item_scores[item]["score"] = 0
                    item_scores[item]["reason"] = text
        
        # ??????????
        print(f"????[{item}]: ??????=\"{text[:50]}...\"?????????={item_scores[item]['score']}")
    
    # ???????????????????????
    missing_items = [item for item in items if item not in sections]
    if missing_items:
        print(f"????: ????????????????????: {', '.join(missing_items)}")
        # ????????????????????????????
        for item in missing_items:
            # ?????????
            item_index = items.index(item)
            
            # ?????????0???????????
            item_index_in_output = item_index
            
            # ?????????????????????????????
            # ????????????????????????????????????????????????????????????
            simple_pattern = rf'{item_index_in_output}\.\s*{re.escape(item)}[:??]\s*([-]?[01])\s*??'
            alt_pattern = rf'{item_index_in_output}\.\s*{re.escape(item)}[^??]*?(?:[????:]\s*)([-]?[01])\s*??'
            
            # ??????????
            simple_match = re.search(simple_pattern, rationale, re.DOTALL | re.IGNORECASE)
            if simple_match:
                try:
                    score = int(simple_match.group(1))
                    item_scores[item]["score"] = score
                    item_scores[item]["reason"] = simple_match.group(0)
                    print(f"????????????[{item}]: ????={score}, ??????=\"{simple_match.group(0)}\"")
                    continue
                except (ValueError, IndexError) as e:
                    print(f"?????????[{item}]???: {e}")
            
            # ???????????
            alt_match = re.search(alt_pattern, rationale, re.DOTALL | re.IGNORECASE)
            if alt_match:
                try:
                    score = int(alt_match.group(1))
                    item_scores[item]["score"] = score
                    item_scores[item]["reason"] = alt_match.group(0)
                    print(f"????????????[{item}]: ????={score}, ??????=\"{alt_match.group(0)}\"")
                    continue
                except (ValueError, IndexError) as e:
                    print(f"?????????[{item}]???: {e}")
            
            # ????????????????????????????????????????
            full_pattern = rf'{item_index_in_output}\.\s*{re.escape(item)}[^{item_index_in_output+1}]*?(?=\n{item_index_in_output+1}\.|$)'
            full_match = re.search(full_pattern, rationale, re.DOTALL | re.IGNORECASE)
            if full_match:
                text = full_match.group(0)
                # ???????????????????
                score_match = re.search(r'[:??]\s*([-]?[01])\s*??', text, re.DOTALL)
                if score_match:
                    try:
                        score = int(score_match.group(1))
                        item_scores[item]["score"] = score
                        item_scores[item]["reason"] = text
                        print(f"?????????????[{item}]: ????={score}, ?????=\"{text[:50]}...\"")
                    except (ValueError, IndexError) as e:
                        print(f"??????????????[{item}]???: {e}")
                else:
                    # ????????????????????
                    if "????" in text or "????" in text or "????" in text or "????" in text or "???" in text or "????" in text or "???????" in text:
                        score = 1
                    elif "????" in text or "???" in text or "????" in text or "?????" in text or "?????" in text or "???????" in text:
                        score = 0  # ???0??
                    else:
                        score = 0
                    
                    item_scores[item]["score"] = score
                    item_scores[item]["reason"] = text
                    print(f"????????????[{item}]: ????={score}, ?????=\"{text[:50]}...\"")
            else:
                # ?????????????????????????????????
                last_pattern = rf'{item_index_in_output}\.\s*.*?([-]?[01])\s*??'
                last_match = re.search(last_pattern, rationale, re.DOTALL | re.IGNORECASE)
                if last_match:
                    try:
                        score = int(last_match.group(1))
                        item_scores[item]["score"] = score
                        item_scores[item]["reason"] = last_match.group(0)
                        print(f"???????????[{item}]: ????={score}, ??????=\"{last_match.group(0)}\"")
                    except (ValueError, IndexError) as e:
                        print(f"????????[{item}]???: {e}")
    
    # ??????????????????
    print("??????????:")
    for item in items:
        print(f"  {item}: {item_scores[item]['score']}??")
    
    # ??????????
    total_score = sum(item_scores[item]["score"] for item in item_scores)
    print(f"???????: {total_score}")
    
    return item_scores

def analyze_stock(stock_index, stock, stock_tech_info, client, output_queue, output_file_lock, output_file, original_headers, model_log_file, max_retries=3, retry_delay=5, industry_tech_info=None, stock_to_industry=None, stock_resonance_info=None):
    """???????????????????????????????????"""
    stock_code = stock.get('????', '???')
    stock_name = stock.get('????', '???')
    
    # ??????????????????????
    tech_info = stock_tech_info.get(stock_code, "")
    
    # ?????????????????????????????????????????????????
    history_info = get_historical_data(stock_code, original_headers, current_stock=stock, use_simulation=False)
    
    # ????????????????????????????Z??
    raw_history_info = ""
    try:
        # ??????????????????????
        all_stock_files = glob.glob(os.path.join("D:\\stock\\tdxdata", "???????*.xls")) + glob.glob(os.path.join("D:\\stock\\tdxdata", "???????*.txt"))
        
        # ???????????????????????????????????????????
        if not all_stock_files:
            print("?????????????????????????????????????...")
            # ???????'????A??*'???
            all_stock_files = glob.glob(os.path.join("D:\\stock\\tdxdata", "????A??*.xls")) + glob.glob(os.path.join("D:\\stock\\tdxdata", "????A??*.txt"))
            # ???????'??????*'???
            if not all_stock_files:
                all_stock_files = glob.glob(os.path.join("D:\\stock\\tdxdata", "??????*.xls")) + glob.glob(os.path.join("D:\\stock\\tdxdata", "??????*.txt"))
            # ??????????????????
            if not all_stock_files:
                all_stock_files = glob.glob(os.path.join("D:\\stock\\tdxdata", "??? A??*.xls")) + glob.glob(os.path.join("D:\\stock\\tdxdata", "??? A??*.txt"))
                all_stock_files += glob.glob(os.path.join("D:\\stock\\tdxdata", "???? A??*.xls")) + glob.glob(os.path.join("D:\\stock\\tdxdata", "???? A??*.txt"))
        
        # ??????????????????????????????????
        def extract_date(filename):
            # ??????????????????
            # 1. ????????"???????20250101.xls"
            date_match = re.search(r'???????(\d{8})', os.path.basename(filename))
            if date_match:
                date_str = date_match.group(1)
                return datetime.strptime(date_str, '%Y%m%d')
            
            # 2. ????A??????"????A??20250101.xls"
            date_match = re.search(r'????A??(\d{8})', os.path.basename(filename))
            if date_match:
                date_str = date_match.group(1)
                return datetime.strptime(date_str, '%Y%m%d')
                
            # 3. ???????????"??????20250101.xls"
            date_match = re.search(r'??????(\d{8})', os.path.basename(filename))
            if date_match:
                date_str = date_match.group(1)
                return datetime.strptime(date_str, '%Y%m%d')
            
            # 4. ??????????"??? A??20250101.xls" ?? "???? A??20250101.xls"
            date_match = re.search(r'(???|????)\s+A??(\d{8})', os.path.basename(filename))
            if date_match:
                date_str = date_match.group(2)
                return datetime.strptime(date_str, '%Y%m%d')
            
            # 5. ?????????????????????????8?????????????
            date_match = re.search(r'(\d{8})', os.path.basename(filename))
            if date_match:
                date_str = date_match.group(1)
                try:
                    return datetime.strptime(date_str, '%Y%m%d')
                except ValueError:
                    pass
                    
            return datetime.min
        
        # ??????5?????
        recent_files = sorted(all_stock_files, key=extract_date, reverse=True)[:5]
        
        # ?????????????????????
        for file_path in recent_files:
            try:
                file_date = extract_date(file_path)
                date_str = file_date.strftime('%Y-%m-%d')
                
                # ?????????????????Excel???
                is_real_excel = False
                if file_path.lower().endswith('.xls'):
                    try:
                        with open(file_path, 'rb') as f:
                            header = f.read(8)
                            # ?????????????Excel??????
                            is_real_excel = header.startswith(b'\xD0\xCF\x11\xE0') or header.startswith(b'PK\x03\x04')
                    except Exception:
                        is_real_excel = False
                
                stock_row = None
                
                if is_real_excel:
                    try:
                        if file_path.lower().endswith('.xls'):
                            df = pd.read_excel(file_path, engine='xlrd')
                        else:
                            df = pd.read_excel(file_path, engine='openpyxl')
                            
                        # ??????????
                        for _, row in df.iterrows():
                            if '????' in df.columns and pd.notna(row['????']):
                                row_code = clean_stock_code(row['????'])
                                if row_code == stock_code:
                                    stock_row = row.to_dict()
                                    break
                    except Exception as e:
                        print(f"??Excel???????????????: {e}")
                else:
                    # ??????????Excel??????????????????
                    try:
                        with open(file_path, 'r', encoding='gbk', errors='ignore') as f:
                            lines = f.readlines()
                        
                        if not lines:
                            continue
                        
                        # ???????
                        file_header = [col.strip() for col in lines[0].strip().split('\t') if col.strip()]
                        if not file_header:
                            continue
                            
                        # ??????
                        for line in lines[1:]:
                            if not line.strip():
                                continue
                            
                            values = [val.strip() for val in line.strip().split('\t')]
                            if len(values) >= len(file_header):
                                row_data = dict(zip(file_header, values))
                                if '????' in row_data and clean_stock_code(row_data['????']) == stock_code:
                                    stock_row = row_data
                                    break
                    except Exception as e:
                        print(f"????????????????????: {e}")
                
                # ???????????????????????????????Z?????
                if stock_row:
                    data_str = f"??{date_str}?????????\n"
                    data_str += f"????: {stock_code}\n"
                    data_str += f"????: {stock_name}\n"
                    
                    # ?????????
                    for key, value in stock_row.items():
                        if value and not pd.isna(value):
                            key_lower = key.lower()
                            if '????' in key_lower or '????z' in key_lower.replace(' ', '') or '????????' in key_lower or '?????' in key_lower or '????' in key_lower or '????' in key_lower:
                                data_str += f"{key}: {value}\n"
                    
                    raw_history_info += data_str + "\n\n"
            except Exception as e:
                print(f"?????????? {file_path} ?????: {e}")
        
        # ???????????????????????????????????????????
        if not raw_history_info and history_info:
            recent_dates = []
            # ???????????????????????????
            for line in history_info.split('\n'):
                if " | " in line and not line.startswith("????") and not line.startswith("---"):
                    parts = line.split(" | ")
                    if len(parts) >= 5:
                        date_str = parts[0].strip()
                        try:
                            # ?????????????????
                            datetime.strptime(date_str, '%Y-%m-%d')
                            recent_dates.append(date_str)
                        except:
                            pass
            
            # ???????????????????????????
            for date_str in recent_dates:
                data_str = f"??{date_str}?????????\n"
                data_str += f"????: {stock_code}\n"
                data_str += f"????: {stock_name}\n"
                
                # ??????????????????????Z???
                for key, value in stock.items():
                    if key in ['????', '????Z', '????????', '?????', '????', '????']:
                        data_str += f"{key}: {value}\n"
                
                raw_history_info += data_str + "\n\n"
            
    except Exception as e:
        print(f"????????????????: {e}")
    
    # ???????????????????????????Z - ????Wstock_tech.py????????
    enhanced_tech_info = enhance_tech_info(tech_info, stock, raw_history_info)
    
    # ?????????????????
    industry_info = ""
    industry_name = ""
    full_industry_name = ""
    
    if stock_to_industry and stock_code in stock_to_industry:
        full_industry_name = stock_to_industry[stock_code]
        # ????????????????????????
        industry_name = full_industry_name.split('-')[-1] if '-' in full_industry_name else full_industry_name
        
        if industry_tech_info and industry_name in industry_tech_info:
            industry_info = industry_tech_info[industry_name]
    
    # ????????????????????
    resonance_info = ""
    resonance_count = 0
    if stock_resonance_info and stock_code in stock_resonance_info:
        resonance_data = stock_resonance_info[stock_code]
        industry = resonance_data.get('industry', full_industry_name)
        resonance_count = resonance_data.get('count', 0)
        
        if industry:
            resonance_info = f"\n??????????????\n?? {industry} ??????? {resonance_count} ???????????????????\n"
    elif full_industry_name:
        # ????????????????????????????
        resonance_info = f"\n??????????????\n?? {full_industry_name} ??????? 0 ???????????????????\n"
    
    # === ?????????????????????????????? ===
    raw_market_data = {
        'basic_info': {
            'code': stock_code,
            'name': stock_name,
            'industry_full': full_industry_name,
            'industry_short': industry_name
        },
        'current_stock_data': dict(stock),  # ??????????????????
        'fund_flow_data': history_info,  # ????????????
        'technical_indicators': enhanced_tech_info,  # ???????????????????
        'original_technical': tech_info,  # ?????????????
        'raw_historical_data': raw_history_info,  # ?????????
        'industry_technical': industry_info,  # ??????????
        'resonance_data': {
            'info': resonance_info,
            'count': resonance_count,
            'industry': resonance_data.get('industry', '') if stock_resonance_info and stock_code in stock_resonance_info else ''
        }
    }
    
    # ????prompt??????????????????????
    prompt = f"""????????????????????????????????????????????????????????6???????????????????1~5??????????????????????g?????"???????"??"???????"???????????????????????????????????????????????????????????????????"???????"????????"???????"???????????????????1?????????????

0. ????????????????????"??????????????"??"????????????"??"????????????"??"??????????"?????????????????(30?????)??????(?????)????????????????????????????????????????????????"???????"????"???????"???????????????1?????????"???????"?????????????"DIF????????""RSI6???50""MA5???????""DIF?????????""MACD??????????""MACD???""MA5???MA10""????????????MA5???""RSI6?????????50????""????????MAVOL5????????""4????????K???MAVOL5????""4????????K???VOL????MAVOL5""MACD???????????""MACD??????????""????????""????????""MACD?????""(30?????)???????????????????????2??""(30?????)?????????????2%"???????"???????"??????????????????????????????????????"?????????????2%"???"?????????????10%"????"??????????????1%"???"??????????????5%"??
1. ?????????????????"????????????"??"??????????"????????"???????"??"???????"?????"0.?????????????"????????????????????????????"?????????????2%"???"?????????????10%"????"??????????????1%"???"??????????????5%"??
2. ??????????????"????????"?????"???????"?????????????????5????????'???????????'??'????????'???????????????6????"???????"???????????????5????????'???????????'??'????????'?????????????6???
3. ??????????????"??????????"?????"???????"?????????????????????????????????????"????"??"????"?????????"???????"????????????????????????????????????"????"????????????????????????????????????????????????????
4. ??????????????????????????????"????????????"??"??????????"??"????????????"??"??????????"???"???????????"?????"???????"???????????????L?????(?????"0.?????????????"??????????????????????????????????????)??????????????????2??????????????????"???????"???????????????L??????
5. ??????????????"???????"??"???????"????????"???????"??????????????????????????????"???????"????????????????????????????????
????????????"???????"????"???????"?????????1???"???????"???????"???????"??????(0??)????????????????????

????????
1. ??????????????????????6???????????????????????
2. ??0??5????????????????????
3. ????????????????????????"X. ????????Y???",????Y?????0??1
4. ??????????????"1. ?????????????1????????..." ?? "4. ?????????????????0??????..."
5. ??????????????????0??1???????????????????????
6. ??????"???"???????? "???????"??xxx, "???????":xxx ???????????

?????????
{stock_code}
??????????
{stock_name}
"""
    
    # ????????????
    if history_info:
        prompt += f"????????????\n{history_info}\n"
    
    # ????????????? - ???????????????
    prompt += f"?????????\n{enhanced_tech_info}\n\n"
    
    # ?????????????????
    if industry_info:
        prompt += f"????????????????\n{industry_info}\n"
        
        # ?????????????????????????????????????
        if resonance_info:
            prompt += resonance_info
    
    # ???????????
    retries = 0
    last_error = None
    
    while retries < max_retries:
        try:
            # ???????????
            start_time = time.time()
            
            # ????????????? - ???????????????????????model_log_file???None??
            if model_log_file:
                with output_file_lock:
                    with open(model_log_file, "a", encoding="utf-8") as logfile:
                        logfile.write(f"="*50 + "\n")
                        logfile.write(f"???????? - ???: {stock_code}({stock_name})\n")
                        logfile.write(f"="*50 + "\n")
                        
                        # === ????????????????????????? ===
                        logfile.write(f"????????????????????\n")
                        logfile.write(f"??????????:\n")
                        logfile.write(f"  ????: {raw_market_data['basic_info']['code']}\n")
                        logfile.write(f"  ????: {raw_market_data['basic_info']['name']}\n")
                        logfile.write(f"  ???????(????): {raw_market_data['basic_info']['industry_full']}\n")
                        logfile.write(f"  ???????(???): {raw_market_data['basic_info']['industry_short']}\n")
                        
                        logfile.write(f"\n??????????(???????):\n")
                        for key, value in raw_market_data['current_stock_data'].items():
                            logfile.write(f"  {key}: {value}\n")
                        
                        logfile.write(f"\n????????????:\n")
                        if raw_market_data['fund_flow_data']:
                            logfile.write(f"{raw_market_data['fund_flow_data']}\n")
                        else:
                            logfile.write("???????????\n")
                        
                        logfile.write(f"\n?????????:\n")
                        if raw_market_data['raw_historical_data']:
                            logfile.write(f"{raw_market_data['raw_historical_data']}\n")
                        else:
                            logfile.write("???????????\n")
                        
                        logfile.write(f"\n???????????????(??):\n")
                        if raw_market_data['original_technical']:
                            logfile.write(f"{raw_market_data['original_technical']}\n")
                        else:
                            logfile.write("???????????????\n")
                        
                        logfile.write(f"\n???????????????(?????):\n")
                        if raw_market_data['technical_indicators']:
                            logfile.write(f"{raw_market_data['technical_indicators']}\n")
                        else:
                            logfile.write("????????????????\n")
                        
                        logfile.write(f"\n??????????????:\n")
                        if raw_market_data['industry_technical']:
                            logfile.write(f"{raw_market_data['industry_technical']}\n")
                        else:
                            logfile.write("????????????????\n")
                        
                        logfile.write(f"\n???????????:\n")
                        logfile.write(f"  ???????: {raw_market_data['resonance_data']['info']}\n")
                        logfile.write(f"  ????????: {raw_market_data['resonance_data']['count']}\n")
                        logfile.write(f"  ???????: {raw_market_data['resonance_data']['industry']}\n")
                        
                        logfile.write(f"\n???????????????????????\n")
                        logfile.write(f"{prompt}\n")
                        logfile.write(f"-"*50 + "\n")
            
            # ????????????????? - ????????API
            # ?????? client.send ??????????? API
            result = client.send(prompt)
            
            # ?????????????
            end_time = time.time()
            elapsed = end_time - start_time
            
            # ????????????????
            with output_file_lock:
                with open(model_log_file, "a", encoding="utf-8") as logfile:
                    logfile.write(f"??????: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} (???: {elapsed:.2f}??)\n")
                    logfile.write(f"??????:\n{result}\n")
                    logfile.write(f"="*50 + "\n\n")
            
            # ???????????????
            if not validate_model_response(result):
                retries += 1
                log_message = f"??{stock_index+1}??????????????? (???? {retries}/{max_retries}): {stock_code}({stock_name})"
                print(log_message)
                
                with output_file_lock:
                    with open(output_file, "a", encoding="utf-8") as fout:
                        fout.write(f"{log_message}\n???????: {result}\n")
                
                if retries < max_retries:
                    time.sleep(retry_delay)
                    continue
                else:
                    with output_file_lock:
                        with open(output_file, "a", encoding="utf-8") as fout:
                            fout.write(f"??{stock_index+1}????????{max_retries}??????????: {stock_code}({stock_name})\n\n")
                    return None, None, None
            
            # ???????????
            print(f"??{stock_index+1}??????????????: {elapsed:.2f}??")
            rationale = result.strip()
            
            # ?????????????????
            print(f"?????????? {stock_code} ??????...")
            item_scores = parse_item_scores(rationale)
            
            # ???????????????
            score = sum(item_scores[item]["score"] for item in item_scores)
            
            # ?????????????????????????
            for item in item_scores:
                if item_scores[item]["score"] not in [0, 1]:
                    print(f"???????? {item} ????? {item_scores[item]['score']} ???????????????0")
                    item_scores[item]["score"] = 0  # ?????0
            
            # ??????????
            score = sum(item_scores[item]["score"] for item in item_scores)
            print(f"??? {stock_code} ??????: {score}")
            
            # ????????????????????????????????
            with output_file_lock:
                with open(output_file, "a", encoding="utf-8") as fout:
                    fout.write(f"???????: {stock_code}({stock_name})\n")
                    fout.write(f"="*60 + "\n")
                    
                    # === ?????????????????????????????????????????????? ===
                    fout.write(f"?????????????????\n")
                    fout.write(f"??????????:\n")
                    fout.write(f"  ????: {raw_market_data['basic_info']['code']}\n")
                    fout.write(f"  ????: {raw_market_data['basic_info']['name']}\n")
                    fout.write(f"  ???????(????): {raw_market_data['basic_info']['industry_full']}\n")
                    fout.write(f"  ???????(???): {raw_market_data['basic_info']['industry_short']}\n")
                    
                    fout.write(f"\n??????????(???????):\n")
                    for key, value in raw_market_data['current_stock_data'].items():
                        fout.write(f"  {key}: {value}\n")
                    
                    fout.write(f"\n????????????:\n")
                    if raw_market_data['fund_flow_data']:
                        fout.write(f"{raw_market_data['fund_flow_data']}\n")
                    else:
                        fout.write("???????????\n")
                    
                    fout.write(f"\n?????????:\n")
                    if raw_market_data['raw_historical_data']:
                        # ????????????????
                        raw_data = raw_market_data['raw_historical_data']
                        if len(raw_data) > 2000:
                            fout.write(f"{raw_data[:2000]}...\n[??????????????????????????????: {model_log_file}]\n")
                        else:
                            fout.write(f"{raw_data}\n")
                    else:
                        fout.write("???????????\n")
                    
                    fout.write(f"\n???????????????(?????):\n")
                    if raw_market_data['technical_indicators']:
                        # ????????????????????????
                        tech_data = raw_market_data['technical_indicators']
                        if len(tech_data) > 5000:
                            fout.write(f"{tech_data[:5000]}...\n[????????????????????????????????: {model_log_file}]\n")
                        else:
                            fout.write(f"{tech_data}\n")
                    else:
                        fout.write("????????????????\n")
                    
                    fout.write(f"\n??????????????:\n")
                    if raw_market_data['industry_technical']:
                        # ????????????????
                        industry_data = raw_market_data['industry_technical']
                        if len(industry_data) > 3000:
                            fout.write(f"{industry_data[:3000]}...\n[???????????????????????????????????: {model_log_file}]\n")
                        else:
                            fout.write(f"{industry_data}\n")
                    else:
                        fout.write("????????????????\n")
                    
                    fout.write(f"\n???????????:\n")
                    fout.write(f"  ???????: {raw_market_data['resonance_data']['info']}\n")
                    fout.write(f"  ????????: {raw_market_data['resonance_data']['count']}\n")
                    fout.write(f"  ???????: {raw_market_data['resonance_data']['industry']}\n")
                    
                    fout.write(f"\n???????????????????????\n")
                    if len(prompt) > 2000:
                        fout.write(f"{prompt[:2000]}...\n[??????????????????????????: {model_log_file}]\n")
                    else:
                        fout.write(f"{prompt}\n")
                    
                    fout.write(f"\n??????????????\n{result}\n")
                    fout.write(f"\n???????????{score}\n")
                    scores_str = ", ".join([f"{item}??{item_scores[item]['score']}??" for item in item_scores])
                    fout.write(f"?????????{scores_str}\n")
                    fout.write(f"="*60 + "\n\n")
            
            # ???????????????????????????????
            output_queue.put((stock_index, stock_code, stock_name, score, rationale, item_scores))
            return score, rationale, item_scores
            
        except Exception as e:
            last_error = e
            retries += 1
            error_msg = f"??{stock_index+1}???????????? (???? {retries}/{max_retries}): {e}"
            print(error_msg)
            
            # ??????????????
            with output_file_lock:
                with open(output_file, "a", encoding="utf-8") as fout:
                    fout.write(f"{error_msg}\n")
                    fout.write(f"????????: {traceback.format_exc()}\n\n")
                
                # ???????????????
                with open(model_log_file, "a", encoding="utf-8") as logfile:
                    logfile.write(f"???????: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    logfile.write(f"???????: {e}\n")
                    logfile.write(f"????????: {traceback.format_exc()}\n")
                    logfile.write(f"="*50 + "\n\n")
            
            if retries < max_retries:
                # ????????????????
                time.sleep(retry_delay)
                # ?????????????????????
                retry_delay = retry_delay * 1.5
            else:
                with output_file_lock:
                    with open(output_file, "a", encoding="utf-8") as fout:
                        fout.write(f"??{stock_index+1}????????{max_retries}??????????: {stock_code}({stock_name})\n\n")
                return None, None, None
    
    # ????????????
    print(f"??{stock_index+1}????????????????: {last_error}")
    return None, None, None

def process_results_thread(output_queue, total_stocks, stock_scores, stock_rationales, stock_item_scores):
    """???????????????????"""
    processed_count = 0
    while processed_count < total_stocks:
        try:
            stock_index, stock_code, stock_name, score, rationale, item_scores = output_queue.get(timeout=1)
            
            if score is not None:
                stock_scores[stock_code] = score
                # ???????????????????????????
                if rationale is not None:
                    stock_rationales[stock_code] = rationale
                if item_scores is not None:
                    stock_item_scores[stock_code] = item_scores
                print(f"??{stock_index+1}?????????{stock_code}({stock_name}) = {score}")
            
            processed_count += 1
            output_queue.task_done()
        except Exception:
            # ??????????????????????
            continue

def export_to_excel(stocks, code2name, stock_scores, stock_item_scores):
    """??????????????Excel???"""
    # ???????????
    data = []
    
    # ??????????????
    sorted_stocks = sorted(stock_scores.items(), key=lambda x: x[1], reverse=True)
          
    # ???????????????????parse_item_scores????????????
    items = ["?????????????", "????????????", "?????????", "?????????", "????????????????", "??????????", "?????????"]
    
    # ????????????????
    excel_debug_file = "excel_export_debug.txt"
    with open(excel_debug_file, "w", encoding="utf-8") as debug_file:
        debug_file.write("Excel???????????\n")
        debug_file.write("=" * 50 + "\n\n")
    
    for rank, (code, score) in enumerate(sorted_stocks, 1):
        name = code2name.get(code, "???")
        
        # ????????????????????????????????
        item_scores = stock_item_scores.get(code, {})
        
        # ??????????
        with open(excel_debug_file, "a", encoding="utf-8") as debug_file:
            debug_file.write(f"???: {code}({name}), ???: {score}\n")
            if code in stock_item_scores:
                debug_file.write(f"???????????: {', '.join(stock_item_scores[code].keys())}\n")
                # ???????????????
                for item in items:
                    if item in stock_item_scores[code]:
                        debug_file.write(f"  ?? ??????: {item}, ????={stock_item_scores[code][item]['score']}\n")
                    else:
                        debug_file.write(f"  ?? ????????: {item}\n")
            else:
                debug_file.write(f"?????????item_scores??\n")
            debug_file.write("-" * 50 + "\n")
        
        row = {
            "????": rank,
            "????": code,
            "????": name,
            "???": score
        }
        
        # ????????????????
        for item in items:
            if code in stock_item_scores and item in stock_item_scores[code]:
                row[f"{item}????"] = stock_item_scores[code][item]["score"]
                row[f"{item}????"] = stock_item_scores[code][item]["reason"]
            else:
                row[f"{item}????"] = 0
                row[f"{item}????"] = ""
        
        data.append(row)
    
    # ?????????
    with open(excel_debug_file, "a", encoding="utf-8") as debug_file:
        debug_file.write("\n?????????????: " + str(len(data)) + "\n")
        if data:
            debug_file.write("???????? (?????):\n")
            for k, v in data[0].items():
                if not k.endswith("????"):  # ?????????????????
                    debug_file.write(f"  {k}: {v}\n")
        debug_file.write("=" * 50 + "\n")
    
    # ????DataFrame
    df = pd.DataFrame(data)
    
    # ?????Excel???
    excel_file = "?????????.xlsx"
    df.to_excel(excel_file, index=False, engine="openpyxl")
    print(f"\n??????????????Excel???: {excel_file}")
    print(f"Excel?????????????????: {excel_debug_file}")
    return excel_file

def check_and_reconnect(client, max_attempts=3):
    """???????????????????????????"""
    for attempt in range(max_attempts):
        try:
            # ?????????????????
            test_result = client.send("????????")
            if test_result:
                return True
        except Exception as e:
            print(f"?????????? (???? {attempt+1}/{max_attempts}): {e}")
        
        print(f"???????????????????... (???? {attempt+1}/{max_attempts})")
        client.close()
        time.sleep(2)  # ???????????????
        
        if client.connect():
            print("????????????????????")
            return True
    
    print("??????????????????????????????")
    return False

def main():
    print("=== ?????????????? ===")
    
    # ??????????????????????
    file_path = input("??????????????????????? D:/stock/tdxdata/??????20250416.txt ??: ").strip()
    if not file_path:
        print("??????????????????????")
        return
    
    # ????????????????
    model_log_file = "local_score_log.txt"
    
    try:
        header, stocks, name_col = read_stock_data(file_path)
        if not stocks:
            print("????????????????????")
            return
        print(f"?????? {len(stocks)} ???????")
        
        # ?????????????1000???????????
        if len(stocks) > 1000:
            print(f"???????({len(stocks)})????1000??????????")
            model_log_file = None
        else:
            # ?????????????
            with open(model_log_file, "w", encoding="utf-8") as logfile:
                logfile.write(f"?????????????????\n")
                logfile.write("???????????\n")
                logfile.write("1. ???????????????????????????????????????????????????????????\n")
                logfile.write("2. ?????/?????????????????Prompt??\n")
                logfile.write("3. ????????????????\n")
                logfile.write("4. ???????????????\n")
                logfile.write("="*50 + "\n\n")
            
            print(f"???????????????????: {model_log_file}")
            print("?????????????????????????????????????")
    except Exception as e:
        print(f"?????????????: {e}")
        return
    
    # ?????????????????
    print("?????????????????:")
    print("1. DeepSeek (????)")
    print("2. ???????????? (????API)")
    print("3. ???????????? (????API)")
    print("4. ??????????")
    model_choice = input("????????? (1??2??3 ?? 4??????1): ").strip()
    
    # ??????DeepSeek
    if not model_choice or model_choice == "1":
        model_type = "deepseek"
        print("????????DeepSeek????????...")
        client = DeepSeekClient()  # ????????API
        if not client.connect():
            print("????DeepSeek???????????")
            return
        print("????DeepSeek??????????")
    elif model_choice == "2":
        model_type = "doubao-thinking"
        print("??????????????????????...")
        # ?????Wstock_compare.py????????API????????????
        client = VolcesClient(api_key="a8eefb00-e55d-4ac5-9fb9-04669e5c6385", model="doubao-seed-1-6-thinking-250615")
        if not client.connect():
            print("?????????????????????")
            return
        print("????????????????????")
    elif model_choice == "3":
        model_type = "doubao-seed"
        print("??????????????????????...")
        # ???Wstock_score copy.py????API????????????
        client = VolcesClient(api_key="e1283034-c29c-41bc-a10f-14e32fbe828b", model="doubao-seed-1-6-250615")
        if not client.connect():
            print("?????????????????????")
            return
        print("????????????????????")
    else:
        model_type = "local-calculation"
        print("??????????????")
        client = None  # ???????????????
    
    # ????????????
    if model_log_file:
        with open(model_log_file, "a", encoding="utf-8") as logfile:
            logfile.write(f"???????????: {model_type}\n")
            logfile.write("="*50 + "\n\n")
    
    # ????????????????????????????????????????
    resonance_file_path = input("?????????????????????????????? D:/stock/tdxdata/ggjs.blk ??: ").strip()
    resonance_data = {}
    if resonance_file_path:
        try:
            print(f"????????????????????: {resonance_file_path}")
            # ?????????????????
            resonance_header, resonance_stocks, resonance_name_col = read_stock_data(resonance_file_path)
            
            # ????????????????????????
            for stock in resonance_stocks:
                if '????????' in stock and stock['????????'] and '????' in stock:
                    industry = stock['????????']
                    code = stock['????']
                    if industry not in resonance_data:
                        resonance_data[industry] = []
                    resonance_data[industry].append(code)
            
            print(f"???????????????????????? {len(resonance_stocks)} ??????{len(resonance_data)} ?????")
        except Exception as e:
            print(f"????????????????????: {e}")
            print("???????????????????????????")
            resonance_data = {}
    else:
        print("???????????????????????????????????????????????")
    
    # ??????????????????????????
    print("??????????????????????...")
    stock_tech_info = {}
    
    # ????: ????????????????
    industry_tech_info = {}
    # ????: ????????????????
    stock_to_industry = {}
    # ????: ??????????????????
    stock_resonance_info = {}
    
    for stock in stocks:
        stock_code = stock.get('????', '')
        if stock_code:
            # ???????????
            max_tech_retries = 3
            tech_retry_count = 0
            tech_success = False
            
            while tech_retry_count < max_tech_retries and not tech_success:
                try:
                    if tech_retry_count > 0:
                        print(f"????????? {stock_code} ???????? (??{tech_retry_count}??)")
                    
                    # ??????????????????????????30????MACD??????????????????
                    print(f"?????????? {stock_code} ??????...")
                    stock_tech.download_all_data_for_stock(stock_code)
                    
                    # ??????analyze_stock??????????
                    tech_info = stock_tech.analyze_stock(stock_code)
                    stock_tech_info[stock_code] = tech_info
                    print(f"????????? {stock_code} ????????")
                    tech_success = True
                    
                    # ????: ??????????????
                    if '????????' in stock and stock['????????']:
                        industry_full_name = stock['????????']
                        # ??????????????????????????????????
                        stock_to_industry[stock_code] = industry_full_name
                        
                        # ??????????????
                        if industry_full_name in resonance_data:
                            # ????????????????????
                            industry_resonance_stocks = resonance_data[industry_full_name]
                            # ?????????????????????????????????
                            resonance_count = sum(1 for code in industry_resonance_stocks if code != stock_code)
                            # ?????????????
                            stock_resonance_info[stock_code] = {
                                'industry': industry_full_name,
                                'count': resonance_count
                            }
                            print(f"??? {stock_code} ?? {industry_full_name} ??????? {resonance_count} ???????")
                        
                        # ????????????????????????????????????????????
                        industry_name = industry_full_name.split('-')[-1] if '-' in industry_full_name else industry_full_name
                        
                        # ?????????????????????????????
                        if industry_name and industry_name not in industry_tech_info:
                            print(f"????????? '{industry_name}' ????????...")
                            try:
                                # ?????????????????
                                industry_result = analyze_industry_properly(industry_name)
                                
                                # ????????????????????????????
                                industry_tech_info[industry_name] = industry_result
                                
                                # ???????????????
                                if isinstance(industry_result, str) and "error" in industry_result:
                                    print(f"???: ??? '{industry_name}' ?????????????: {industry_result}")
                                else:
                                    print(f"????????? '{industry_name}' ????????")
                            except Exception as ie:
                                print(f"?????? '{industry_name}' ??????????: {ie}")
                                industry_tech_info[industry_name] = f"????????????????: {str(ie)}"
                    
                except Exception as e:
                    tech_retry_count += 1
                    if tech_retry_count < max_tech_retries:
                        print(f"?????? {stock_code} ??????????: {e}??????2???????...")
                        time.sleep(2)  # ?????????
                    else:
                        # ????????????????????????
                        stock_tech_info[stock_code] = ""
                        print(f"?????? {stock_code} ??????????: {e}???????????????")
            
    print(f"????? {len(stock_tech_info)} ??????????????")
    print(f"????? {len(industry_tech_info)} ???????????????")
    print(f"????? {len(stock_resonance_info)} ????????????????")
    
    # ???????????????????
    code2name = {stock['????']: stock['????'] for stock in stocks}
    
    # ???????????
    continue_analysis = True
    while continue_analysis:
        # ????????????????
        stock_scores = {stock['????']: 0 for stock in stocks}
        
        # ??????????
        stock_rationales = {stock['????']: "" for stock in stocks}
        
        # ????????????????
        stock_item_scores = {stock['????']: {} for stock in stocks}
        
        # ??????????????????????????????
        output_file = f"stock_scores_{model_type}.txt"
        with open(output_file, "w", encoding="utf-8") as fout:
            pass  # ?????????
        
        # ?????????????????????????
        output_queue = Queue()
        output_file_lock = threading.Lock()
        
        # ??????????????
        result_thread = threading.Thread(
            target=process_results_thread, 
            args=(output_queue, len(stocks), stock_scores, stock_rationales, stock_item_scores)
        )
        result_thread.daemon = True
        result_thread.start()
        
        # ???CPU???????????????????
        cpu_count = os.cpu_count()
        print(f"???CPU??????: {cpu_count}")
        max_workers = min(cpu_count or 16, 32)  # ??????????????32??????????????
        print(f"??? {max_workers} ????????????????????")
        
        # ?????????????????????
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # ??????????
            futures = []
            for i, stock in enumerate(stocks):
                if model_type == "local-calculation":
                    # ???????????
                    future = executor.submit(
                        analyze_stock_locally, i, stock, stock_tech_info,
                        output_queue, output_file_lock, output_file, header,
                        model_log_file,
                        industry_tech_info=industry_tech_info, stock_to_industry=stock_to_industry, stock_resonance_info=stock_resonance_info
                    )
                else:
                    # ??????????????
                    future = executor.submit(
                        analyze_stock, i, stock, stock_tech_info, 
                        client, output_queue, output_file_lock, output_file, header,
                        model_log_file, max_retries=3, retry_delay=5,  # ???????????
                        industry_tech_info=industry_tech_info, stock_to_industry=stock_to_industry, stock_resonance_info=stock_resonance_info  # ??????????
                    )
                futures.append(future)
            
            # ??????????????????????
            total = len(futures)
            completed = 0
            failed = 0
            
            for future in concurrent.futures.as_completed(futures):
                completed += 1
                try:
                    result = future.result()
                    if result[0] is None:  # ??????????
                        failed += 1
                        
                        # ?5????????????????????????? (????????????)
                        if failed > 0 and failed % 5 == 0 and model_type != "local-calculation":
                            print(f"????????????????????????...")
                            if not check_and_reconnect(client):
                                print("????????????????????????????????")
                except Exception as e:
                    print(f"???????????????: {e}")
                    failed += 1
                
                # ???????
                if completed % 5 == 0 or completed == total:
                    success_rate = ((completed - failed) / completed * 100) if completed > 0 else 0
                    print(f"????: {completed}/{total} ({(completed/total*100):.1f}%)???????: {success_rate:.1f}%")
        
        # ????????????????
        output_queue.join()
        
        # ????????????????
        print("\n=== ??????????? ===")
        sorted_stocks = sorted(stock_scores.items(), key=lambda x: x[1], reverse=True)
        valid_results = sum(1 for _, score in sorted_stocks if score != 0)
        print(f"???????????: {valid_results}/{len(stocks)} ({valid_results/len(stocks)*100:.1f}%)")
        
        for rank, (code, score) in enumerate(sorted_stocks, 1):
            name = code2name.get(code, "???")
            print(f"??{rank}??: {code}({name})??????: {score}")
        
        # ??????Excel???
        excel_file = export_to_excel(stocks, code2name, stock_scores, stock_item_scores)
        
        # ??????????????
        if model_log_file:
            with open(model_log_file, "a", encoding="utf-8") as logfile:
                logfile.write(f"\n??????? {model_type} ??????\n")
                logfile.write(f"??????? {len(stocks)} ????????? {valid_results} ?????? {len(stocks) - valid_results} ?\n")
                logfile.write(f"?????????????????????????\n")
                logfile.write(f"- ???????????????????????????\n")
                logfile.write(f"- ??????????????????????\n")
                logfile.write(f"- ??????????????????????????????????\n")
                logfile.write(f"- ????????????????????????????????????\n")
                logfile.write(f"- ??????????????\n")
                logfile.write(f"- ???????????\n")
                logfile.write(f"- ???????????????Prompt???????????????\n")
                logfile.write(f"- ??????????????????????\n")
                logfile.write("="*50 + "\n")
        
        print(f"\n???????????????????????? {output_file}")
        print(f"???{output_file} ??????????????????????????????????")
        print(f"?????????????????Excel???: {excel_file}")
        print(f"????????????????????????????????: {model_log_file}")
        if model_type == "local-calculation":
            print(f"???????????????????: local_calculation_detailed_log.txt")
        
        # ????????????????????????
        switch_model = input("\n?????????????????????????????(y/n): ").strip().lower()
        if switch_model == 'y' or switch_model == 'yes':
            # ???????????????????????
            if client:
                client.close()
            
            # ???????????
            if model_type == "deepseek":
                model_type = "doubao-thinking"
                print("??????????????????????...")
                client = VolcesClient(api_key="a8eefb00-e55d-4ac5-9fb9-04669e5c6385", model="doubao-seed-1-6-thinking-250615")
                if not client.connect():
                    print("?????????????????????????????")
                    return
                print("????????????????????")
            elif model_type == "doubao-thinking":
                model_type = "doubao-seed"
                print("??????????????????????...")
                client = VolcesClient(api_key="e1283034-c29c-41bc-a10f-14e32fbe828b", model="doubao-seed-1-6-250615")
                if not client.connect():
                    print("?????????????????????????????")
                    return
                print("????????????????????")
            elif model_type == "doubao-seed":
                model_type = "local-calculation"
                print("??????????????????")
                client = None
            else:  # local-calculation
                model_type = "deepseek"
                print("????????DeepSeek????????...")
                client = DeepSeekClient()
                if not client.connect():
                    print("????DeepSeek???????????????????")
                    return
                print("????DeepSeek??????????")
            
            # ???????????????
            if model_log_file:
                with open(model_log_file, "a", encoding="utf-8") as logfile:
                    logfile.write(f"\n?????????: {model_type}\n")
                    logfile.write("="*50 + "\n\n")
        else:
            continue_analysis = False
    
    # ????????????????
    if client:
        client.close()
    print(f"????????????????????????????????????: {model_log_file}")
    print("?????????????????????")

def test_enhanced_functions():
    """????????????????????????"""
    try:
        # ??????????
        test_stock_code = "000001"
        print(f"????????? {test_stock_code} ????????...")
        
        # ??????????
        print("1. ???????????????????...")
        stock_tech.download_all_data_for_stock(test_stock_code)
        
        # ??????????
        print("2. ??????????...")
        tech_info = stock_tech.analyze_stock(test_stock_code)
        if not tech_info:
            print("?????????????")
            return
        
        # ????????????????K?????
        print("\n??????????????:")
        # ????K???????????????????
        k_line_format_found = False
        
        # ?????????
        section_start = tech_info.find("1. ???20????????K???")
        if section_start != -1:
            print("???????: '1. ???20????????K???'")
            k_line_format_found = True
        
        # ?????????
        if not k_line_format_found:
            section_start = tech_info.find("1. ???10?????????K??/?????????")
            if section_start != -1:
                print("???????: '1. ???10?????????K??/?????????'")
                k_line_format_found = True
        
        # ?????????? (Zstock_tech_qmt.py???)
        if not k_line_format_found:
            section_start = tech_info.find("1. ???5?????????K??/?????????")
            if section_start != -1:
                print("???????: '1. ???5?????????K??/?????????'")
                k_line_format_found = True
        
        if not k_line_format_found:
            print("??????????????K????")
            return
            
        section_end = tech_info.find("\n\n2.", section_start)
        if section_end == -1:
            section_end = len(tech_info)
        
        kline_section = tech_info[section_start:section_end]
        lines = kline_section.split('\n')
        
        print("K???????5??:")
        for i in range(min(5, len(lines))):
            print(f"  {lines[i]}")
        
        # ??????????
        print("\n3. ??????????...")
        # ???????????????????????????????Z
        mock_stock = {
            "????": test_stock_code,
            "????": "???????",
            "????": "1.35",
            "????Z": "0.86%",
            "????????": "10000000",
            "?????": "8000000",
            "????": "50000000",
            "????": "40000000"
        }
        
        # ??????????????
        raw_history_info = ""
        # ?????????????????????????????
        raw_history_info = """??2025-05-15?????????
????: 000001
????: ???????
????: 1.28
????Z: 0.79%
????????: 8500000
?????: 7000000
????: 48000000
????: 42000000

??2025-05-14?????????
????: 000001
????: ???????
????: 1.15
????Z: 0.92%
????????: 9200000
?????: 7800000
????: 45000000
????: 38000000
"""
        
        history_info = get_historical_data(test_stock_code, current_stock=mock_stock)
        print(f"?????????????????: {len(history_info)}")
        if history_info:
            print("???????????:")
            print(history_info[:300] + "..." if len(history_info) > 300 else history_info)
            
            # ??????????????
            if "????????" in history_info and "????" in history_info and "????" in history_info and "?????" in history_info:
                print("? ?????????????????????????????????????????")
            else:
                print("? ??????????????????????????????????????????")
        
        # ????????????????
        print("\n4. ??????????????...")
        enhanced_tech_info = enhance_tech_info(tech_info, mock_stock, raw_history_info)
        
        # ???????????????????K?????
        k_line_format_found = False
        
        # ?????????
        section_start = enhanced_tech_info.find("1. ???20????????K???")
        if section_start != -1:
            print("????????????: '1. ???20????????K???'")
            k_line_format_found = True
        
        # ?????????
        if not k_line_format_found:
            section_start = enhanced_tech_info.find("1. ???10?????????K??/?????????")
            if section_start != -1:
                print("????????????: '1. ???10?????????K??/?????????'")
                k_line_format_found = True
        
        # ?????????? (Zstock_tech_qmt.py???)
        if not k_line_format_found:
            section_start = enhanced_tech_info.find("1. ???5?????????K??/?????????")
            if section_start != -1:
                print("????????????: '1. ???5?????????K??/?????????'")
                k_line_format_found = True
        
        if not k_line_format_found:
            print("???????????????????K????")
            return
        
        section_end = enhanced_tech_info.find("\n\n2.", section_start)
        if section_end == -1:
            section_end = len(enhanced_tech_info)
        
        kline_section = enhanced_tech_info[section_start:section_end]
        lines = kline_section.split('\n')
        
        print("?????K???????5??:")
        for i in range(min(5, len(lines))):
            print(f"  {lines[i]}")
        
        # ??????????????????????Z
        has_volume_ratio = False
        has_turnover_z = False
        for line in lines:
            if "????=" in line:
                has_volume_ratio = True
                print(f"  ??????????: {line}")
            if "????Z=" in line:
                has_turnover_z = True
                print(f"  ???????Z???: {line}")
        
        if has_volume_ratio and has_turnover_z:
            print("? ?????????????????Z???")
        else:
            print("? ??????????????????Z???")
        
        # ??????????prompt
        print("\n5. ?????????????...")
        prompt = (
            f"?????????\n{test_stock_code}\n??????????\n???????\n\n"
        )
        
        # ????????????
        if history_info:
            prompt += f"????????????\n{history_info}\n"
        
        # ?????????????
        prompt += f"?????????\n{enhanced_tech_info}\n\n"
        
        print(f"??????????300?????:")
        print(prompt[:300] + "...")
        
        # ??????????G???????
        print("\n6. ??????????G???????...")
        test_industry_name = "????"
        print(f"?????? '{test_industry_name}' ????????...")
        
        try:
            # ???????????????????????????
            industry_result = analyze_industry_properly(test_industry_name)
            
            # ??????
            if isinstance(industry_result, str) and "error" in industry_result:
                print(f"????????????????: {industry_result}")
                
                # ?????????????????
                alternative_names = ["??", "????", "????????"]
                for alt_name in alternative_names:
                    print(f"\n???????????: '{alt_name}'")
                    alt_result = analyze_industry_properly(alt_name)
                    if not (isinstance(alt_result, str) and "error" in alt_result):
                        industry_result = alt_result
                        print(f"????????? '{alt_name}' ????????")
                        break
                    else:
                        print(f"?????? '{alt_name}' ???????????")
            
            # ?????????????????
            has_kline = False
            has_macd = False
            has_ma = False
            
            if isinstance(industry_result, str):
                if "???10?????????K??" in industry_result:
                    has_kline = True
                if "????MACD" in industry_result:
                    has_macd = True
                if "???10???????????" in industry_result:
                    has_ma = True
                    
                if has_kline and has_macd and has_ma:
                    print("? ?????????????????????K???MACD????????")
                else:
                    print("?? ?????????????????????????????")
                    print(f"  K?????: {'????' if has_kline else '??'}")
                    print(f"  MACD???: {'????' if has_macd else '??'}")
                    print(f"  ???????: {'????' if has_ma else '??'}")
                
                # ???????????????
                if "???????" in industry_result:
                    print("? ????????????????? '??????? xxx ???????????'")
                else:
                    print("?? ???????????????")
                
                # ???????????????????????
                if "?????????????" in industry_result:
                    if "?: '?????????????'" in industry_result:
                        print("? ????????????????????")
                    else:
                        print("?? ?????????????????")
            else:
                print("?? ??????????????????????????????????")
                
        except Exception as e:
            print(f"??????????G????????????: {e}")
            print(traceback.format_exc())
        
        print("\n????????")
        return True
    except Exception as e:
        print(f"????????????????: {e}")
        print(traceback.format_exc())
        return False

def analyze_industry_properly(industry_name):
    """
    ?????????????????Zstock_industry_tdx.py??main????????
    
    Args:
        industry_name: ??????????'????????'
        
    Returns:
        str: ????????????????
    """
    # ????????????????????? Zstock_industry_tdx ????????????????
    from Zstock_industry_tdx import (
        BLOCK_NAME_CODE_MAP,
        build_block_code_map,
        find_latest_block_file
    )
    # ???????? Zstock_tech_qmttdx ???????????????
    import Zstock_tech_qmttdx_bak as qmttdx
    
    # ????????????????????
    original_industry_name = industry_name
    
    # ????????????????
    block_code_map = BLOCK_NAME_CODE_MAP
    
    # ??????????????????????????????????
    latest_block_file = find_latest_block_file()
    current_date = datetime.now().strftime('%Y%m%d')
    today_block_file = None
    
    # ?????????????????????
    tdxdata_dir = "tdxdata"  # ?????
    today_file_name = f"??????{current_date}.xls"
    candidate_paths = [
        os.path.join("D:\\stock", tdxdata_dir, today_file_name),
        os.path.join(tdxdata_dir, today_file_name),
        os.path.join(".", tdxdata_dir, today_file_name)
    ]
    
    for path in candidate_paths:
        if os.path.exists(path):
            today_block_file = path
            print(f"???????????????: {path}")
            break
    
    # ????????????????????????????
    block_file_to_use = today_block_file or latest_block_file
    
    if block_file_to_use:
        # ???????????????????????????????????
        print(f"???????????: {block_file_to_use}")
        file_block_map = build_block_code_map(block_file_to_use)
        if file_block_map:
            # ????????????????????????
            block_code_map.update(file_block_map)
    
    # ?????input_code?None
    input_code = None
    input_code_type = None  # ??????????????
    matched_block_name = None  # ?????????????????
    
    # ???????????????????????
    if industry_name in block_code_map:
        input_code = block_code_map[industry_name]
        input_code_type = "???"
        matched_block_name = industry_name
        print(f"??????? '{industry_name}' ??????????: {input_code}")
    else:
        # ????????????????
        matched_blocks = []
        for block_name, block_code in block_code_map.items():
            if industry_name in block_name or block_name in industry_name:
                matched_blocks.append((block_name, block_code))
        
        if matched_blocks:
            # ?????????????
            matched_block_name = matched_blocks[0][0]
            input_code = matched_blocks[0][1]
            input_code_type = "???"
            print(f"?????????: {matched_block_name}??????: {input_code}")
        
        # ????????????????????????????????????
        if not input_code and len(industry_name) == 6 and industry_name.isdigit():
            input_code = industry_name
            input_code_type = "???"
            print(f"???????????: {input_code}")
    
    if not input_code:
        # ????????????????
        # ?????????????????
        if len(industry_name) <= 4:  # ??????????????????
            # ????????????????
            for prefix in ['880', '881', '399']:
                # ??????????????
                candidate_code = f"{prefix}{industry_name[:3].zfill(3)}"
                print(f"?????????????: {candidate_code}")
                
                # ???????????????????????????????????
                input_code = candidate_code
                input_code_type = "???"
                matched_block_name = industry_name  # ????????????
                break
    
    if not input_code:
        error_msg = f"?????????? '{industry_name}' ??????????"
        print(error_msg)
        return {"error": error_msg}
    
    # ???? Zstock_tech_qmttdx.analyze_stock ????????
    try:
        # ??????????????????????????? 'tdx'????????? 'miniqmt'
        data_source = 'tdx' if input_code_type == '???' else 'miniqmt'

        # ??????????????????????????????
        result = qmttdx.analyze_stock(input_code, data_source=data_source)
        
        # ???result??????????????????????????
        if isinstance(result, str):
            # ???????????? "{code} ???????????" ??I? "??????? xxx ???????????"
            display_name = matched_block_name or original_industry_name
            old_header = f"{input_code} ?????????????"
            # ???????? "?????? {code} ?????????????"
            old_header_alt = f"?????? {input_code} ?????????????"
            new_header = f"??????? {display_name} ?????????????"
            result = result.replace(old_header_alt, new_header)
            result = result.replace(old_header, new_header)
            
            # ???????"?????????????"?????????????
            if "?????????????" in result:
                explanation = "\n?: '?????????????' ???????????????????????????????????????????????????????????"
                # ??????????????
                result += explanation
                
        return result
    except Exception as e:
        error_msg = f"??????? '{industry_name}' (????: {input_code}) ?????: {str(e)}"
        print(error_msg)
        return {"error": error_msg}

def _extract_industry_signals(industry_info):
    """??????????????????????????????????????????????"""
    if not industry_info:
        return {}, ""
    
    # ?????????? - ?????????
    industry_name = "??????"
    import re
    
    # ????????????????????????Zstock_tech_qmttdx.py??????
    def extract_industry_name_from_content(data):
        patterns = [
            # ???1: "??????? ??? ???????????"???????QMT?????
            r'???????\s+([^\s??]+)\s*????????',
            # ???2: "??? ?????????????"????QMT?????
            r'^([^\s]+)\s+?????????????',
            # ???3: "????????????????\n??????? ??? ???????????"
            r'????????????????[^\n]*\n[^\n]*???????\s+([^\s??]+)',
            # ???4: "?????? '???' (????: 881128) ?????????????"???????????
            r"??????\s*'([^']+)'\s*\(????:\s*\d+\)",
            # ???5: "?????? '???' ?????????????"???????????
            r"??????\s*'([^']+)'\s*????????",
            # ???6: "?????? ??? (????: 881128)"??????????????????
            r"??????\s+([^\s\(]+)\s*\(????:",
            # ???7: ??????????????????????????????
            r"???\s*'([^']+)'",
            # ???8: ???????????????????????"????"?????????????
            r"'([^']+)'\s*\(????:",
        ]
        
        for pattern in patterns:
            match = re.search(pattern, data, re.MULTILINE)
            if match:
                name = match.group(1).strip()
                # ?????????????881128??
                if name.isdigit():
                    continue
                if name and name != "":
                    return name
        return "??????"
    
    # ??????????
    industry_name = extract_industry_name_from_content(industry_info)
    
    # ???????????????????????
    signal_section = ""
    start_marker = "??????????????????"
    start_idx = industry_info.find(start_marker)
    if start_idx != -1:
        # ??"??????????????????"???????????
        signal_section = industry_info[start_idx:]
    else:
        # ?????????????"??????????"
        start_marker = "??????????"
        start_idx = industry_info.find(start_marker)
        if start_idx != -1:
            signal_section = industry_info[start_idx:]
        else:
            # ??????????
            signal_section = industry_info
    
    # ?????????????????????????
    unique_markers = []
    
    # ???MACD?????????????????????????
    macd_values = re.findall(r'DIF[?=:\s]*([-]?[0-9]+\.?[0-9]*)', signal_section)
    if macd_values:
        unique_markers.append(f"DIF?:{','.join(macd_values[:3])}")  # ???3?????????
    
    # ?????????????
    ma_values = re.findall(r'MA5[=:\s]*([0-9]+\.?[0-9]*)', signal_section)
    if ma_values:
        unique_markers.append(f"MA5?:{','.join(ma_values[:3])}")
    
    # ???RSI???????
    rsi_values = re.findall(r'RSI6[=:\s]*([0-9]+\.?[0-9]*)', signal_section)
    if rsi_values:
        unique_markers.append(f"RSI6?:{','.join(rsi_values[:3])}")
    
    # ???????????
    vol_values = re.findall(r'VOL[=:\s]*([0-9]+)', signal_section)
    if vol_values:
        unique_markers.append(f"?????:{','.join(vol_values[:2])}")
    
    # ??????????
    unique_signature = f"???:{industry_name};" + ";".join(unique_markers)
    
    # ---------- ????????????????????????????? ----------
    def _count_signals(sig_list, text):
        """?????????????????????????????????????????????"""
        # ???????????????????????????????
        sorted_signals = sorted(set(sig_list), key=len, reverse=True)  # ???set???
        
        # ????????????????????
        matched_signals = []
        temp = text  # ????????????????????I??????????????????
        
        for s in sorted_signals:
            # ?????????????????
            import re
            matches = list(re.finditer(re.escape(s), temp))
            for match in matches:
                start, end = match.span()
                # ??????????????????????????????
                overlapped = False
                for matched_start, matched_end, matched_signal in matched_signals:
                    if not (end <= matched_start or start >= matched_end):  # ?????
                        overlapped = True
                        break
                
                if not overlapped:
                    matched_signals.append((start, end, s))
        
        return len(matched_signals)
    # ----------------------------------------------------------
    
    signal_counts = {}
    
    # ?????????
    start_signals = [
        "DIF???????", "RSI6???50", "MA5???????", "MACD???", "MA5???MA10",
    ]
    signal_counts['start'] = _count_signals(start_signals, signal_section)
    
    # ??????????
    continue_signals = [
        "DIF????????", "MACD??????????", "????????????MA5???",
        "RSI6?????????50????", "DIF??????0?????"
    ]
    signal_counts['continue'] = _count_signals(continue_signals, signal_section)
    
    # ?????????????????????????
    divergence_signals = [
        "MACD??????????", "?????????????", "MACD?????",
        "MACD???????????"
    ]
    signal_counts['divergence'] = _count_signals(divergence_signals, signal_section)
    
    # ?????????????????????????????
    strong_signals = [
        "4????????K???MAVOL5????", "4????????K???VOL????MAVOL5",
        "????????MAVOL5????????",
        "?????????????"  # ???????????????????????????????
    ]
    signal_counts['strong'] = _count_signals(strong_signals, signal_section)
    
    # ???????
    weak_signals = [
        "DIF???????", "MACD???????????", "MACD????", "MA5???MA10",
        "RSI6???50", "DIF????????", "3????????K???MAVOL5???",
        "4????????K???VOL????MAVOL5", "?????????", "MACD??????"
    ]
    signal_counts['weak'] = _count_signals(weak_signals, signal_section)
    
    # ??????????
    print(f"[????] ??????: {industry_name}")
    print(f"[????] ??????????: {signal_counts}")
    print(f"[????] ????????????: {sum(signal_counts.values())}")
    
    return signal_counts, unique_signature

# ????????????????????????????????
def calculate_industry_trend_resonance_score_detailed(industry_info, resonance_count):
    """
    ???????????????????????????

    Args:
        industry_info: ?????????????
        resonance_count: ?????????????

    Returns:
        tuple: (????, ???????, ?????????)
    """
    # ?????????????????????
    industry_score, industry_reason, industry_details = calculate_industry_trend_score_detailed(industry_info)

    # ??????????????????????????????????????
    if resonance_count >= 2:
        reason = f"??????????:{industry_score}??({industry_reason})?????????:{resonance_count}????(??2???????????)"
    else:
        reason = f"??????????:{industry_score}??({industry_reason})?????????:{resonance_count}????(<2???????????????????)"

    # ?????????????
    details = {
        'resonance_count': resonance_count,
        'industry_score': industry_score,
        'final_score': industry_score,
        'industry_details': industry_details if isinstance(industry_details, dict) else {},
        'reason': 'combined_score'
    }

    # ?????????????????????????????details??
    if isinstance(industry_details, dict):
        for key, value in industry_details.items():
            if key not in details:  # ?????????????
                details[key] = value

    return industry_score, reason, details

def calculate_industry_trend_resonance_score(industry_info, resonance_count):
    """
    ???????????????????????????
    
    Args:
        industry_info: ?????????????
        resonance_count: ?????????????
        
    Returns:
        int: ????
    """
    score, _, _ = calculate_industry_trend_resonance_score_detailed(industry_info, resonance_count)
    return score

def calculate_multi_period_resonance_score_detailed(tech_info):
    """?????????????????????????????
    
    ????????????????????????????????????????1??????????1????????0??
    
    Args:
        tech_info: ??????????
    
    Returns:
        tuple: (????, ????, ?????????)
    """
    try:
        # ???????????????30???????
        ultra_short_score, ultra_short_reason, ultra_short_signals = calculate_trend_score_detailed(
            tech_info, "??????", 2, 1
        )
        
        # ????????????????????????  
        medium_score, medium_reason, medium_signals = calculate_trend_score_detailed(
            tech_info, "????", 15, 8
        )
        
        # ????????????????????1??????????1??
        final_score = 1 if (ultra_short_score == 1 or medium_score == 1) else 0
        
        # ??????????
        decision_process = f"????????????????\n"
        decision_process += f"??????????(30?????)??{ultra_short_score}???{ultra_short_reason}\n"
        decision_process += f"????????(?????)??{medium_score}???{medium_reason}\n"
        
        if final_score == 1:
            winning_periods = []
            if ultra_short_score == 1:
                winning_periods.append("??????????")
            if medium_score == 1:
                winning_periods.append("????????")
            decision_process += f"?????????{' + '.join(winning_periods)}??????????1???????????????????1???"
        else:
            decision_process += "??????????????????????????????0???????????????????0???"
        
        # ?????????????
        details = {
            'ultra_short': {
                'score': ultra_short_score, 
                'reason': ultra_short_reason, 
                'signals': ultra_short_signals
            },
            'medium': {
                'score': medium_score, 
                'reason': medium_reason, 
                'signals': medium_signals
            },
            'final_score': final_score
        }
        
        return final_score, decision_process, details
        
    except Exception as e:
        error_process = f"????????????????????????{str(e)}"
        return 0, error_process, {}

def calculate_multi_period_resonance_score(tech_info):
    """?????????????????????????????????????"""
    score, reason, _ = calculate_multi_period_resonance_score_detailed(tech_info)
    return score, reason

if __name__ == "__main__":
    try:
        # ??????????????????????
        if len(sys.argv) > 1 and sys.argv[1] == "--test":
            # ??????
            test_enhanced_functions()
        else:
            # ?????
            main()
    except KeyboardInterrupt:
        print("\n???????????")
    except Exception as e:
        print(f"????????????: {e}")
        print(f"????????: {traceback.format_exc()}")
    finally:
        print("?????????")



