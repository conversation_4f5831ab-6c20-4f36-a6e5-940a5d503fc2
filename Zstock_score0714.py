# -*- coding: gbk -*-
# 股票评分分析程序
# 修改说明：
# 1. 修改了format_stock_info函数，不再返回当前行情数据
# 2. 更新了get_historical_data函数，只返回资金面情况表格格式，并添加了内盘、外盘、内外比等信息
# 3. 改进了enhance_tech_info函数，以正确识别stock_tech.py中"1. 最近20个交易日K线："格式并添加量比和换手Z信息
# 4. 修改了analyze_stock函数，增加对原始历史数据的获取和处理，使用增强后的技术指标信息
# 5. 修改了日志记录方式，不再在日志中记录时间戳，避免生成过多日志文件

import os
import time
import sys
import requests  # 添加requests库用于HTTP请求
import json  # 添加json库用于处理响应
from deepseek_client import DeepSeekClient
import Zstock_tech_qmttdx_bak as stock_tech
import concurrent.futures
import threading
from queue import Queue
import re
import pandas as pd
import traceback  # 添加用于详细错误跟踪
import glob
from datetime import datetime, timedelta
import numpy as np
import random
import akshare as ak

# 导入自定义模块
# use import * 用于支持reload模块
import Zstock_industry_tdx as industry_tech
import ZJBM  # 导入基本面分析模块

def get_timestamp():
    """获取格式化的时间戳，用于日志文件命名"""
    # 此函数保留但不再用于生成日志文件名
    return datetime.now().strftime("%Y%m%d_%H%M%S")

class VolcesClient:
    """火山引擎大模型API客户端"""
    
    def __init__(self, api_key="e1283034-c29c-41bc-a10f-14e32fbe828b", model="doubao-seed-1-6-250615"):
        self.api_url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
        self.api_key = api_key
        self.model = model
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        self.connected = True  # 默认为已连接状态
    
    def connect(self):
        """连接到服务"""
        # 火山引擎API不需要保持连接，每次请求都是独立的HTTP调用
        # 只需检查API密钥是否存在
        if not self.api_key:
            print("API密钥不能为空")
            self.connected = False
            return False
        
        self.connected = True
        return True
    
    def send(self, prompt):
        """发送请求并获取回复"""
        if not self.connected:
            print("未连接到服务")
            return ""
        
        try:
            payload = {
                "model": self.model,
                "messages": [
                    {
                        "role": "user",
                        "content": [{"type": "text", "text": prompt}]
                    }
                ],
                "temperature": 0.5  # 设置temperature参数控制随机性
            }
            
            response = requests.post(
                self.api_url, 
                headers=self.headers, 
                json=payload
            )
            
            if response.status_code != 200:
                print(f"API错误: {response.status_code} - {response.text}")
                # 增加详细错误信息输出
                print(f"火山引擎错误码: {response.status_code}")
                print(f"完整错误响应: {response.text}")
                
                try:
                    error_data = response.json()
                    if "error" in error_data:
                        error_type = error_data.get('error', {}).get('type', '未知')
                        error_msg = error_data.get('error', {}).get('message', '无详细信息')
                        print(f"错误类型: {error_type}")
                        print(f"错误信息: {error_msg}")
                    elif "code" in error_data:
                        error_code = error_data.get('code')
                        error_msg = error_data.get('message', '无详细信息')
                        print(f"错误代码: {error_code}")
                        print(f"错误信息: {error_msg}")
                except Exception as parse_error:
                    print(f"解析错误响应失败: {parse_error}")
                
                return ""
            
            result = response.json()
            # 获取回复内容 - 处理新的doubao-seed-1-6模型可能返回的格式
            content = ""
            message = result.get("choices", [{}])[0].get("message", {})
            if "content" in message:
                content = message.get("content", "")
            else:
                # 处理新模型可能返回的格式
                content_list = message.get("content", [])
                if content_list and isinstance(content_list, list):
                    for item in content_list:
                        if item.get("type") == "text":
                            content += item.get("text", "")
            
            return content
        
        except Exception as e:
            print(f"发送请求失败: {e}")
            print(f"错误详情: {traceback.format_exc()}")
            return ""
    
    def stream(self, prompt):
        """流式发送请求并获取回复"""
        if not self.connected:
            print("未连接到服务")
            return
        
        try:
            payload = {
                "model": self.model,
                "stream": True,
                "messages": [
                    {
                        "role": "user",
                        "content": [{"type": "text", "text": prompt}]
                    }
                ],
                "temperature": 0.5  # 设置temperature参数控制随机性
            }
            
            response = requests.post(
                self.api_url, 
                headers=self.headers, 
                json=payload,
                stream=True
            )
            
            if response.status_code != 200:
                print(f"API错误: {response.status_code} - {response.text}")
                yield ""
                return
            
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        data = line[6:]
                        if data.strip() == '[DONE]':
                            break
                        try:
                            json_data = json.loads(data)
                            # 处理新模型可能返回的流式格式
                            delta = json_data.get('choices', [{}])[0].get('delta', {})
                            if 'content' in delta:
                                # 旧格式
                                content = delta.get('content', '')
                                if content:
                                    yield content
                            else:
                                # 新格式 - 可能返回列表形式的content
                                content_list = delta.get('content', [])
                                if content_list and isinstance(content_list, list):
                                    for item in content_list:
                                        if item.get('type') == 'text':
                                            text = item.get('text', '')
                                            if text:
                                                yield text
                        except json.JSONDecodeError:
                            pass
        
        except Exception as e:
            print(f"流式请求失败: {e}")
            yield ""
    
    def close(self):
        """关闭连接"""
        self.connected = False
        # 实际上无需特别关闭，因为使用的是无状态HTTP请求

def find_name_column(columns):
    """查找股票名称列，支持'名称'和'名称(x)'格式"""
    # 首先检查是否有精确匹配'名称(x)'格式的列
    for col in columns:
        if isinstance(col, str) and col.startswith('名称(') and col.endswith(')'):
            return col
    
    # 其次检查是否有精确匹配'名称'的列
    for col in columns:
        if isinstance(col, str) and col == '名称':
            return col
    
    # 最后检查是否有以'名称'开头的列
    for col in columns:
        if isinstance(col, str) and col.startswith('名称'):
            return col
    
    return None

def clean_stock_code(code):
    """清理股票代码，去除非数字字符并确保6位格式"""
    if not code:
        return ""
    
    # 转为字符串
    code_str = str(code)
    
    # 去除所有非数字字符（如'='等）
    digits_only = ''.join(c for c in code_str if c.isdigit())
    
    # 确保6位格式，不足前面补0
    if digits_only:
        return digits_only.zfill(6)
    
    return ""

def read_stock_data(file_path):
    """读取股票数据文件，支持xls和txt格式，自动识别'名称'和'名称(x)'列"""
    try:
        # 根据文件扩展名选择读取方式
        file_ext = os.path.splitext(file_path)[1].lower()
        
        if file_ext == '.xls' or file_ext == '.xlsx':
            # 首先尝试作为文本文件读取
            try:
                print(f"尝试读取文件: {file_path}")
                with open(file_path, 'r', encoding='gbk', errors='ignore') as f:
                    lines = f.readlines()
                
                if not lines:
                    raise ValueError("文件为空")
                
                # 解析表头
                header = [col.strip() for col in lines[0].strip().split('\t') if col.strip()]
                
                # 查找名称列
                name_col = find_name_column(header)
                if not name_col:
                    raise ValueError("未找到名称列")
                
                # 解析数据
                stocks = []
                for i, line in enumerate(lines[1:], 1):
                    if not line.strip():
                        continue
                    values = [val.strip() for val in line.strip().split('\t')]
                    # 确保数据项数量与表头一致
                    if len(values) >= len(header):
                        stock = dict(zip(header, values))
                        # 确保股票代码和名称存在
                        if '代码' in stock and name_col in stock and stock['代码'] and stock[name_col]:
                            # 清理股票代码中的非法字符
                            stock['代码'] = clean_stock_code(stock['代码'])
                            # 统一名称列为'名称'
                            stock['名称'] = stock[name_col]
                            stocks.append(stock)
                    else:
                        print(f"警告: 第{i+1}行数据项数量({len(values)})小于表头数量({len(header)})，已跳过")
                
                print(f"成功读取 {len(stocks)} 只股票数据")
                return header, stocks, name_col
                
            except Exception as e:
                print(f"以文本格式读取失败: {e}，尝试以Excel格式读取...")
                # 如果文本读取失败，再尝试Excel读取
                try:
                    if file_ext == '.xlsx':
                        df = pd.read_excel(file_path, engine='openpyxl')
                    else:
                        # 对于.xls文件，尝试使用xlrd引擎
                        df = pd.read_excel(file_path, engine='xlrd')
                except Exception as e2:
                    # 尝试备选引擎
                    try:
                        engine = 'openpyxl' if file_ext == '.xlsx' else 'xlrd'
                        df = pd.read_excel(file_path, engine=engine)
                    except Exception as e3:
                        # 放弃，抛出异常
                        raise ValueError(f"无法读取文件，所有尝试都失败: {e}, {e2}, {e3}")
                
                # 查找名称列
                name_col = find_name_column(df.columns)
                if not name_col:
                    raise ValueError("未找到名称列")
                
                # 将DataFrame转换为字典列表
                stocks = []
                for _, row in df.iterrows():
                    stock = row.to_dict()
                    # 确保股票代码和名称存在
                    if '代码' in stock and name_col in stock and pd.notna(stock['代码']) and pd.notna(stock[name_col]):
                        # 清理并格式化股票代码
                        stock['代码'] = clean_stock_code(stock['代码'])
                        # 统一名称列为'名称'
                        stock['名称'] = stock[name_col]
                        stocks.append(stock)
                
                print(f"成功读取 {len(stocks)} 只股票数据")
                return df.columns.tolist(), stocks, name_col
            
        else:  # 默认为txt格式
            with open(file_path, 'r', encoding='gbk', errors='ignore') as f:
                lines = f.readlines()
            
            if not lines:
                raise ValueError("文件为空")
            
            # 解析表头
            header = [col.strip() for col in lines[0].strip().split('\t') if col.strip()]
            
            # 查找名称列
            name_col = find_name_column(header)
            if not name_col:
                raise ValueError("未找到名称列")
            
            # 解析数据
            stocks = []
            for i, line in enumerate(lines[1:], 1):
                if not line.strip():
                    continue
                values = [val.strip() for val in line.strip().split('\t')]
                # 确保数据项数量与表头一致
                if len(values) >= len(header):
                    stock = dict(zip(header, values))
                    # 确保股票代码和名称存在
                    if '代码' in stock and name_col in stock and stock['代码'] and stock[name_col]:
                        # 清理股票代码
                        stock['代码'] = clean_stock_code(stock['代码'])
                        # 统一名称列为'名称'
                        stock['名称'] = stock[name_col]
                        stocks.append(stock)
                else:
                    print(f"警告: 第{i+1}行数据项数量({len(values)})小于表头数量({len(header)})，已跳过")
            
            print(f"成功读取 {len(stocks)} 只股票数据")
            return header, stocks, name_col
    except Exception as e:
        print(f"读取文件时出错: {e}")
        raise

def get_historical_data(stock_code, original_headers=None, base_dir="D:\\stock\\tdxdata", current_stock=None, use_simulation=False):
    """获取股票的历史交易数据
    
    Args:
        stock_code: 股票代码
        original_headers: 原始数据文件的列名列表，用于过滤历史数据
        base_dir: 数据文件目录
        current_stock: 当前交易日的股票数据(来自股票信息部分)
        use_simulation: 是否使用模拟数据(当通达信数据不可用时)
    
    Returns:
        str: 仅包含格式化的资金面情况表格的字符串
    """
    try:
        if original_headers is None:
            original_headers = []
        
        # 存储每个交易日的日期和数据
        date_data_mapping = {}
        
        # 获取当前日期，用于比较和过滤
        current_date_obj = datetime.now()
        current_date = current_date_obj.strftime('%Y%m%d')
        current_date_formatted = current_date_obj.strftime('%Y-%m-%d')
        
        # 从当前股票信息中提取当前交易日(t日)数据
        current_main_flow = None
        current_main_buy = None
        current_inner_volume = None
        current_outer_volume = None
        
        # 确保current_stock是字典类型
        if current_stock and isinstance(current_stock, dict):
            for key, value in current_stock.items():
                if value and str(value).strip() and not pd.isna(value):
                    key_lower = key.lower()
                    if '主力净额' in key_lower:
                        try:
                            current_main_flow = float(str(value).replace(',', ''))
                        except (ValueError, TypeError):
                            current_main_flow = 0
                    elif '主买净额' in key_lower:
                        try:
                            current_main_buy = float(str(value).replace(',', ''))
                        except (ValueError, TypeError):
                            current_main_buy = 0
                    elif '内盘' in key_lower and '外盘' not in key_lower:
                        try:
                            current_inner_volume = float(str(value).replace(',', ''))
                        except (ValueError, TypeError):
                            current_inner_volume = 0
                    elif '外盘' in key_lower:
                        try:
                            current_outer_volume = float(str(value).replace(',', ''))
                        except (ValueError, TypeError):
                            current_outer_volume = 0
            
            # 保存当前日数据
            date_data_mapping[current_date_formatted] = {
                'main_flow': current_main_flow,
                'main_buy': current_main_buy,
                'inner_volume': current_inner_volume,
                'outer_volume': current_outer_volume
            }
        
        # 如果不使用模拟数据，尝试从通达信目录获取历史数据
        if not use_simulation and os.path.exists(base_dir):
            try:
                # 查找符合格式的历史文件
                all_stock_files = glob.glob(os.path.join(base_dir, "全部Ａ股*.xls")) + glob.glob(os.path.join(base_dir, "全部Ａ股*.txt"))
                
                # 如果没有找到标准格式文件，尝试查找其他可能的命名格式
                if not all_stock_files:
                    print("未找到标准格式的文件，尝试搜索其他命名格式...")
                    # 尝试查找'沪深A股*'格式
                    all_stock_files = glob.glob(os.path.join(base_dir, "沪深A股*.xls")) + glob.glob(os.path.join(base_dir, "沪深A股*.txt"))
                    # 尝试查找'全部股票*'格式
                    if not all_stock_files:
                        all_stock_files = glob.glob(os.path.join(base_dir, "全部股票*.xls")) + glob.glob(os.path.join(base_dir, "全部股票*.txt"))
                    # 尝试查找可能有空格的格式
                    if not all_stock_files:
                        all_stock_files = glob.glob(os.path.join(base_dir, "全部 A股*.xls")) + glob.glob(os.path.join(base_dir, "全部 A股*.txt"))
                        all_stock_files += glob.glob(os.path.join(base_dir, "沪深 A股*.xls")) + glob.glob(os.path.join(base_dir, "沪深 A股*.txt"))
                
                # 按照日期排序文件（从文件名中提取日期）
                def extract_date(filename):
                    # 尝试多种格式的日期提取
                    # 1. 标准格式："全部Ａ股20250101.xls"
                    date_match = re.search(r'全部Ａ股(\d{8})', os.path.basename(filename))
                    if date_match:
                        date_str = date_match.group(1)
                        return datetime.strptime(date_str, '%Y%m%d')
                    
                    # 2. 沪深A股格式："沪深A股20250101.xls"
                    date_match = re.search(r'沪深A股(\d{8})', os.path.basename(filename))
                    if date_match:
                        date_str = date_match.group(1)
                        return datetime.strptime(date_str, '%Y%m%d')
                        
                    # 3. 全部股票格式："全部股票20250101.xls"
                    date_match = re.search(r'全部股票(\d{8})', os.path.basename(filename))
                    if date_match:
                        date_str = date_match.group(1)
                        return datetime.strptime(date_str, '%Y%m%d')
                    
                    # 4. 带空格的格式："全部 A股20250101.xls" 或 "沪深 A股20250101.xls"
                    date_match = re.search(r'(全部|沪深)\s+A股(\d{8})', os.path.basename(filename))
                    if date_match:
                        date_str = date_match.group(2)
                        return datetime.strptime(date_str, '%Y%m%d')
                    
                    # 5. 一般格式：尝试直接从文件名中提取8位数字作为日期
                    date_match = re.search(r'(\d{8})', os.path.basename(filename))
                    if date_match:
                        date_str = date_match.group(1)
                        try:
                            return datetime.strptime(date_str, '%Y%m%d')
                        except ValueError:
                            pass
                            
                    return datetime.min
                
                sorted_files = sorted(all_stock_files, key=extract_date, reverse=True)
                
                # 过滤掉可能与当前日期相同的文件
                filtered_files = []
                for file_path in sorted_files:
                    file_date_match = re.search(r'全部Ａ股(\d{8})', os.path.basename(file_path))
                    if file_date_match and file_date_match.group(1) != current_date:
                        filtered_files.append(file_path)
                
                # 只取最近的4个文件作为历史数据(不包括当前交易日)
                recent_files = filtered_files[:4]
                
                if not recent_files:
                    print(f"警告: 未找到任何历史交易数据文件，将使用模拟数据")
                    print(f"搜索路径: {base_dir}")
                    print(f"当前日期: {current_date}")
                    print(f"找到的所有文件: {all_stock_files}")
                    print(f"过滤后的文件: {filtered_files}")
                    use_simulation = True
                else:
                    # 从历史数据文件中获取t-1至t-4日数据
                    for file_path in recent_files:
                        file_date = extract_date(file_path)
                        date_str = file_date.strftime('%Y-%m-%d')
                        
                        # 检查文件是否是真正的Excel文件
                        is_real_excel = False
                        if file_path.lower().endswith('.xls'):
                            try:
                                with open(file_path, 'rb') as f:
                                    header = f.read(8)
                                    # 检查文件头部是否为Excel文件标识
                                    is_real_excel = header.startswith(b'\xD0\xCF\x11\xE0') or header.startswith(b'PK\x03\x04')
                            except Exception:
                                is_real_excel = False
                        
                        try:
                            stock_row = None
                            
                            if is_real_excel:
                                try:
                                    if file_path.lower().endswith('.xls'):
                                        df = pd.read_excel(file_path, engine='xlrd')
                                    else:
                                        df = pd.read_excel(file_path, engine='openpyxl')
                                        
                                    # 查找股票代码
                                    for _, row in df.iterrows():
                                        if '代码' in df.columns and pd.notna(row['代码']):
                                            row_code = clean_stock_code(row['代码'])
                                            if row_code == stock_code:
                                                stock_row = row.to_dict()
                                                break
                                except Exception as e:
                                    print(f"以Excel格式读取历史文件失败: {e}")
                            else:
                                # 不是真正的Excel文件，尝试文本方式读取
                                try:
                                    with open(file_path, 'r', encoding='gbk', errors='ignore') as f:
                                        lines = f.readlines()
                                    
                                    if not lines:
                                        print(f"文件 {file_path} 为空或无法读取")
                                        continue
                                    
                                    # 解析表头
                                    file_header = [col.strip() for col in lines[0].strip().split('\t') if col.strip()]
                                    if not file_header:
                                        print(f"文件 {file_path} 表头解析失败")
                                        continue
                                        
                                    # 查找股票
                                    for line in lines[1:]:
                                        if not line.strip():
                                            continue
                                        
                                        values = [val.strip() for val in line.strip().split('\t')]
                                        if len(values) >= len(file_header):
                                            row_dict = {file_header[i]: values[i] for i in range(len(file_header))}
                                            if '代码' in row_dict and row_dict['代码']:
                                                row_code = clean_stock_code(row_dict['代码'])
                                                if row_code == stock_code:
                                                    stock_row = row_dict
                                                    break
                                except Exception as e:
                                    print(f"以文本格式读取历史文件失败: {e}")
                            
                            # 如果找到了股票行，提取资金面数据
                            if stock_row:
                                main_flow = None
                                main_buy = None
                                inner_volume = None
                                outer_volume = None
                                
                                for key, value in stock_row.items():
                                    if value and str(value).strip() and not pd.isna(value):
                                        key_lower = key.lower()
                                        if '主力净额' in key_lower:
                                            try:
                                                main_flow = float(str(value).replace(',', ''))
                                            except (ValueError, TypeError):
                                                main_flow = 0
                                        elif '主买净额' in key_lower:
                                            try:
                                                main_buy = float(str(value).replace(',', ''))
                                            except (ValueError, TypeError):
                                                main_buy = 0
                                        elif '内盘' in key_lower and '外盘' not in key_lower:
                                            try:
                                                inner_volume = float(str(value).replace(',', ''))
                                            except (ValueError, TypeError):
                                                inner_volume = 0
                                        elif '外盘' in key_lower:
                                            try:
                                                outer_volume = float(str(value).replace(',', ''))
                                            except (ValueError, TypeError):
                                                outer_volume = 0
                                
                                # 保存该日期的数据
                                date_data_mapping[date_str] = {
                                    'main_flow': main_flow,
                                    'main_buy': main_buy,
                                    'inner_volume': inner_volume,
                                    'outer_volume': outer_volume
                                }
                        except Exception as e:
                            print(f"处理历史文件 {file_path} 时出错: {e}")
            except Exception as e:
                print(f"读取历史交易数据失败: {e}")
                use_simulation = True
        else:
            use_simulation = True
        
        # 如果需要使用模拟数据，生成模拟的历史数据
        if use_simulation:
            print("使用模拟历史数据...")
            # 生成模拟的历史数据（t-1至t-4日）
            for i in range(1, 5):
                date = (current_date_obj - timedelta(days=i)).strftime('%Y-%m-%d')
                
                # 基于当前数据生成合理的模拟数据
                if current_main_flow is not None:
                    sim_main_flow = current_main_flow * (0.85 + 0.3 * random.random())  # 在当前值的85%-115%之间波动
                else:
                    sim_main_flow = 5000000 + 5000000 * random.random()  # 完全随机
                    
                if current_main_buy is not None:
                    sim_main_buy = current_main_buy * (0.85 + 0.3 * random.random())
                else:
                    sim_main_buy = 3000000 + 4000000 * random.random()
                    
                if current_inner_volume is not None:
                    sim_inner_volume = current_inner_volume * (0.85 + 0.3 * random.random())
                else:
                    sim_inner_volume = 10000000 + 5000000 * random.random()
                    
                if current_outer_volume is not None:
                    sim_outer_volume = current_outer_volume * (0.85 + 0.3 * random.random())
                else:
                    sim_outer_volume = 10000000 + 5000000 * random.random()
                
                # 保存模拟数据
                date_data_mapping[date] = {
                    'main_flow': sim_main_flow,
                    'main_buy': sim_main_buy,
                    'inner_volume': sim_inner_volume,
                    'outer_volume': sim_outer_volume
                }
        
        # 格式化结果
        result = "【资金面情况】\n"
        if use_simulation:
            result += "【警告：使用模拟数据】真实数据未找到，以下为基于算法生成的模拟数据\n"
            
        # 添加表头
        result += "日期\t\t主力净额\t主买净额\t内盘\t\t外盘\t\t内外盘比\n"
        
        # 按日期排序（从最近到最远）
        all_dates = sorted(date_data_mapping.keys(), reverse=True)
        
        # 遍历所有日期数据
        for date_str in all_dates:
            data = date_data_mapping[date_str]
            # 确保data是字典类型，避免'str'对象没有'get'属性的错误
            if not isinstance(data, dict):
                print(f"警告：日期 {date_str} 的数据不是字典类型: {type(data)}")
                continue
                
            main_flow = data.get('main_flow')
            main_buy = data.get('main_buy')
            inner_volume = data.get('inner_volume')
            outer_volume = data.get('outer_volume')
            
            # 计算内外盘比
            inner_outer_ratio = 0
            if outer_volume and outer_volume > 0:
                inner_outer_ratio = inner_volume / outer_volume if inner_volume is not None else 0
            
            # 格式化数据
            main_flow_str = f"{main_flow/10000:.2f}万" if main_flow is not None else "N/A"
            main_buy_str = f"{main_buy/10000:.2f}万" if main_buy is not None else "N/A"
            inner_volume_str = f"{inner_volume/10000:.2f}万" if inner_volume is not None else "N/A"
            outer_volume_str = f"{outer_volume/10000:.2f}万" if outer_volume is not None else "N/A"
            ratio_str = f"{inner_outer_ratio:.2f}" if inner_outer_ratio > 0 else "N/A"
            
            # 添加该日期的数据行
            result += f"{date_str}\t{main_flow_str}\t{main_buy_str}\t{inner_volume_str}\t{outer_volume_str}\t{ratio_str}\n"
        
        # 添加主力净额和主买净额为正的天数统计
        def is_positive_number(value):
            """检查值是否为正数"""
            return isinstance(value, (int, float)) and value > 0
        
        positive_main_flow = sum(1 for date in all_dates 
                               if isinstance(date_data_mapping[date], dict) 
                               and is_positive_number(date_data_mapping[date].get('main_flow', 0)))
        positive_main_buy = sum(1 for date in all_dates 
                              if isinstance(date_data_mapping[date], dict) 
                              and is_positive_number(date_data_mapping[date].get('main_buy', 0)))
        
        result += f"\n主力净额为正的天数: {positive_main_flow}/{len(all_dates)}"
        result += f"\n主买净额为正的天数: {positive_main_buy}/{len(all_dates)}"
        
        return result
    except Exception as e:
        print(f"获取历史交易数据失败: {e}")
        traceback.print_exc()
        return "无法获取历史交易数据"

def _robust_extract_section(text: str, start_keyword: str) -> str:
    """More robustly extracts a section from a text block.
    A section starts with a given keyword and ends right before the next
    numbered heading (e.g., '2. Some Title') or at the end of the text.
    """
    import re
    lines = text.splitlines()
    found_section_lines = []
    in_section = False

    for line in lines:
        # A section header is a line that starts with digits, a dot, and a space.
        is_header = re.match(r'^\s*\d+\.\s', line)

        if is_header:
            if start_keyword in line:
                # Found the start of the target section.
                in_section = True
                found_section_lines.append(line)
            elif in_section:
                # Found the header of the *next* section, so we stop.
                break
        elif in_section:
            # This line is part of the target section.
            found_section_lines.append(line)
            
    return "\n".join(found_section_lines).strip()

def enhance_tech_info(tech_info, stock, historical_data=None):
    """
    从当日行情和历史行情中提取量比和换手Z信息，并将其添加到技术指标信息中
    
    Args:
        tech_info: 从stock_tech.py获取的技术指标信息
        stock: 当日股票信息字典
        historical_data: 历史交易数据信息字符串
    
    Returns:
        str: 增强后的技术指标信息
    """
    if not tech_info:
        return tech_info
    
    # 查找K线部分，同时支持三种可能的格式
    section_start = tech_info.find("1. 最近20个交易日K线：")
    if section_start == -1:
        # 尝试查找第二种格式
        section_start = tech_info.find("1. 最近10个交易日的K线/成交量行情：")
        if section_start == -1:
            # 尝试查找第三种格式 (Zstock_tech_qmt.py格式)
            section_start = tech_info.find("1. 最近5个交易日的K线/成交量行情：")
            if section_start == -1:
                # 如果三种格式都没找到，直接返回原始信息
                return tech_info
    
    section_end = tech_info.find("\n\n2.", section_start)
    if section_end == -1:
        # 如果没找到下一部分的开始，假设到结尾
        section_end = len(tech_info)
    
    # 提取这个部分的内容
    kline_section = tech_info[section_start:section_end]
    
    # 从当前股票信息中提取量比和换手Z
    current_volume_ratio = None
    current_turnover_z = None
    
    # 确保stock是字典类型
    if stock and isinstance(stock, dict):
        for key, value in stock.items():
            if value and str(value).strip() and not pd.isna(value):
                key_lower = key.lower()
                if '量比' in key_lower:
                    try:
                        current_volume_ratio = float(str(value).replace('%', ''))
                    except (ValueError, TypeError):
                        pass
                elif '换手z' in key_lower.replace(' ', ''):
                    try:
                        # 如果值已包含%，保留它
                        current_turnover_z = str(value)
                        if not current_turnover_z.endswith('%') and not current_turnover_z.strip() == '0':
                            current_turnover_z += '%'
                    except (ValueError, TypeError):
                        pass
    
    # 从历史数据中提取量比和换手Z信息
    historical_volume_ratio = {}
    historical_turnover_z = {}
    
    if historical_data:
        # 查找历史数据中的日期和相应的量比、换手Z
        data_blocks = re.findall(r'【(\d{4}-\d{2}-\d{2})交易数据】(.*?)(?=【|$)', historical_data, re.DOTALL)
        for date_str, content in data_blocks:
            # 提取量比
            volume_ratio_match = re.search(r'量比[:：]\s*([0-9.]+)', content)
            if volume_ratio_match:
                try:
                    historical_volume_ratio[date_str] = float(volume_ratio_match.group(1))
                except (ValueError, TypeError):
                    pass
            
            # 提取换手Z
            turnover_z_match = re.search(r'换手Z[:：]\s*([0-9.]+%?)', content)
            if turnover_z_match:
                turnover_z = turnover_z_match.group(1)
                if not turnover_z.endswith('%') and not turnover_z.strip() == '0':
                    turnover_z += '%'
                historical_turnover_z[date_str] = turnover_z
    
    # 修改K线部分，添加量比和换手Z
    lines = kline_section.split('\n')
    modified_lines = []
    
    # 首行是标题，保持不变
    if lines:
        modified_lines.append(lines[0])
    
    # 处理其余行
    for i in range(1, len(lines)):
        line = lines[i]
        # 支持两种可能的日期格式：
        # 1. "   2025-05-16: 开盘=29586.57, ..." (带缩进)
        # 2. "2025-05-16: 开盘=29586.57, ..." (不带缩进)
        date_match = re.search(r'\s*(\d{4}-\d{2}-\d{2}):', line)
        if date_match:
            date_str = date_match.group(1)
            
            # 确定是否为当日数据
            is_current_date = False
            try:
                line_date = datetime.strptime(date_str, '%Y-%m-%d')
                current_date = datetime.now()
                is_current_date = (line_date.date() == current_date.date())
            except:
                pass
            
            # 根据日期选择量比和换手Z的值
            volume_ratio = None
            turnover_z = None
            
            if is_current_date and current_volume_ratio is not None:
                volume_ratio = current_volume_ratio
            elif date_str in historical_volume_ratio:
                volume_ratio = historical_volume_ratio[date_str]
            
            if is_current_date and current_turnover_z is not None:
                turnover_z = current_turnover_z
            elif date_str in historical_turnover_z:
                turnover_z = historical_turnover_z[date_str]
            
            # 添加量比和换手Z到行末
            if volume_ratio is not None or turnover_z is not None:
                # 去掉行尾可能的换行符
                clean_line = line.rstrip()
                
                # 添加量比
                if volume_ratio is not None:
                    clean_line += f", 量比={volume_ratio:.2f}"
                
                # 添加换手Z
                if turnover_z is not None:
                    clean_line += f", 换手Z={turnover_z}"
                
                # 替换原行
                modified_lines.append(clean_line)
            else:
                # 如果没有找到对应的量比和换手Z，保持原行不变
                modified_lines.append(line)
        else:
            # 不是日期行，保持不变
            modified_lines.append(line)
    
    # 替换原技术指标中的K线部分
    modified_kline_section = '\n'.join(modified_lines)
    enhanced_tech_info = tech_info[:section_start] + modified_kline_section + tech_info[section_end:]
    
    return enhanced_tech_info

def format_stock_info(stock):
    """格式化股票信息，参考Wstock_compare.py，不再返回当前行情数据"""
    # 不再返回当前行情数据
    return ""

def validate_model_response(response):
    """验证模型返回的响应是否有效"""
    if not response or not isinstance(response, str):
        return False
    
    # 检查是否包含预期的评分项格式，更新为新的评分项
    item_patterns = [
        r'0\.\s*超短趋势判断',
        r'1\.\s*短线趋势判断',
        r'2\.\s*中线趋势判断',
        r'3\.\s*资金面判断',
        r'4\.\s*消息面判断',
        r'5\.\s*行业趋势及共振判断',
        r'6\.\s*盈亏比判断'
    ]
    
    # 至少应包含几个评分项
    valid_items = 0
    for pattern in item_patterns:
        if re.search(pattern, response, re.IGNORECASE):
            valid_items += 1
    
    # 如果至少有4个评分项，认为响应有效
    return valid_items >= 4

def calculate_fundamental_score_detailed(stock_code):
    """
    计算基本面判断评分（详细版本）

    评分规则：
    a. PE分位小于等于50%
    b. ROE大于等于15%
    c. 最近一个季度的营收和扣非利润增长率之和大于50%，或者营收/扣非利润其中一项实现同比扭亏

    以上三个条件满足2个或以上，则得1分，否则不得分。

    Args:
        stock_code: 6位股票代码

    Returns:
        tuple: (得分, 理由文本, 详细信息字典)
    """
    try:
        api_token = "d7c12fcb-2961-41b4-b3ef-1e432b4f2a1c"

        # 获取PE分位数据
        pe_result = ZJBM.get_pe_percentile(stock_code, api_token)

        # 获取ROE数据
        roe_result = ZJBM.get_roe_data(stock_code, api_token)

        # 获取季度增长率数据
        quarterly_result = ZJBM.get_quarterly_growth_rates(stock_code, api_token)

        # 初始化条件检查结果
        condition_a = False  # PE分位 <= 50%
        condition_b = False  # ROE >= 15%
        condition_c = False  # 最近季度营收和扣非利润增长率之和 > 50% 或扭亏

        # 详细信息记录
        details = {
            'pe_data': pe_result,
            'roe_data': roe_result,
            'quarterly_data': quarterly_result,
            'condition_a': False,
            'condition_b': False,
            'condition_c': False,
            'conditions_met': 0
        }

        decision_process = f"基本面分析 - 股票代码: {stock_code}\n"

        # 检查条件a: PE分位 <= 50%
        if 'error' not in pe_result and 'percentile' in pe_result:
            pe_percentile = pe_result['percentile']
            if pe_percentile <= 50:
                condition_a = True
                decision_process += f"- 条件a满足: PE分位({pe_percentile:.2f}%) <= 50%\n"
            else:
                decision_process += f"- 条件a不满足: PE分位({pe_percentile:.2f}%) > 50%\n"
        else:
            decision_process += f"- 条件a无法判断: PE数据获取失败\n"

        # 检查条件b: ROE >= 15%
        if 'error' not in roe_result and 'roe' in roe_result:
            roe_value = roe_result['roe']
            if roe_value >= 15:
                condition_b = True
                decision_process += f"- 条件b满足: ROE({roe_value:.2f}%) >= 15%\n"
            else:
                decision_process += f"- 条件b不满足: ROE({roe_value:.2f}%) < 15%\n"
        else:
            decision_process += f"- 条件b无法判断: ROE数据获取失败\n"

        # 检查条件c: 最近季度营收和扣非利润增长率之和 > 50% 或扭亏
        if 'error' not in quarterly_result and 'quarterly_data' in quarterly_result:
            quarterly_data = quarterly_result['quarterly_data']
            if quarterly_data and len(quarterly_data) >= 2:  # 需要至少2个季度数据来判断扭亏
                latest_quarter = quarterly_data[0]  # 最新季度
                previous_quarter = quarterly_data[1]  # 上一个季度

                latest_revenue_growth = latest_quarter.get('revenue_growth')
                latest_profit_growth = latest_quarter.get('profit_growth')
                previous_revenue_growth = previous_quarter.get('revenue_growth')
                previous_profit_growth = previous_quarter.get('profit_growth')

                if (latest_revenue_growth is not None and latest_profit_growth is not None):
                    # 检查增长率之和是否大于50%
                    growth_sum = latest_revenue_growth + latest_profit_growth
                    growth_sum_satisfied = growth_sum > 50

                    # 检查是否有扭亏情况
                    revenue_turnaround = False
                    profit_turnaround = False

                    if (previous_revenue_growth is not None and
                        previous_revenue_growth < 0 and latest_revenue_growth > 0):
                        revenue_turnaround = True

                    if (previous_profit_growth is not None and
                        previous_profit_growth < 0 and latest_profit_growth > 0):
                        profit_turnaround = True

                    turnaround_satisfied = revenue_turnaround or profit_turnaround

                    # 满足任一条件即可
                    if growth_sum_satisfied or turnaround_satisfied:
                        condition_c = True
                        if growth_sum_satisfied and turnaround_satisfied:
                            decision_process += f"- 条件c满足: 增长率之和({growth_sum:.2f}% > 50%) 且 "
                            if revenue_turnaround and profit_turnaround:
                                decision_process += f"营收和扣非利润均实现扭亏\n"
                            elif revenue_turnaround:
                                decision_process += f"营收实现扭亏(从{previous_revenue_growth:.2f}%到{latest_revenue_growth:.2f}%)\n"
                            else:
                                decision_process += f"扣非利润实现扭亏(从{previous_profit_growth:.2f}%到{latest_profit_growth:.2f}%)\n"
                        elif growth_sum_satisfied:
                            decision_process += f"- 条件c满足: 最近季度营收增长({latest_revenue_growth:.2f}%) + 扣非利润增长({latest_profit_growth:.2f}%) = {growth_sum:.2f}% > 50%\n"
                        else:  # turnaround_satisfied
                            if revenue_turnaround and profit_turnaround:
                                decision_process += f"- 条件c满足: 营收和扣非利润均实现扭亏(营收:{previous_revenue_growth:.2f}%→{latest_revenue_growth:.2f}%, 扣非利润:{previous_profit_growth:.2f}%→{latest_profit_growth:.2f}%)\n"
                            elif revenue_turnaround:
                                decision_process += f"- 条件c满足: 营收实现扭亏(从{previous_revenue_growth:.2f}%到{latest_revenue_growth:.2f}%)\n"
                            else:
                                decision_process += f"- 条件c满足: 扣非利润实现扭亏(从{previous_profit_growth:.2f}%到{latest_profit_growth:.2f}%)\n"
                    else:
                        decision_process += f"- 条件c不满足: 增长率之和({growth_sum:.2f}% <= 50%) 且 无扭亏情况\n"
                        if previous_revenue_growth is not None and previous_profit_growth is not None:
                            decision_process += f"  (上季度: 营收{previous_revenue_growth:.2f}%, 扣非利润{previous_profit_growth:.2f}%; 本季度: 营收{latest_revenue_growth:.2f}%, 扣非利润{latest_profit_growth:.2f}%)\n"
                else:
                    decision_process += f"- 条件c无法判断: 最近季度增长率数据不完整\n"
            elif quarterly_data and len(quarterly_data) == 1:
                # 只有一个季度数据，无法判断扭亏，只能用增长率之和
                latest_quarter = quarterly_data[0]
                latest_revenue_growth = latest_quarter.get('revenue_growth')
                latest_profit_growth = latest_quarter.get('profit_growth')

                if (latest_revenue_growth is not None and latest_profit_growth is not None):
                    growth_sum = latest_revenue_growth + latest_profit_growth
                    if growth_sum > 50:
                        condition_c = True
                        decision_process += f"- 条件c满足: 最近季度营收增长({latest_revenue_growth:.2f}%) + 扣非利润增长({latest_profit_growth:.2f}%) = {growth_sum:.2f}% > 50%\n"
                    else:
                        decision_process += f"- 条件c不满足: 增长率之和({growth_sum:.2f}% <= 50%) 且 无法判断扭亏(仅有1个季度数据)\n"
                else:
                    decision_process += f"- 条件c无法判断: 最近季度增长率数据不完整\n"
            else:
                decision_process += f"- 条件c无法判断: 无季度数据\n"
        else:
            decision_process += f"- 条件c无法判断: 季度数据获取失败\n"

        # 更新详细信息
        details['condition_a'] = condition_a
        details['condition_b'] = condition_b
        details['condition_c'] = condition_c

        # 计算满足的条件数量
        conditions_met = sum([condition_a, condition_b, condition_c])
        details['conditions_met'] = conditions_met

        # 根据规则计算得分
        score = 1 if conditions_met >= 2 else 0

        decision_process += f"\n满足条件数量: {conditions_met}/3\n"
        decision_process += f"根据规则，满足条件数量{'≥2' if conditions_met >= 2 else '<2'}，因此本维度得{score}分。"

        return score, decision_process, details

    except Exception as e:
        error_process = f"计算基本面评分时出现错误：{str(e)}"
        return 0, error_process, {'error': str(e)}

def analyze_stock_locally(stock_index, stock, stock_tech_info, output_queue, output_file_lock, output_file, original_headers, model_log_file, industry_tech_info=None, stock_to_industry=None, stock_resonance_info=None):
    """本地计算股票评分，不使用大模型"""
    
    # 检查stock参数的类型
    if not isinstance(stock, dict):
        print(f"错误: stock参数不是字典类型，而是 {type(stock)}: {stock}")
        return
    
    stock_code = stock.get('代码', '未知')
    stock_name = stock.get('名称', '未知')
    
    try:
        print(f"开始本地计算第{stock_index+1}只股票: {stock_code}({stock_name})")
        
        # 获取技术指标信息
        tech_info = stock_tech_info.get(stock_code, "")
        if not tech_info:
            print(f"警告: 股票 {stock_code} 没有技术指标信息")
            
        # 获取历史交易数据
        history_info = get_historical_data(stock_code, original_headers, current_stock=stock, use_simulation=False)
        
        # 获取行业信息
        industry_info = ""
        industry_name = ""
        full_industry_name = ""
        
        if stock_to_industry and stock_code in stock_to_industry:
            full_industry_name = stock_to_industry[stock_code]
            industry_name = full_industry_name.split('-')[-1] if '-' in full_industry_name else full_industry_name
            
            if industry_tech_info and industry_name in industry_tech_info:
                industry_info = industry_tech_info[industry_name]
        
        # 获取行业共振信息
        resonance_info = ""
        resonance_count = 0
        if stock_resonance_info and stock_code in stock_resonance_info:
            resonance_data = stock_resonance_info[stock_code]
            resonance_count = resonance_data.get('count', 0)
            industry = resonance_data.get('industry', full_industry_name)
            resonance_info = f"在 {industry} 行业共有 {resonance_count} 支个股在近期实现放量突破"
        
        # === 新增：收集所有用于打分的原始行情数据 ===
        raw_market_data = {
            'basic_info': {
                'code': stock_code,
                'name': stock_name,
                'industry_full': full_industry_name,
                'industry_short': industry_name
            },
            'current_stock_data': dict(stock),  # 当前股票的所有原始数据
            'fund_flow_data': history_info,  # 资金面历史数据
            'technical_indicators': tech_info,  # 技术指标数据
            'industry_technical': industry_info,  # 行业技术指标
            'resonance_data': {
                'info': resonance_info,
                'count': resonance_count,
                'industry': resonance_data.get('industry', '') if stock_resonance_info and stock_code in stock_resonance_info else ''
            }
        }
        
        # 构建等同于提供给大模型的Prompt原始数据
        prompt_data = f"""【股票代码】
{stock_code}
【股票名称】
{stock_name}
"""
        
        # 添加资金面情况
        if history_info:
            prompt_data += f"【资金面情况】\n{history_info}\n"
        
        # 添加技术指标信息
        prompt_data += f"【技术指标】\n{tech_info}\n\n"
        
        # 添加行业技术指标信息
        if industry_info:
            prompt_data += f"【所属行业技术指标】\n{industry_info}\n"
            
            # 添加行业共振信息
            if resonance_info:
                prompt_data += f"\n【行业共振趋势】\n{resonance_info}\n"
        
        # 开始本地计算各维度评分
        item_scores = {}
        items = ["多周期共振判断", "短线趋势判断", "资金面判断", "消息面判断", "行业趋势及共振判断", "基本面判断", "盈亏比判断"]
        
        # 详细评分日志
        detailed_log = f"""
=== 股票 {stock_code}({stock_name}) 本地计算详细日志 ===

【完整原始行情数据】
股票基本信息:
  代码: {stock_code}
  名称: {stock_name}
  所属行业(完整): {full_industry_name}
  所属行业(简称): {industry_name}

当前股票数据(所有字段):"""
        
        # 记录当前股票的所有原始数据字段
        for key, value in stock.items():
            detailed_log += f"\n  {key}: {value}"
        
        detailed_log += f"""

资金面历史数据:
{history_info if history_info else '无资金面数据'}

技术指标完整数据:
{tech_info if tech_info else '无技术指标数据'}

行业技术指标数据:
{industry_info if industry_info else '无行业技术指标数据'}

行业共振数据:
  共振信息: {resonance_info if resonance_info else '无共振信息'}
  共振数量: {resonance_count}
  所属行业: {resonance_data.get('industry', '') if stock_resonance_info and stock_code in stock_resonance_info else ''}

【原始数据 - 等同于大模型Prompt】
{prompt_data}

【各维度评分计算过程】
"""
        
        # 0. 多周期共振判断 (合并原超短趋势判断和中线趋势判断)
        score_0, decision_process_0, _ = calculate_multi_period_resonance_score_detailed(tech_info)
        item_scores["多周期共振判断"] = {"score": score_0, "reason": decision_process_0}
        detailed_log += f"0. 多周期共振判断：{score_0}分\n   决策过程：{decision_process_0}\n\n"
        
        # 1. 短线趋势判断 (日线级)
        score_1, decision_process_1, _ = calculate_trend_score_detailed(tech_info, "短线", 10, 5)  # 日线级
        item_scores["短线趋势判断"] = {"score": score_1, "reason": decision_process_1}
        detailed_log += f"1. 短线趋势判断：{score_1}分\n   决策过程：{decision_process_1}\n\n"
        
        # 2. 资金面判断
        score_2, decision_process_2, _ = calculate_fund_score_detailed(history_info)
        item_scores["资金面判断"] = {"score": score_2, "reason": decision_process_2}
        detailed_log += f"2. 资金面判断：{score_2}分\n   决策过程：{decision_process_2}\n\n"
        
        # 3. 消息面判断
        score_3, decision_process_3, _ = calculate_news_score_detailed(tech_info, is_local_calculation=True)
        item_scores["消息面判断"] = {"score": score_3, "reason": decision_process_3}
        detailed_log += f"3. 消息面判断：{score_3}分\n   决策过程：{decision_process_3}\n\n"
        
        # 4. 行业趋势及共振判断 (合并原行业趋势判断和行业共振判断)
        score_4, decision_process_4, _ = calculate_industry_trend_resonance_score_detailed(industry_info, resonance_count)
        item_scores["行业趋势及共振判断"] = {"score": score_4, "reason": decision_process_4}
        detailed_log += f"4. 行业趋势及共振判断：{score_4}分\n   决策过程：{decision_process_4}\n\n"

        # 5. 基本面判断
        score_5, decision_process_5, details_5 = calculate_fundamental_score_detailed(stock_code)
        item_scores["基本面判断"] = {"score": score_5, "reason": decision_process_5}
        detailed_log += f"5. 基本面判断：{score_5}分\n   决策过程：{decision_process_5}\n\n"

        # 将基本面数据添加到raw_market_data中
        raw_market_data['fundamental_data'] = details_5

        # 6. 盈亏比判断
        score_6, decision_process_6, _ = calculate_profit_loss_ratio_score_detailed(tech_info)
        item_scores["盈亏比判断"] = {"score": score_6, "reason": decision_process_6}
        detailed_log += f"6. 盈亏比判断：{score_6}分\n   决策过程：{decision_process_6}\n\n"
        
        # 计算总分
        total_score = sum(item_scores[item]["score"] for item in item_scores)
        
        # 完善详细评分日志
        detailed_log += f"【最终评分】\n总分：{total_score}分\n"
        detailed_log += "=" * 80 + "\n\n"
        
        # 生成分析结果文本
        rationale = f"本地计算结果 - {stock_code}({stock_name}):\n\n"
        for i, item in enumerate(items):
            score = item_scores[item]["score"]
            decision_process = item_scores[item]["reason"]
            rationale += f"{i}. {item}：{score}分。{decision_process}\n\n"
        
        rationale += f"总评分：{total_score}分"
        
        # 记录到日志文件 - 增加完整的原始数据记录（如果model_log_file不为None）
        if model_log_file:
            with output_file_lock:
                with open(model_log_file, "a", encoding="utf-8") as logfile:
                    logfile.write(f"="*50 + "\n")
                    logfile.write(f"本地计算 - 股票: {stock_code}({stock_name})\n")
                    logfile.write(f"="*50 + "\n")
                    
                    # === 新增：记录完整的原始行情数据 ===
                    logfile.write(f"【完整原始行情数据记录】\n")
                    logfile.write(f"股票基本信息:\n")
                    logfile.write(f"  代码: {raw_market_data['basic_info']['code']}\n")
                    logfile.write(f"  名称: {raw_market_data['basic_info']['name']}\n")
                    logfile.write(f"  所属行业(完整): {raw_market_data['basic_info']['industry_full']}\n")
                    logfile.write(f"  所属行业(简称): {raw_market_data['basic_info']['industry_short']}\n")
                    
                    logfile.write(f"\n当前股票数据(所有字段):\n")
                    for key, value in raw_market_data['current_stock_data'].items():
                        logfile.write(f"  {key}: {value}\n")
                    
                    logfile.write(f"\n资金面历史数据:\n")
                    if raw_market_data['fund_flow_data']:
                        logfile.write(f"{raw_market_data['fund_flow_data']}\n")
                    else:
                        logfile.write("无资金面数据\n")
                    
                    logfile.write(f"\n技术指标完整数据:\n")
                    if raw_market_data['technical_indicators']:
                        logfile.write(f"{raw_market_data['technical_indicators']}\n")
                    else:
                        logfile.write("无技术指标数据\n")
                    
                    logfile.write(f"\n行业技术指标数据:\n")
                    if raw_market_data['industry_technical']:
                        logfile.write(f"{raw_market_data['industry_technical']}\n")
                    else:
                        logfile.write("无行业技术指标数据\n")
                    
                    logfile.write(f"\n行业共振数据:\n")
                    logfile.write(f"  共振信息: {raw_market_data['resonance_data']['info']}\n")
                    logfile.write(f"  共振数量: {raw_market_data['resonance_data']['count']}\n")
                    logfile.write(f"  所属行业: {raw_market_data['resonance_data']['industry']}\n")

                    # 添加基本面数据到日志
                    if 'fundamental_data' in raw_market_data:
                        logfile.write(f"\n基本面数据:\n")
                        fundamental_data = raw_market_data['fundamental_data']

                        # PE分析结果
                        if 'pe_data' in fundamental_data:
                            pe_data = fundamental_data['pe_data']
                            if 'error' not in pe_data:
                                logfile.write(f"a. 市盈率分析结果\n")
                                logfile.write(f"- 当前扣非PE: {pe_data.get('current_pe', 'N/A')}\n")
                                logfile.write(f"- PE-TTM近3年百分位: {pe_data.get('percentile', 'N/A')}%\n")
                                logfile.write(f"- 最新数据日期: {pe_data.get('latest_date', 'N/A')}\n")
                            else:
                                logfile.write(f"a. 市盈率分析结果\n")
                                logfile.write(f"查询失败: {pe_data.get('error', '未知错误')}\n")

                        # ROE分析结果
                        if 'roe_data' in fundamental_data:
                            roe_data = fundamental_data['roe_data']
                            if 'error' not in roe_data:
                                logfile.write(f"\nc. ROE分析结果\n")
                                logfile.write(f"- ROE：{roe_data.get('roe', 'N/A')}%\n")
                                logfile.write(f"- 计算时间：{roe_data.get('calculation_date', 'N/A')}\n")
                            else:
                                logfile.write(f"\nc. ROE分析结果\n")
                                logfile.write(f"查询失败: ROE数据获取失败\n")

                        # 季度增长率分析结果
                        if 'quarterly_data' in fundamental_data:
                            quarterly_result = fundamental_data['quarterly_data']
                            if 'error' not in quarterly_result and 'quarterly_data' in quarterly_result:
                                logfile.write(f"\nb. 季度增长率分析结果\n")
                                logfile.write(f"\n营收季度增长率（同比）\n")
                                for quarter in quarterly_result['quarterly_data']:
                                    quarter_str = quarter['quarter']
                                    revenue_growth = quarter['revenue_growth']
                                    revenue_str = f"{revenue_growth:+.2f}%" if revenue_growth is not None else "N/A"
                                    logfile.write(f"- {quarter_str}：{revenue_str}\n")

                                logfile.write(f"\n扣非利润增长率（同比）\n")
                                for quarter in quarterly_result['quarterly_data']:
                                    quarter_str = quarter['quarter']
                                    profit_growth = quarter['profit_growth']
                                    profit_str = f"{profit_growth:+.2f}%" if profit_growth is not None else "N/A"
                                    logfile.write(f"- {quarter_str}：{profit_str}\n")
                            else:
                                logfile.write(f"\nb. 季度增长率分析结果\n")
                                logfile.write(f"查询失败: 季度数据获取失败\n")

                        logfile.write(f"\n基本面评分条件满足情况:\n")
                        logfile.write(f"- 条件a(PE分位≤50%): {'满足' if fundamental_data.get('condition_a', False) else '不满足'}\n")
                        logfile.write(f"- 条件b(ROE≥15%): {'满足' if fundamental_data.get('condition_b', False) else '不满足'}\n")
                        logfile.write(f"- 条件c(增长率之和>50%或扭亏): {'满足' if fundamental_data.get('condition_c', False) else '不满足'}\n")
                        logfile.write(f"- 满足条件数量: {fundamental_data.get('conditions_met', 0)}/3\n")
                    else:
                        logfile.write(f"\n基本面数据: 未获取\n")
                    
                    logfile.write(f"\n【模型输入数据】\n")
                    logfile.write(f"{prompt_data}\n")
                    logfile.write(f"-"*50 + "\n")
                    
                    logfile.write(f"计算结果:\n{rationale}\n")
                    logfile.write(f"="*50 + "\n\n")
            
            with open(output_file, "a", encoding="utf-8") as fout:
                fout.write(f"股票分析: {stock_code}({stock_name})\n")
                fout.write(f"="*60 + "\n")
                
                # === 修改：在输出文件中记录完整的原始行情数据，而不仅仅是摘要 ===
                fout.write(f"【完整原始行情数据】\n")
                fout.write(f"股票基本信息:\n")
                fout.write(f"  代码: {raw_market_data['basic_info']['code']}\n")
                fout.write(f"  名称: {raw_market_data['basic_info']['name']}\n")
                fout.write(f"  所属行业(完整): {raw_market_data['basic_info']['industry_full']}\n")
                fout.write(f"  所属行业(简称): {raw_market_data['basic_info']['industry_short']}\n")
                
                fout.write(f"\n当前股票数据(所有字段):\n")
                for key, value in raw_market_data['current_stock_data'].items():
                    fout.write(f"  {key}: {value}\n")
                
                fout.write(f"\n资金面历史数据:\n")
                if raw_market_data['fund_flow_data']:
                    fout.write(f"{raw_market_data['fund_flow_data']}\n")
                else:
                    fout.write("无资金面数据\n")
                
                fout.write(f"\n技术指标完整数据:\n")
                if raw_market_data['technical_indicators']:
                    # 限制技术指标长度，避免文件过大
                    tech_data = raw_market_data['technical_indicators']
                    if len(tech_data) > 5000:
                        fout.write(f"{tech_data[:5000]}...\n[技术指标数据过长，已截断，完整数据请查看: {model_log_file}]\n")
                    else:
                        fout.write(f"{tech_data}\n")
                else:
                    fout.write("无技术指标数据\n")
                
                fout.write(f"\n行业技术指标数据:\n")
                if raw_market_data['industry_technical']:
                    # 限制行业技术指标长度
                    industry_data = raw_market_data['industry_technical']
                    if len(industry_data) > 3000:
                        fout.write(f"{industry_data[:3000]}...\n[行业技术指标数据过长，已截断，完整数据请查看: {model_log_file}]\n")
                    else:
                        fout.write(f"{industry_data}\n")
                else:
                    fout.write("无行业技术指标数据\n")
                
                fout.write(f"\n行业共振数据:\n")
                fout.write(f"  共振信息: {raw_market_data['resonance_data']['info']}\n")
                fout.write(f"  共振数量: {raw_market_data['resonance_data']['count']}\n")
                fout.write(f"  所属行业: {raw_market_data['resonance_data']['industry']}\n")

                # 添加基本面数据输出
                if 'fundamental_data' in raw_market_data:
                    fout.write(f"\n基本面数据:\n")
                    fundamental_data = raw_market_data['fundamental_data']

                    # PE分析结果
                    if 'pe_data' in fundamental_data:
                        pe_data = fundamental_data['pe_data']
                        if 'error' not in pe_data:
                            fout.write(f"a. 市盈率分析结果\n")
                            fout.write(f"- 当前扣非PE: {pe_data.get('current_pe', 'N/A')}\n")
                            fout.write(f"- PE-TTM近3年百分位: {pe_data.get('percentile', 'N/A')}%\n")
                            fout.write(f"- 最新数据日期: {pe_data.get('latest_date', 'N/A')}\n")
                        else:
                            fout.write(f"a. 市盈率分析结果\n")
                            fout.write(f"查询失败: {pe_data.get('error', '未知错误')}\n")

                    # ROE分析结果
                    if 'roe_data' in fundamental_data:
                        roe_data = fundamental_data['roe_data']
                        if 'error' not in roe_data:
                            fout.write(f"\nc. ROE分析结果\n")
                            fout.write(f"- ROE：{roe_data.get('roe', 'N/A')}%\n")
                            fout.write(f"- 计算时间：{roe_data.get('calculation_date', 'N/A')}\n")
                        else:
                            fout.write(f"\nc. ROE分析结果\n")
                            fout.write(f"查询失败: ROE数据获取失败\n")

                    # 季度增长率分析结果
                    if 'quarterly_data' in fundamental_data:
                        quarterly_result = fundamental_data['quarterly_data']
                        if 'error' not in quarterly_result and 'quarterly_data' in quarterly_result:
                            fout.write(f"\nb. 季度增长率分析结果\n")
                            fout.write(f"\n营收季度增长率（同比）\n")
                            for quarter in quarterly_result['quarterly_data']:
                                quarter_str = quarter['quarter']
                                revenue_growth = quarter['revenue_growth']
                                revenue_str = f"{revenue_growth:+.2f}%" if revenue_growth is not None else "N/A"
                                fout.write(f"- {quarter_str}：{revenue_str}\n")

                            fout.write(f"\n扣非利润增长率（同比）\n")
                            for quarter in quarterly_result['quarterly_data']:
                                quarter_str = quarter['quarter']
                                profit_growth = quarter['profit_growth']
                                profit_str = f"{profit_growth:+.2f}%" if profit_growth is not None else "N/A"
                                fout.write(f"- {quarter_str}：{profit_str}\n")
                        else:
                            fout.write(f"\nb. 季度增长率分析结果\n")
                            fout.write(f"查询失败: 季度数据获取失败\n")

                    fout.write(f"\n基本面评分条件满足情况:\n")
                    fout.write(f"- 条件a(PE分位≤50%): {'满足' if fundamental_data.get('condition_a', False) else '不满足'}\n")
                    fout.write(f"- 条件b(ROE≥15%): {'满足' if fundamental_data.get('condition_b', False) else '不满足'}\n")
                    fout.write(f"- 条件c(增长率之和>50%或扭亏): {'满足' if fundamental_data.get('condition_c', False) else '不满足'}\n")
                    fout.write(f"- 满足条件数量: {fundamental_data.get('conditions_met', 0)}/3\n")
                else:
                    fout.write(f"\n基本面数据: 未获取\n")
                
                fout.write(f"\n【模型输入数据】\n")
                if len(prompt_data) > 2000:
                    fout.write(f"{prompt_data[:2000]}...\n[输入数据过长，已截断，完整数据请查看: {model_log_file}]\n")
                else:
                    fout.write(f"{prompt_data}\n")
                
                fout.write(f"\n【本地计算结果】\n")
                fout.write(f"{rationale}\n")
                fout.write(f"\n计算总评分：{total_score}\n")
                scores_str = ", ".join([f"{item}：{item_scores[item]['score']}分" for item in item_scores])
                fout.write(f"分项评分：{scores_str}\n")
                fout.write(f"="*60 + "\n\n")
            
            # === 修复：确保detailed_log_file的正确写入（如果model_log_file不为None）===
            if model_log_file:
                detailed_log_file = "local_calculation_detailed_log.txt"
    
                # 如果日志文件不存在，则先创建并写入标题
                if not os.path.exists(detailed_log_file):
                    with open(detailed_log_file, "w", encoding="utf-8") as detail_file:
                        detail_file.write("本地计算详细评分日志 - 全部股票（含完整原始数据）\n")
                        detail_file.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                        detail_file.write("说明：本文件包含每只股票的完整原始行情数据和详细评分过程\n")
                        detail_file.write("=" * 80 + "\n\n")
    
                # 追加当前股票的评分日志
                with open(detailed_log_file, "a", encoding="utf-8") as detail_file:
                    detail_file.write(detailed_log)
                    detail_file.flush()  # 确保数据立即写入磁盘
        
        # 将结果放入队列
        output_queue.put((stock_index, stock_code, stock_name, total_score, rationale, item_scores))
        print(f"第{stock_index+1}只股票本地计算完成: {stock_code}({stock_name}) = {total_score}分")
        
        return total_score, rationale, item_scores
        
    except Exception as e:
        error_msg = f"第{stock_index+1}只股票本地计算出错: {e}"
        print(error_msg)
        
        with output_file_lock:
            with open(output_file, "a", encoding="utf-8") as fout:
                fout.write(f"{error_msg}\n")
                fout.write(f"错误详情: {traceback.format_exc()}\n\n")
        
        return None, None, None

def calculate_trend_score_detailed(tech_info, trend_type, high_threshold, low_threshold):
    """计算趋势判断评分（详细版本，返回信号信息）
    
    Args:
        tech_info: 技术指标信息
        trend_type: 趋势类型 ("超短线", "短线", "中线")
        high_threshold: 高阈值百分比
        low_threshold: 低阈值百分比
    
    Returns:
        tuple: (评分, 理由, 检测到的信号列表)
    """
    try:
        # 初始化检测到的信号列表
        detected_signals = []
        
        # 查找对应的趋势行情和技术指标部分
        trend_section = ""
        tech_section = ""
        
        # 趋势类型 ("超短线", "短线", "中线")
        if trend_type == "超短线":
            trend_section = _robust_extract_section(tech_info, "超短线趋势行情")
            tech_section = _robust_extract_section(tech_info, "超短线技术指标")
        elif trend_type == "短线":
            trend_section = _robust_extract_section(tech_info, "短线趋势行情")
            tech_section = _robust_extract_section(tech_info, "短线技术指标")
        elif trend_type == "中线":
            trend_section = _robust_extract_section(tech_info, "中线趋势行情")
            tech_section = _robust_extract_section(tech_info, "中线技术指标")

        combined_info = (trend_section + "\n" + tech_section).strip()
        
        if not combined_info.strip():
            return 0, f"未找到{trend_type}相关技术指标信息", "无技术指标信息"
        
        # 添加技术指标内容的预览（用于调试）
        tech_preview = combined_info[:200].replace('\n', ' ') + "..." if len(combined_info) > 200 else combined_info.replace('\n', ' ')
        detected_signals.append(f"[调试]技术指标内容预览: {tech_preview}")
        
        # 1. 判定趋势阶段
        trend_stage_score = 2  # 默认中等
        stage_signals = []
        
        # === 信号库扩充 : 趋势开始(3 分) ===
        start_signals = [
            # DIF
            "DIF拐头向上",
            # RSI
            "RSI6上穿50",
            # MA5
            "MA5拐头向上",
            # MACD 金叉
            "MACD金叉",
            # MA5/MA10 交叉
            "MA5上穿MA10",
        ]
        # 趋势确认阶段信号 (2分)  
        confirm_signals = [
            "DIF持续上涨", 
            "MACD红柱持续变长",
            "RSI6始终运行在50以上",
        ]
        # 趋势结束阶段信号 (-1分)
        end_signals = [
            "DIF拐头向下",
            "MACD红柱开始缩短",
            "MACD死叉",
            "MA5下穿MA10",
            "RSI6下穿50",
            "DIF持续下降"
        ]
        
        # 检查各阶段信号并记录具体信号 - 使用不区分大小写的包含匹配
        found_start_signals = []
        found_confirm_signals = []
        found_end_signals = []

        # 使用不区分大小写的包含匹配
        combined_info_lower = combined_info.lower()

        # 新增：提取信号及其出现的日期时间信息，用于判断时间先后
        def extract_signal_dates(signals, text, trend_type):
            """提取信号及其出现的日期时间，返回[(信号, 日期时间), ...]格式

            Args:
                signals: 信号列表
                text: 技术指标文本
                trend_type: 趋势类型（"超短线"/"短线"/"中线"）
            """
            signal_dates = []
            for signal in signals:
                if signal.lower() in text.lower():
                    # 查找信号在文本中的位置
                    signal_pos = text.lower().find(signal.lower())
                    if signal_pos != -1:
                        # 找到信号所在的行
                        lines = text.split('\n')
                        signal_line = ""
                        signal_line_index = -1

                        # 找到包含信号的行
                        for i, line in enumerate(lines):
                            if signal.lower() in line.lower():
                                signal_line = line
                                signal_line_index = i
                                break

                        if signal_line:
                            # 根据趋势类型选择不同的时间提取模式
                            if trend_type == "超短线":
                                # 超短线：提取日期+时间（如：2025-07-14 15:00）
                                datetime_matches = re.findall(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2})', signal_line)
                                if datetime_matches:
                                    signal_datetime = datetime_matches[0]
                                    signal_dates.append((signal, signal_datetime))
                                else:
                                    # 如果信号行没有完整日期时间，在前面几行中查找
                                    found_datetime = False
                                    for j in range(max(0, signal_line_index - 3), signal_line_index):
                                        prev_line = lines[j]
                                        datetime_matches = re.findall(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2})', prev_line)
                                        if datetime_matches:
                                            signal_datetime = datetime_matches[-1]  # 取最后一个日期时间
                                            signal_dates.append((signal, signal_datetime))
                                            found_datetime = True
                                            break

                                    if not found_datetime:
                                        # 如果前面几行也没找到，使用默认日期时间
                                        signal_dates.append((signal, "1900-01-01 00:00"))
                            else:
                                # 短线和中线：只提取日期（如：2025-07-14）
                                date_matches = re.findall(r'(\d{4}-\d{2}-\d{2})', signal_line)
                                if date_matches:
                                    signal_date = date_matches[0]
                                    signal_dates.append((signal, signal_date))
                                else:
                                    # 如果信号行没有日期，在前面几行中查找
                                    found_date = False
                                    for j in range(max(0, signal_line_index - 3), signal_line_index):
                                        prev_line = lines[j]
                                        date_matches = re.findall(r'(\d{4}-\d{2}-\d{2})', prev_line)
                                        if date_matches:
                                            signal_date = date_matches[-1]  # 取最后一个日期
                                            signal_dates.append((signal, signal_date))
                                            found_date = True
                                            break

                                    if not found_date:
                                        # 如果前面几行也没找到，使用默认日期
                                        signal_dates.append((signal, "1900-01-01"))
            return signal_dates

        # 提取各类信号及其日期时间
        start_signal_dates = extract_signal_dates(start_signals, combined_info, trend_type)
        confirm_signal_dates = extract_signal_dates(confirm_signals, combined_info, trend_type)
        end_signal_dates = extract_signal_dates(end_signals, combined_info, trend_type)

        # 从信号日期列表中提取信号名称
        found_start_signals = [signal for signal, _ in start_signal_dates]
        found_confirm_signals = [signal for signal, _ in confirm_signal_dates]
        found_end_signals = [signal for signal, _ in end_signal_dates]

        has_start = len(found_start_signals) > 0
        has_confirm = len(found_confirm_signals) > 0
        has_end = len(found_end_signals) > 0

        # 添加调试信息
        debug_info = f"[调试]技术信息长度:{len(combined_info)}, 开始信号:{len(found_start_signals)}, 确认信号:{len(found_confirm_signals)}, 结束信号:{len(found_end_signals)}"
        detected_signals.append(debug_info)

        # --- 新的阶段判定逻辑（修正信号优先级和时间比较）---
        # 规则：
        # 1. 开始信号 vs 结束信号：比较时间，谁最后出现就算作谁
        # 2. 确认信号 vs 结束信号：直接以结束信号为准（不比较时间）
        # 3. 开始信号 vs 确认信号：比较时间，谁最后出现就算作谁

        if has_start and has_end:
            # 开始信号与结束信号同时存在：比较时间，谁最后出现就算作谁
            latest_start_datetime = "1900-01-01 00:00" if trend_type == "超短线" else "1900-01-01"
            latest_end_datetime = "1900-01-01 00:00" if trend_type == "超短线" else "1900-01-01"

            for signal, signal_datetime in start_signal_dates:
                if signal_datetime > latest_start_datetime:
                    latest_start_datetime = signal_datetime

            for signal, signal_datetime in end_signal_dates:
                if signal_datetime > latest_end_datetime:
                    latest_end_datetime = signal_datetime

            # 比较开始信号和结束信号的时间
            if latest_end_datetime > latest_start_datetime:
                # 结束信号更晚，以结束信号为准
                trend_stage_score = 0
                stage_signals.append(f"同时检测到开始和结束信号，结束信号更晚({latest_end_datetime} > {latest_start_datetime})，以结束信号为准")
                detected_signals.extend([f"趋势结束:{s}" for s in found_end_signals])
            else:
                # 开始信号更晚或同时，以开始信号为准
                if len(found_start_signals) >= 2:
                    trend_stage_score = 3
                    stage_signals.append(f"同时检测到开始和结束信号，开始信号更晚或同时({latest_start_datetime} >= {latest_end_datetime})，以开始信号为准（>=2个）")
                elif len(found_start_signals) == 1:
                    trend_stage_score = 2
                    stage_signals.append(f"同时检测到开始和结束信号，开始信号更晚或同时({latest_start_datetime} >= {latest_end_datetime})，以开始信号为准（1个）")
                else:
                    trend_stage_score = 1
                    stage_signals.append(f"同时检测到开始和结束信号，开始信号更晚或同时({latest_start_datetime} >= {latest_end_datetime})，以开始信号为准（0个）")
                detected_signals.extend([f"趋势开始:{s}" for s in found_start_signals])
        elif has_confirm and has_end:
            # 确认信号与结束信号同时存在：直接以结束信号为准（不比较时间）
            trend_stage_score = 0
            stage_signals.append("同时检测到确认和结束信号，直接以结束信号为准")
            detected_signals.extend([f"趋势结束:{s}" for s in found_end_signals])
        elif has_end:
            # 只有结束信号
            trend_stage_score = 0
            stage_signals.append("检测到趋势结束信号")
            detected_signals.extend([f"趋势结束:{s}" for s in found_end_signals])
        elif has_start and has_confirm:
            # 只有开始信号和确认信号时，才需要比较时间先后
            # 找到最晚的开始信号时间和最晚的确认信号时间
            latest_start_datetime = "1900-01-01 00:00" if trend_type == "超短线" else "1900-01-01"
            latest_confirm_datetime = "1900-01-01 00:00" if trend_type == "超短线" else "1900-01-01"

            for signal, signal_datetime in start_signal_dates:
                if signal_datetime > latest_start_datetime:
                    latest_start_datetime = signal_datetime

            for signal, signal_datetime in confirm_signal_dates:
                if signal_datetime > latest_confirm_datetime:
                    latest_confirm_datetime = signal_datetime

            # 比较开始信号和确认信号的时间
            if latest_start_datetime > latest_confirm_datetime:
                # 开始信号更晚，以开始信号为准
                if len(found_start_signals) >= 2:
                    trend_stage_score = 3
                    stage_signals.append(f"同时检测到开始和确认信号，开始信号更晚({latest_start_datetime} > {latest_confirm_datetime})，以开始信号为准（>=2个）")
                elif len(found_start_signals) == 1:
                    trend_stage_score = 2
                    stage_signals.append(f"同时检测到开始和确认信号，开始信号更晚({latest_start_datetime} > {latest_confirm_datetime})，以开始信号为准（1个）")
                else:
                    trend_stage_score = 1
                    stage_signals.append(f"同时检测到开始和确认信号，开始信号更晚({latest_start_datetime} > {latest_confirm_datetime})，以开始信号为准（0个）")
                detected_signals.extend([f"趋势开始:{s}" for s in found_start_signals])
            else:
                # 确认信号更晚或同时，以确认信号为准
                trend_stage_score = 2
                stage_signals.append(f"同时检测到开始和确认信号，确认信号更晚或同时({latest_confirm_datetime} >= {latest_start_datetime})，以确认信号为准")
                detected_signals.extend([f"趋势确认:{s}" for s in found_confirm_signals])
        elif has_confirm:
            # 只有确认信号
            trend_stage_score = 2
            stage_signals.append("检测到趋势确认信号")
            detected_signals.extend([f"趋势确认:{s}" for s in found_confirm_signals])
        elif has_start:
            # 只有开始信号
            if len(found_start_signals) >= 2:
                trend_stage_score = 3
                stage_signals.append("检测到趋势开始信号（>=2个）")
            elif len(found_start_signals) == 1:
                trend_stage_score = 2
                stage_signals.append("检测到趋势开始信号（1个）")
            else:
                trend_stage_score = 1
                stage_signals.append("检测到趋势开始信号（0个）")
            detected_signals.extend([f"趋势开始:{s}" for s in found_start_signals])
        
        # 2. 判定趋势强度
        trend_strength_score = 2  # 默认中等
        strength_signals = []
        
        # 趋势强度-强信号 (3分) - 严格按照用户指定的信号
        strong_signals = [
            # 放量
            "放量超过MAVOL5两倍以上", 
            # 量能连续上涨
            "4根及以上K线的MAVOL5上涨",
            "4根及以上K线的VOL大于MAVOL5",
            # 背离、价量
            "MACD强势无顶背离",
            "量价齐升无背离",
            "MACD底背离",
            # 股价趋势
            "股价持续运行在MA5之上"
        ]
        # 趋势强度-弱信号 (1 分)
        weak_signals = [
            "3根及以上K线的MAVOL5下跌",
            "4根及以上K线的VOL小于MAVOL5",
            "量价顶背离","量价齐跌无背离",
            "MACD弱势无底背离", "MACD顶背离"
        ]
        
        # 检查强度信号 - 使用不区分大小写的匹配
        found_strong_signals = []
        found_weak_signals = []
        
        for signal in strong_signals:
            if signal.lower() in combined_info_lower:
                found_strong_signals.append(signal)
        
        for signal in weak_signals:
            if signal.lower() in combined_info_lower:
                found_weak_signals.append(signal)
        
        has_strong = len(found_strong_signals) > 0
        has_weak = len(found_weak_signals) > 0
        
        # 添加强度调试信息
        strength_debug = f"[调试]强势信号:{len(found_strong_signals)}, 弱势信号:{len(found_weak_signals)}"
        detected_signals.append(strength_debug)
        
        # 添加详细的信号匹配调试信息
        if found_strong_signals:
            detected_signals.append(f"[调试]找到的强势信号: {', '.join(found_strong_signals)}")
        if found_weak_signals:
            detected_signals.append(f"[调试]找到的弱势信号: {', '.join(found_weak_signals)}")
        
        # 添加更详细的调试信息，帮助诊断匹配问题
        detected_signals.append(f"[调试]定义的强势信号列表: {', '.join(strong_signals)}")
        detected_signals.append(f"[调试]检查'4根及以上K线的MAVOL5上涨'是否在技术信息中: {'4根及以上K线的MAVOL5上涨' in combined_info}")
        detected_signals.append(f"[调试]检查'DIF持续在0轴上方'是否在技术信息中: {'DIF持续在0轴上方' in combined_info}")
        detected_signals.append(f"[调试]检查'DIF持续在0轴上方'是否在强势信号列表中: {'DIF持续在0轴上方' in strong_signals}")
        
        # 检查是否包含背离相关内容
        if "背离" in combined_info_lower:
            detected_signals.append(f"[调试]技术信息包含背离相关内容")
            # 提取背离相关的行
            divergence_lines = [line.strip() for line in combined_info.split('\n') if '背离' in line]
            if divergence_lines:
                detected_signals.append(f"[调试]背离相关行: {'; '.join(divergence_lines[:3])}")  # 只显示前3行
        else:
            detected_signals.append(f"[调试]技术信息不包含背离相关内容")
        
        # 当同时有强势和弱势信号时，根据数量判断
        if has_strong and has_weak:
            if len(found_strong_signals) >= len(found_weak_signals):
                # 检查是否同时包含特殊信号组合
                has_macd_strong_divergence = "MACD强势无顶背离" in found_strong_signals
                has_price_volume_rise = "量价齐升无背离" in found_strong_signals
                
                if has_macd_strong_divergence and has_price_volume_rise:
                    # 特殊规则：同时包含这两个信号时，需要至少3个强势信号才能得3分
                    if len(found_strong_signals) >= 3:
                        trend_strength_score = 3
                        strength_signals.append(f"检测到强势信号({len(found_strong_signals)})多于弱势信号({len(found_weak_signals)})且>=3个（含特殊信号组合）")
                    else:
                        trend_strength_score = 2
                        strength_signals.append(f"检测到强势信号({len(found_strong_signals)})多于弱势信号({len(found_weak_signals)})但<3个（含特殊信号组合，需>=3个）")
                else:
                    # 常规规则：需要至少2个强势信号才能得3分
                    if len(found_strong_signals) >= 2:
                        trend_strength_score = 3
                        strength_signals.append(f"检测到强势信号({len(found_strong_signals)})多于弱势信号({len(found_weak_signals)})且>=2个")
                    else:
                        trend_strength_score = 2
                        strength_signals.append(f"检测到强势信号({len(found_strong_signals)})多于弱势信号({len(found_weak_signals)})但<2个")
                detected_signals.extend([f"趋势强势:{s}" for s in found_strong_signals])
                detected_signals.extend([f"趋势弱势:{s}" for s in found_weak_signals])
            else:
                trend_strength_score = 1
                strength_signals.append(f"检测到弱势信号({len(found_weak_signals)})多于强势信号({len(found_strong_signals)})")
                detected_signals.extend([f"趋势强势:{s}" for s in found_strong_signals])
                detected_signals.extend([f"趋势弱势:{s}" for s in found_weak_signals])
        elif has_strong:
            # 检查是否同时包含特殊信号组合
            has_macd_strong_divergence = "MACD强势无顶背离" in found_strong_signals
            has_price_volume_rise = "量价齐升无背离" in found_strong_signals
            
            if has_macd_strong_divergence and has_price_volume_rise:
                # 特殊规则：同时包含这两个信号时，需要至少3个强势信号才能得3分
                if len(found_strong_signals) >= 3:
                    trend_strength_score = 3
                    strength_signals.append(f"检测到强势信号（>=3个，含特殊信号组合）")
                else:
                    trend_strength_score = 2
                    strength_signals.append(f"检测到强势信号（{len(found_strong_signals)}个，含特殊信号组合，需>=3个）")
            else:
                # 常规规则：需要至少2个强势信号才能得3分
                if len(found_strong_signals) >= 2:
                    trend_strength_score = 3
                    strength_signals.append(f"检测到强势信号（>=2个）")
                else:
                    trend_strength_score = 2
                    strength_signals.append(f"检测到强势信号（1个）")
            detected_signals.extend([f"趋势强势:{s}" for s in found_strong_signals])
        elif has_weak:
            trend_strength_score = 1
            strength_signals.append("检测到弱势信号")
            detected_signals.extend([f"趋势弱势:{s}" for s in found_weak_signals])
        
        # 计算总分（只包含阶段和强度两项，最多6分）
        total_trend_score = trend_stage_score + trend_strength_score
        final_score = 1 if total_trend_score > 4 else 0
        
        # 生成决策过程描述
        decision_process = f"检查{trend_type}技术指标发现："
        
        # 趋势阶段决策过程 - 更新为与新的时间优先级逻辑一致
        stage_process = ""
        for signal_msg in stage_signals:
            if stage_process:
                stage_process += "；"
            stage_process += signal_msg

        # 如果没有生成stage_signals，使用默认逻辑
        if not stage_process:
            if has_end:
                stage_process = f"检测到趋势结束信号({len(found_end_signals)}个)：{', '.join(['\"' + s + '\"' for s in found_end_signals])}，因此趋势阶段判定为结束阶段({trend_stage_score}分)"
            elif has_start and has_confirm:
                stage_process = f"检测到趋势开始信号({len(found_start_signals)}个)和确认信号({len(found_confirm_signals)}个)，按时间优先级处理，趋势阶段得{trend_stage_score}分"
            elif has_confirm:
                stage_process = f"检测到趋势确认信号({len(found_confirm_signals)}个)：{', '.join(['\"' + s + '\"' for s in found_confirm_signals])}，因此趋势阶段判定为确认阶段({trend_stage_score}分)"
            elif has_start:
                if len(found_start_signals) >= 2:
                    stage_process = f"检测到趋势开始信号({len(found_start_signals)}个)：{', '.join(['\"' + s + '\"' for s in found_start_signals])}，信号数量>=2个，因此趋势阶段判定为开始阶段({trend_stage_score}分)"
                elif len(found_start_signals) == 1:
                    stage_process = f"检测到趋势开始信号({len(found_start_signals)}个)：{', '.join(['\"' + s + '\"' for s in found_start_signals])}，信号数量=1个，因此趋势阶段判定为较强阶段({trend_stage_score}分)"
                else:
                    stage_process = f"检测到趋势开始信号({len(found_start_signals)}个)，信号数量=0个，因此趋势阶段判定为较弱阶段({trend_stage_score}分)"
            else:
                stage_process = f"未检测到\"DIF值拐头向上\"、\"RSI6上穿50\"、\"MA5拐头向上\"、\"MACD金叉\"、\"MA5上穿MA10\"等趋势开始信号，也未检测到趋势确认信号和趋势结束信号，因此趋势阶段判定为中等({trend_stage_score}分)"
        
        # 趋势强度决策过程
        if has_strong and has_weak:
            if len(found_strong_signals) >= len(found_weak_signals):
                # 检查是否包含特殊信号组合
                has_macd_strong_divergence = "MACD强势无顶背离" in found_strong_signals
                has_price_volume_rise = "量价齐升无背离" in found_strong_signals
                
                if has_macd_strong_divergence and has_price_volume_rise:
                    if len(found_strong_signals) >= 3:
                        strength_process = f"检测到强势信号({len(found_strong_signals)}个)：{', '.join(['\"' + s + '\"' for s in found_strong_signals])}，弱势信号({len(found_weak_signals)}个)：{', '.join(['\"' + s + '\"' for s in found_weak_signals])}，强势信号更多且>=3个（含\"MACD强势无顶背离\"和\"量价齐升无背离\"特殊组合，需>=3个），因此趋势强度判定为强({trend_strength_score}分)"
                    else:
                        strength_process = f"检测到强势信号({len(found_strong_signals)}个)：{', '.join(['\"' + s + '\"' for s in found_strong_signals])}，弱势信号({len(found_weak_signals)}个)：{', '.join(['\"' + s + '\"' for s in found_weak_signals])}，强势信号更多但<3个（含\"MACD强势无顶背离\"和\"量价齐升无背离\"特殊组合，需>=3个），因此趋势强度判定为较强({trend_strength_score}分)"
                else:
                    if len(found_strong_signals) >= 2:
                        strength_process = f"检测到强势信号({len(found_strong_signals)}个)：{', '.join(['\"' + s + '\"' for s in found_strong_signals])}，弱势信号({len(found_weak_signals)}个)：{', '.join(['\"' + s + '\"' for s in found_weak_signals])}，强势信号更多且>=2个，因此趋势强度判定为强({trend_strength_score}分)"
                    else:
                        strength_process = f"检测到强势信号({len(found_strong_signals)}个)：{', '.join(['\"' + s + '\"' for s in found_strong_signals])}，弱势信号({len(found_weak_signals)}个)：{', '.join(['\"' + s + '\"' for s in found_weak_signals])}，强势信号更多但<2个，因此趋势强度判定为较强({trend_strength_score}分)"
            else:
                strength_process = f"检测到弱势信号({len(found_weak_signals)}个)：{', '.join(['\"' + s + '\"' for s in found_weak_signals])}，强势信号({len(found_strong_signals)}个)：{', '.join(['\"' + s + '\"' for s in found_strong_signals])}，弱势信号更多，因此趋势强度判定为弱({trend_strength_score}分)"
        elif has_strong:
            # 检查是否包含特殊信号组合
            has_macd_strong_divergence = "MACD强势无顶背离" in found_strong_signals
            has_price_volume_rise = "量价齐升无背离" in found_strong_signals
            
            if has_macd_strong_divergence and has_price_volume_rise:
                if len(found_strong_signals) >= 3:
                    strength_process = f"检测到强势信号({len(found_strong_signals)}个)：{', '.join(['\"' + s + '\"' for s in found_strong_signals])}，含\"MACD强势无顶背离\"和\"量价齐升无背离\"特殊组合且>=3个，因此趋势强度判定为强({trend_strength_score}分)"
                else:
                    strength_process = f"检测到强势信号({len(found_strong_signals)}个)：{', '.join(['\"' + s + '\"' for s in found_strong_signals])}，含\"MACD强势无顶背离\"和\"量价齐升无背离\"特殊组合但<3个（需>=3个），因此趋势强度判定为较强({trend_strength_score}分)"
            else:
                if len(found_strong_signals) >= 2:
                    strength_process = f"检测到强势信号({len(found_strong_signals)}个)：{', '.join(['\"' + s + '\"' for s in found_strong_signals])}，信号数量>=2个，因此趋势强度判定为强({trend_strength_score}分)"
                else:
                    strength_process = f"检测到强势信号({len(found_strong_signals)}个)：{', '.join(['\"' + s + '\"' for s in found_strong_signals])}，信号数量=1个，因此趋势强度判定为较强({trend_strength_score}分)"
        elif has_weak:
            strength_process = f"检测到弱势信号({len(found_weak_signals)}个)：{', '.join(['\"' + s + '\"' for s in found_weak_signals])}，因此趋势强度判定为弱({trend_strength_score}分)"
        else:
            strength_process = f"未检测到强势信号和弱势信号，因此趋势强度判定为中等({trend_strength_score}分)"
        
        # 最终判定过程
        final_process = f"两项合计{total_trend_score}分，{'大于4分' if total_trend_score > 4 else '小于等于4分'}，因此本维度得{final_score}分"
        
        decision_process += f"在趋势阶段判断中，{stage_process}；在趋势强度判断中，{strength_process}。{final_process}。"
        
        # 构建详细计数字典
        details = {
            'start_cnt': len(found_start_signals),
            'confirm_cnt': len(found_confirm_signals),
            'strong_cnt': len(found_strong_signals),
            'end_cnt': len(found_end_signals),
            'weak_cnt': len(found_weak_signals),
            'subtotal': total_trend_score
        }
        
        return final_score, decision_process, details
        
    except Exception as e:
        return 0, f"计算{trend_type}趋势评分出错: {str(e)}", {}

def calculate_trend_score(tech_info, trend_type, high_threshold, low_threshold):
    """计算趋势判断评分（简化版本，保持兼容性）"""
    score, reason, _ = calculate_trend_score_detailed(tech_info, trend_type, high_threshold, low_threshold)
    return score, reason

def extract_price_space(text, position_type):
    """从文本中提取价格空间信息"""
    try:
        # 查找压力位或支撑位相关信息
        import re
        
        # 查找距离压力位/支撑位的空间信息，支持百分比格式
        pattern = rf"距{position_type}[^0-9]*?([0-9.]+)%"
        match = re.search(pattern, text)
        
        if match:
            return float(match.group(1))
        
        # 如果没找到百分比，尝试查找其他格式
        pattern = rf"距{position_type}[^0-9]*?([0-9.]+)"
        match = re.search(pattern, text)
        
        if match:
            return float(match.group(1))
            
        return None
        
    except Exception:
        return None

def calculate_fund_score_detailed(history_info):
    """计算资金面判断评分（详细版本）"""
    try:
        if not history_info:
            return 0, "无资金面数据", "无资金面数据"
        
        # 统计主力净额为正和主买净额为正的天数
        positive_main_flow_days = 0
        positive_main_buy_days = 0
        detailed_data = []
        
        lines = history_info.split('\n')
        for line in lines:
            # 支持两种格式：制表符分隔和竖线分隔
            if ("\t" in line or " | " in line) and not line.startswith("日期") and not line.startswith("---"):
                # 首选制表符分隔，其次是竖线分隔
                if "\t" in line:
                    parts = line.split("\t")
                else:
                    parts = line.split(" | ")
                    
                if len(parts) >= 5:
                    date_str = parts[0].strip()
                    try:
                        # 解析主力净额 (第2列)
                        main_flow_str = parts[1].strip()
                        main_flow_positive = False
                        if main_flow_str not in ["未知", "N/A"] and "万" in main_flow_str:
                            # 处理负数
                            main_flow_val = main_flow_str.replace("万", "").replace("-", "")
                            main_flow = float(main_flow_val)
                            if not main_flow_str.startswith("-") and main_flow > 0:
                                positive_main_flow_days += 1
                                main_flow_positive = True
                        
                        # 解析主买净额 (第3列)
                        main_buy_str = parts[2].strip()
                        main_buy_positive = False
                        if main_buy_str not in ["未知", "N/A"] and "万" in main_buy_str:
                            # 处理负数
                            main_buy_val = main_buy_str.replace("万", "").replace("-", "")
                            main_buy = float(main_buy_val)
                            if not main_buy_str.startswith("-") and main_buy > 0:
                                positive_main_buy_days += 1
                                main_buy_positive = True
                        
                        # 记录每日详情
                        if main_flow_positive or main_buy_positive:
                            detailed_data.append(f"{date_str}:主力净额{'正' if main_flow_positive else '负'},主买净额{'正' if main_buy_positive else '负'}")
                    except (ValueError, IndexError):
                        continue
        
        total_positive_days = positive_main_flow_days + positive_main_buy_days
        score = 1 if total_positive_days >= 6 else 0
        
        # 生成决策过程描述
        decision_process = f"检查资金面情况发现：近5个交易日中，主力净额为正的天数有{positive_main_flow_days}天，主买净额为正的天数有{positive_main_buy_days}天，两项合计{total_positive_days}天。"
        if detailed_data:
            decision_process += f"具体为：{'; '.join(detailed_data)}。"
        decision_process += f"根据规则，合计天数{'≥6天' if total_positive_days >= 6 else '<6天'}，因此本维度得{score}分。"
        
        # 构建详细信息字典
        details = {
            'positive_days': total_positive_days
        }
        
        return score, decision_process, details
        
    except Exception as e:
        return 0, f"计算资金面评分出错: {str(e)}", {}

def calculate_fund_score(history_info):
    """计算资金面判断评分（简化版本，保持兼容性）"""
    score, reason, _ = calculate_fund_score_detailed(history_info)
    return score, reason

def calculate_news_score_detailed(tech_info, is_local_calculation=False):
    """计算消息面判断评分（详细版本）"""
    try:
        # 本地计算时直接返回0分
        if is_local_calculation:
            decision_process = "检查消息面情况：本地计算模式下，消息面统一置为0分，不进行具体的利好利空分析。"
            return 0, decision_process, {}
        
        # 查找新闻相关信息
        news_section = ""
        start_idx = tech_info.find("最近相关新闻")
        if start_idx != -1:
            end_idx = tech_info.find("\n\n", start_idx)
            if end_idx == -1:
                end_idx = len(tech_info)
            news_section = tech_info[start_idx:end_idx]
        
        if not news_section:
            decision_process = "检查消息面情况：未找到相关新闻信息，因此本维度得0分。"
            return 0, decision_process, {}
        
        # 利好关键词
        positive_keywords = ["利好", "买入", "持有", "推荐", "看好", "上调", "增持"]
        # 利空关键词  
        negative_keywords = ["利空", "卖出", "下调", "减持", "风险", "警示"]
        
        positive_signals = [keyword for keyword in positive_keywords if keyword in news_section]
        negative_signals = [keyword for keyword in negative_keywords if keyword in news_section]
        
        positive_count = len(positive_signals)
        negative_count = len(negative_signals)
        
        # 生成决策过程描述
        decision_process = f"检查消息面情况发现：在相关新闻中"
        if positive_signals:
            decision_process += f"检测到{positive_count}个利好信号（{', '.join(['\"' + s + '\"' for s in positive_signals])}）"
        else:
            decision_process += f"未检测到利好信号"
        
        if negative_signals:
            decision_process += f"，检测到{negative_count}个利空信号（{', '.join(['\"' + s + '\"' for s in negative_signals])}）"
        else:
            decision_process += f"，未检测到利空信号"
        
        if positive_count > negative_count:
            decision_process += f"。利好信号多于利空信号，因此本维度得1分。"
            return 1, decision_process, {}
        elif negative_count > positive_count:
            decision_process += f"。利空信号多于利空信号，因此本维度得-1分。"
            return -1, decision_process, {}
        else:
            decision_process += f"。利好利空信号数量相等，消息面中性，因此本维度得0分。"
            return 0, decision_process, {}
            
    except Exception as e:
        return 0, f"计算消息面评分出错: {str(e)}", {}

def calculate_news_score(tech_info, is_local_calculation=False):
    """计算消息面判断评分（简化版本，保持兼容性）"""
    score, reason, _ = calculate_news_score_detailed(tech_info, is_local_calculation)
    return score, reason

def calculate_industry_trend_score_detailed(industry_info):
    """计算行业趋势判断评分（详细版本）"""
    try:
        if not industry_info:
            decision_process = "检查行业趋势情况：未找到行业技术指标信息，因此本维度得0分。"
            return 0, decision_process, decision_process
        
        # 直接复用个股的短线趋势评分逻辑，保持调试信息格式一致
        # -----------------------------------------------------------------
        # 1) 计算行业短线趋势得分（调用与个股相同的函数）
        score, decision_process, details = calculate_trend_score_detailed(
            industry_info, "短线", 10, 5
        )

        # 2) 提取行业名称，用于在调试信息中标识
        import re
        def _extract_name(data: str) -> str:
            patterns = [
                r"所属行业\s+([^\s的]+)\s*的技术指标",
                r"^([^\s]+)\s+的技术指标分析：",
                r"行业板块\s*'([^']+)'",
            ]
            for pat in patterns:
                m = re.search(pat, data, re.MULTILINE)
                if m:
                    name = m.group(1).strip()
                    return name if not name.isdigit() else "未知行业"
            return "未知行业"

        industry_name = _extract_name(industry_info)

        # 3) 把文案中的"短线技术指标"改为"行业短线技术指标"，并加上行业前缀
        industry_decision = decision_process.replace("检查短线技术指标", "检查行业短线技术指标")
        industry_decision = f"【{industry_name} 行业分析】" + industry_decision

        # 4) 返回与个股评分一致的三元组（传递详细信息字典）
        return score, industry_decision, details
    except Exception as e:
        error_process = f"计算行业趋势评分时出错: {e}"
        return 0, error_process, {}

def calculate_industry_trend_score(industry_info):
    """计算行业趋势判断评分（简化版本，保持兼容性）"""
    score, reason, _ = calculate_industry_trend_score_detailed(industry_info)
    return score, reason

def calculate_resonance_score_detailed(resonance_count, resonance_info):
    """计算行业共振判断评分（详细版本）"""
    try:
        score = 1 if resonance_count >= 2 else 0
        
        # 生成决策过程描述
        decision_process = f"检查行业共振情况发现：该行业共有{resonance_count}支个股在近期实现放量突破。"
        if resonance_info and "行业" in resonance_info:
            decision_process += f"具体为：{resonance_info}。"
        decision_process += f"根据规则，共振股票数量{'≥2支' if resonance_count >= 2 else '<2支'}，因此本维度得{score}分。"
        
        # 构建详细信息字典
        details = {
            'resonance_count': resonance_count
        }
        
        return score, decision_process, details
        
    except Exception as e:
        error_process = f"计算行业共振评分时出现错误：{str(e)}"
        return 0, error_process, {}

def calculate_resonance_score(resonance_count, resonance_info):
    """计算行业共振判断评分（简化版本，保持兼容性）"""
    score, reason, _ = calculate_resonance_score_detailed(resonance_count, resonance_info)
    return score, reason

def _extract_pressure_space_from_tech_info(tech_info: str) -> float:
    """从完整的技术指标信息中提取真实的压力位空间百分比"""
    import re
    if not isinstance(tech_info, str): 
        return 0.0
    
    # 匹配程序化输出格式：   - 压力位1: 856.82 (0.49%向上空间), 强度: 2
    pattern = r"\s*-\s*压力位\d+:\s*[\d\.]+\s*\(([\d\.]+)%向上空间\)"
    matches = re.findall(pattern, tech_info)
    
    if matches:
        try:
            # 返回第一个压力位的向上空间百分比
            return float(matches[0])
        except (ValueError, TypeError):
            pass
    
    # 如果没有找到精确格式，尝试其他可能的格式
    # 匹配格式：压力位空间：X.X%
    pattern2 = r"压力位空间[:：]\s*([\d\.]+)%"
    matches2 = re.findall(pattern2, tech_info)
    if matches2:
        try:
            return float(matches2[0])
        except (ValueError, TypeError):
            pass
    
    # 匹配格式：距压力位空间X.X%
    pattern3 = r"距压力位空间\s*([\d\.]+)%"
    matches3 = re.findall(pattern3, tech_info)
    if matches3:
        try:
            return float(matches3[0])
        except (ValueError, TypeError):
            pass
    
    return 0.0

def calculate_profit_loss_ratio_score_detailed(tech_info):
    """计算盈亏比判断评分（详细版本）
    
    根据用户需求：
    1. 如果检测到"未找到有效压力位"，直接判定为1分
    2. 否则在输出中捕捉两个信号："总盈亏比大于2:1"和"距压力位空间大于10%"
       当这两个信号同时出现时，判定为1分，否则不得分。
    """
    try:
        if not tech_info:
            decision_process = "检查盈亏比情况：未找到技术指标信息，因此本维度得0分。"
            return 0, decision_process, decision_process
        
        # 直接在整个技术信息中搜索特定信号
        tech_info_lower = tech_info.lower()
        
        # 优先检查是否"未找到有效压力位"
        no_pressure_patterns = [
            "未找到有效压力位",
            "无压力位",
            "压力位不明确",
            "未识别到压力位",
            "压力位缺失",
            "没有找到压力位",
            "无有效压力位",
            "压力位未确定",
            "未检测到压力位"
        ]
        
        for pattern in no_pressure_patterns:
            if pattern.lower() in tech_info_lower:
                decision_process = f"检查盈亏比情况发现：检测到\"{pattern}\"，根据规则直接判定为1分。"
                details = {
                    'pressure_space_pct': 0.0,
                    'no_pressure_found': True
                }
                return 1, decision_process, details
        
        # 搜索两个特定信号
        signal1_found = False  # "总盈亏比大于2:1"
        signal2_found = False  # "距压力位空间大于10%"
        
        # 信号1：总盈亏比大于2:1 的各种表达方式
        signal1_patterns = [
            "总盈亏比大于2:1",
            "总盈亏比大于2：1",
            "总盈亏比>2:1",
            "总盈亏比>2：1",
            "盈亏比大于2:1",
            "盈亏比大于2：1",
            "盈亏比>2:1",
            "盈亏比>2：1"
        ]
        
        for pattern in signal1_patterns:
            if pattern.lower() in tech_info_lower:
                signal1_found = True
                break
        
        # 信号2：距压力位空间大于10% 的各种表达方式
        signal2_patterns = [
            "距压力位空间大于10%",
            "距离压力位空间大于10%",
            "距压力位向上空间大于10%",
            "距离压力位向上空间大于10%",
            "距压力位空间>10%",
            "距离压力位空间>10%",
            "距压力位向上空间>10%",
            "距离压力位向上空间>10%"
        ]
        
        for pattern in signal2_patterns:
            if pattern.lower() in tech_info_lower:
                signal2_found = True
                break
        
        # 判定逻辑：两个信号都找到才得分
        score = 0
        decision_process = "检查盈亏比情况发现："
        
        found_signals = []
        if signal1_found:
            found_signals.append("\"总盈亏比大于2:1\"")
        if signal2_found:
            found_signals.append("\"距压力位空间大于10%\"")
        
        # 从技术指标信息中提取真实的压力位空间百分比
        pressure_pct = _extract_pressure_space_from_tech_info(tech_info)
        
        if signal1_found and signal2_found:
            score = 1
            decision_process += f"检测到两个关键信号：{' 和 '.join(found_signals)}，两个信号同时出现，满足条件，因此本维度得1分。"
            # 如果提取不到真实数值，默认为大于10%的一个合理值
            if pressure_pct == 0.0:
                pressure_pct = 12.0  # 默认为12%而不是10%，表示大于10%
        elif signal1_found or signal2_found:
            score = 0
            missing_signal = "\"距压力位空间大于10%\"" if signal1_found else "\"总盈亏比大于2:1\""
            decision_process += f"只检测到信号{found_signals[0]}，但未检测到{missing_signal}，需要两个信号同时出现才能得分，因此本维度得0分。"
            # 根据检测到的信号推测，如果检测不到真实数值，使用默认值
            if pressure_pct == 0.0:
                pressure_pct = 12.0 if signal2_found else 5.0  # 根据检测到的信号推测
        else:
            score = 0
            decision_process += "未检测到\"总盈亏比大于2:1\"和\"距压力位空间大于10%\"这两个关键信号，因此本维度得0分。"
            # 如果提取不到真实数值，设为0
            if pressure_pct == 0.0:
                pressure_pct = 0.0
        
        # 构建详细信息字典
        details = {
            'pressure_space_pct': pressure_pct,
            'no_pressure_found': False
        }
        
        return score, decision_process, details
        
    except Exception as e:
        error_process = f"计算盈亏比评分时出现错误：{str(e)}"
        return 0, error_process, {}

def calculate_profit_loss_ratio_score(tech_info):
    """计算盈亏比判断评分（简化版本，保持兼容性）"""
    score, reason, _ = calculate_profit_loss_ratio_score_detailed(tech_info)
    return score, reason

def parse_item_scores(rationale):
    """解析分项评分和理由"""
    # 更新为新的6个评分项
    items = ["多周期共振判断", "短线趋势判断", "资金面判断", "消息面判断", "行业趋势及共振判断", "盈亏比判断"]
    item_scores = {item: {"score": 0, "reason": ""} for item in items}
    
    # 1. 提取所有部分的文本段落
    sections = {}
    section_patterns = [
        (items[0], r'0\.\s*多周期共振判断[^1]*?(?=1\.|$)'),
        (items[1], r'1\.\s*短线趋势判断[^2]*?(?=2\.|$)'),
        (items[2], r'2\.\s*资金面判断[^3]*?(?=3\.|$)'),
        (items[3], r'3\.\s*消息面判断[^4]*?(?=4\.|$)'),
        (items[4], r'4\.\s*行业趋势及共振判断[^5]*?(?=5\.|$)'),
        (items[5], r'5\.\s*盈亏比判断.*?(?=$)')
    ]
    
    # 提取每个部分的文本段落
    for item, pattern in section_patterns:
        match = re.search(pattern, rationale, re.DOTALL | re.IGNORECASE)
        if match:
            sections[item] = match.group(0).strip()
    
    # 2. 从文本段落中提取分数
    for item, text in sections.items():
        if not text:
            continue  # 如果没有找到对应段落，跳过
            
        # 更严格的评分提取模式
        # 匹配格式：X. 项目名称：Y分。其中Y是-1, 0或1
        score_match = re.search(r'[:：]\s*([-]?[01])\s*分[。，\.]', text)
        
        if score_match:
            try:
                score_str = score_match.group(1)
                score = int(score_str)
                # 确保分数在有效范围内
                if score in [-1, 0, 1]:
                    item_scores[item]["score"] = score
                    item_scores[item]["reason"] = text
                else:
                    print(f"警告: 项目 {item} 提取到的分数 {score} 超出范围，设为0")
                    item_scores[item]["score"] = 0
                    item_scores[item]["reason"] = text
            except (ValueError, IndexError) as e:
                print(f"警告: 解析项目 {item} 的分数时出错: {e}，根据关键词判断")
                # 解析失败时，根据关键词判断
                if "积极" in text or "看涨" in text or "上涨" in text or "正面" in text or "金叉" in text or "看多信号" in text:
                    item_scores[item]["score"] = 1
                    item_scores[item]["reason"] = text
                elif "负面" in text or "下跌" in text or "看跌" in text or "死叉" in text or "看空信号" in text:
                    item_scores[item]["score"] = 0  # 改为0分
                    item_scores[item]["reason"] = text
                else:
                    item_scores[item]["score"] = 0
                    item_scores[item]["reason"] = text
        else:
            # 尝试其他可能的评分格式
            alternate_match = re.search(r'[得评][分值]\s*[:：]\s*([-]?[01])', text)
            if alternate_match:
                try:
                    score = int(alternate_match.group(1))
                    if score in [-1, 0, 1]:
                        item_scores[item]["score"] = score
                        item_scores[item]["reason"] = text
                    else:
                        item_scores[item]["score"] = 0
                        item_scores[item]["reason"] = text
                except (ValueError, IndexError):
                    item_scores[item]["score"] = 0
                    item_scores[item]["reason"] = text
            else:
                # 根据关键词判断
                if "积极" in text or "看涨" in text or "上涨" in text or "正面" in text or "金叉" in text or "看多信号" in text:
                    item_scores[item]["score"] = 1
                    item_scores[item]["reason"] = text
                elif "负面" in text or "下跌" in text or "看跌" in text or "死叉" in text or "看空信号" in text:
                    item_scores[item]["score"] = 0  # 改为0分
                    item_scores[item]["reason"] = text
                else:
                    item_scores[item]["score"] = 0
                    item_scores[item]["reason"] = text
        
        # 添加调试输出
        print(f"解析[{item}]: 找到文本=\"{text[:50]}...\"，提取分数={item_scores[item]['score']}")
    
    # 检查是否有项目分数没有提取到
    missing_items = [item for item in items if item not in sections]
    if missing_items:
        print(f"警告: 以下项目没有找到对应段落: {', '.join(missing_items)}")
        # 尝试更简单的正则表达式查找漏掉的项目
        for item in missing_items:
            # 提取项目序号
            item_index = items.index(item)
            
            # 需要转换为从0开始的序号形式
            item_index_in_output = item_index
            
            # 改进的搜索模式，精确匹配项目编号和分数
            # 使用两种模式：一种是匹配标准格式，一种是匹配包含项目名称的行然后在附近找分数
            simple_pattern = rf'{item_index_in_output}\.\s*{re.escape(item)}[:：]\s*([-]?[01])\s*分'
            alt_pattern = rf'{item_index_in_output}\.\s*{re.escape(item)}[^。]*?(?:[。：:]\s*)([-]?[01])\s*分'
            
            # 先尝试标准格式
            simple_match = re.search(simple_pattern, rationale, re.DOTALL | re.IGNORECASE)
            if simple_match:
                try:
                    score = int(simple_match.group(1))
                    item_scores[item]["score"] = score
                    item_scores[item]["reason"] = simple_match.group(0)
                    print(f"通过精确模式解析[{item}]: 分数={score}, 匹配文本=\"{simple_match.group(0)}\"")
                    continue
                except (ValueError, IndexError) as e:
                    print(f"精确模式解析[{item}]失败: {e}")
            
            # 再尝试替代格式
            alt_match = re.search(alt_pattern, rationale, re.DOTALL | re.IGNORECASE)
            if alt_match:
                try:
                    score = int(alt_match.group(1))
                    item_scores[item]["score"] = score
                    item_scores[item]["reason"] = alt_match.group(0)
                    print(f"通过替代模式解析[{item}]: 分数={score}, 匹配文本=\"{alt_match.group(0)}\"")
                    continue
                except (ValueError, IndexError) as e:
                    print(f"替代模式解析[{item}]失败: {e}")
            
            # 最后尝试直接在整个文本中查找该项目对应的完整段落
            full_pattern = rf'{item_index_in_output}\.\s*{re.escape(item)}[^{item_index_in_output+1}]*?(?=\n{item_index_in_output+1}\.|$)'
            full_match = re.search(full_pattern, rationale, re.DOTALL | re.IGNORECASE)
            if full_match:
                text = full_match.group(0)
                # 在提取的段落中查找分数
                score_match = re.search(r'[:：]\s*([-]?[01])\s*分', text, re.DOTALL)
                if score_match:
                    try:
                        score = int(score_match.group(1))
                        item_scores[item]["score"] = score
                        item_scores[item]["reason"] = text
                        print(f"通过段落模式解析[{item}]: 分数={score}, 段落开头=\"{text[:50]}...\"")
                    except (ValueError, IndexError) as e:
                        print(f"段落中分数解析[{item}]失败: {e}")
                else:
                    # 查找关键词，尝试判断分数
                    if "积极" in text or "看涨" in text or "上涨" in text or "正面" in text or "支持" in text or "利好" in text or "看多信号" in text:
                        score = 1
                    elif "负面" in text or "下跌" in text or "看跌" in text or "负相关" in text or "不支持" in text or "看空信号" in text:
                        score = 0  # 改为0分
                    else:
                        score = 0
                    
                    item_scores[item]["score"] = score
                    item_scores[item]["reason"] = text
                    print(f"通过关键词判断[{item}]: 分数={score}, 段落开头=\"{text[:50]}...\"")
            else:
                # 使用回退方案：直接搜索项目序号和分数的简单模式
                last_pattern = rf'{item_index_in_output}\.\s*.*?([-]?[01])\s*分'
                last_match = re.search(last_pattern, rationale, re.DOTALL | re.IGNORECASE)
                if last_match:
                    try:
                        score = int(last_match.group(1))
                        item_scores[item]["score"] = score
                        item_scores[item]["reason"] = last_match.group(0)
                        print(f"通过简化模式解析[{item}]: 分数={score}, 匹配文本=\"{last_match.group(0)}\"")
                    except (ValueError, IndexError) as e:
                        print(f"简化模式解析[{item}]失败: {e}")
    
    # 打印最终解析结果的汇总
    print("最终解析结果:")
    for item in items:
        print(f"  {item}: {item_scores[item]['score']}分")
    
    # 计算并打印总分
    total_score = sum(item_scores[item]["score"] for item in item_scores)
    print(f"计算总分: {total_score}")
    
    return item_scores

def analyze_stock(stock_index, stock, stock_tech_info, client, output_queue, output_file_lock, output_file, original_headers, model_log_file, max_retries=3, retry_delay=5, industry_tech_info=None, stock_to_industry=None, stock_resonance_info=None):
    """分析单只股票并返回评分结果，增加重试机制"""
    stock_code = stock.get('代码', '未知')
    stock_name = stock.get('名称', '未知')
    
    # 从预先获取的技术面信息中获取
    tech_info = stock_tech_info.get(stock_code, "")
    
    # 获取历史交易数据（现在只返回资金面情况），确保使用真实数据
    history_info = get_historical_data(stock_code, original_headers, current_stock=stock, use_simulation=False)
    
    # 获取原始历史数据（用于提取量比和换手Z）
    raw_history_info = ""
    try:
        # 尝试从通达信目录获取历史数据
        all_stock_files = glob.glob(os.path.join("D:\\stock\\tdxdata", "全部Ａ股*.xls")) + glob.glob(os.path.join("D:\\stock\\tdxdata", "全部Ａ股*.txt"))
        
        # 如果没有找到标准格式文件，尝试查找其他可能的命名格式
        if not all_stock_files:
            print("未找到标准格式的文件，尝试搜索其他命名格式...")
            # 尝试查找'沪深A股*'格式
            all_stock_files = glob.glob(os.path.join("D:\\stock\\tdxdata", "沪深A股*.xls")) + glob.glob(os.path.join("D:\\stock\\tdxdata", "沪深A股*.txt"))
            # 尝试查找'全部股票*'格式
            if not all_stock_files:
                all_stock_files = glob.glob(os.path.join("D:\\stock\\tdxdata", "全部股票*.xls")) + glob.glob(os.path.join("D:\\stock\\tdxdata", "全部股票*.txt"))
            # 尝试查找可能有空格的格式
            if not all_stock_files:
                all_stock_files = glob.glob(os.path.join("D:\\stock\\tdxdata", "全部 A股*.xls")) + glob.glob(os.path.join("D:\\stock\\tdxdata", "全部 A股*.txt"))
                all_stock_files += glob.glob(os.path.join("D:\\stock\\tdxdata", "沪深 A股*.xls")) + glob.glob(os.path.join("D:\\stock\\tdxdata", "沪深 A股*.txt"))
        
        # 按照日期排序文件（从文件名中提取日期）
        def extract_date(filename):
            # 尝试多种格式的日期提取
            # 1. 标准格式："全部Ａ股20250101.xls"
            date_match = re.search(r'全部Ａ股(\d{8})', os.path.basename(filename))
            if date_match:
                date_str = date_match.group(1)
                return datetime.strptime(date_str, '%Y%m%d')
            
            # 2. 沪深A股格式："沪深A股20250101.xls"
            date_match = re.search(r'沪深A股(\d{8})', os.path.basename(filename))
            if date_match:
                date_str = date_match.group(1)
                return datetime.strptime(date_str, '%Y%m%d')
                
            # 3. 全部股票格式："全部股票20250101.xls"
            date_match = re.search(r'全部股票(\d{8})', os.path.basename(filename))
            if date_match:
                date_str = date_match.group(1)
                return datetime.strptime(date_str, '%Y%m%d')
            
            # 4. 带空格的格式："全部 A股20250101.xls" 或 "沪深 A股20250101.xls"
            date_match = re.search(r'(全部|沪深)\s+A股(\d{8})', os.path.basename(filename))
            if date_match:
                date_str = date_match.group(2)
                return datetime.strptime(date_str, '%Y%m%d')
            
            # 5. 一般格式：尝试直接从文件名中提取8位数字作为日期
            date_match = re.search(r'(\d{8})', os.path.basename(filename))
            if date_match:
                date_str = date_match.group(1)
                try:
                    return datetime.strptime(date_str, '%Y%m%d')
                except ValueError:
                    pass
                    
            return datetime.min
        
        # 获取最近5个文件
        recent_files = sorted(all_stock_files, key=extract_date, reverse=True)[:5]
        
        # 从这些文件中提取股票数据
        for file_path in recent_files:
            try:
                file_date = extract_date(file_path)
                date_str = file_date.strftime('%Y-%m-%d')
                
                # 检查文件是否是真正的Excel文件
                is_real_excel = False
                if file_path.lower().endswith('.xls'):
                    try:
                        with open(file_path, 'rb') as f:
                            header = f.read(8)
                            # 检查文件头部是否为Excel文件标识
                            is_real_excel = header.startswith(b'\xD0\xCF\x11\xE0') or header.startswith(b'PK\x03\x04')
                    except Exception:
                        is_real_excel = False
                
                stock_row = None
                
                if is_real_excel:
                    try:
                        if file_path.lower().endswith('.xls'):
                            df = pd.read_excel(file_path, engine='xlrd')
                        else:
                            df = pd.read_excel(file_path, engine='openpyxl')
                            
                        # 查找股票代码
                        for _, row in df.iterrows():
                            if '代码' in df.columns and pd.notna(row['代码']):
                                row_code = clean_stock_code(row['代码'])
                                if row_code == stock_code:
                                    stock_row = row.to_dict()
                                    break
                    except Exception as e:
                        print(f"以Excel格式读取历史文件失败: {e}")
                else:
                    # 不是真正的Excel文件，尝试文本方式读取
                    try:
                        with open(file_path, 'r', encoding='gbk', errors='ignore') as f:
                            lines = f.readlines()
                        
                        if not lines:
                            continue
                        
                        # 解析表头
                        file_header = [col.strip() for col in lines[0].strip().split('\t') if col.strip()]
                        if not file_header:
                            continue
                            
                        # 查找股票
                        for line in lines[1:]:
                            if not line.strip():
                                continue
                            
                            values = [val.strip() for val in line.strip().split('\t')]
                            if len(values) >= len(file_header):
                                row_data = dict(zip(file_header, values))
                                if '代码' in row_data and clean_stock_code(row_data['代码']) == stock_code:
                                    stock_row = row_data
                                    break
                    except Exception as e:
                        print(f"以文本格式读取历史文件失败: {e}")
                
                # 如果找到了对应股票的数据，提取量比和换手Z等信息
                if stock_row:
                    data_str = f"【{date_str}交易数据】\n"
                    data_str += f"代码: {stock_code}\n"
                    data_str += f"名称: {stock_name}\n"
                    
                    # 提取关键信息
                    for key, value in stock_row.items():
                        if value and not pd.isna(value):
                            key_lower = key.lower()
                            if '量比' in key_lower or '换手z' in key_lower.replace(' ', '') or '主力净额' in key_lower or '主买净额' in key_lower or '内盘' in key_lower or '外盘' in key_lower:
                                data_str += f"{key}: {value}\n"
                    
                    raw_history_info += data_str + "\n\n"
            except Exception as e:
                print(f"处理历史文件 {file_path} 时出错: {e}")
        
        # 如果没有从历史文件中获取到数据，使用资金面情况表格构造
        if not raw_history_info and history_info:
            recent_dates = []
            # 从已获取的资金面情况表中提取日期
            for line in history_info.split('\n'):
                if " | " in line and not line.startswith("日期") and not line.startswith("---"):
                    parts = line.split(" | ")
                    if len(parts) >= 5:
                        date_str = parts[0].strip()
                        try:
                            # 验证是否为有效日期格式
                            datetime.strptime(date_str, '%Y-%m-%d')
                            recent_dates.append(date_str)
                        except:
                            pass
            
            # 使用当前股票信息和日期构造历史数据
            for date_str in recent_dates:
                data_str = f"【{date_str}交易数据】\n"
                data_str += f"代码: {stock_code}\n"
                data_str += f"名称: {stock_name}\n"
                
                # 添加关键数据（如量比、换手Z等）
                for key, value in stock.items():
                    if key in ['量比', '换手Z', '主力净额', '主买净额', '内盘', '外盘']:
                        data_str += f"{key}: {value}\n"
                
                raw_history_info += data_str + "\n\n"
            
    except Exception as e:
        print(f"构造历史数据时出错: {e}")
    
    # 增强技术指标信息，添加量比和换手Z - 符合Wstock_tech.py的输出格式
    enhanced_tech_info = enhance_tech_info(tech_info, stock, raw_history_info)
    
    # 提取行业信息和技术指标
    industry_info = ""
    industry_name = ""
    full_industry_name = ""
    
    if stock_to_industry and stock_code in stock_to_industry:
        full_industry_name = stock_to_industry[stock_code]
        # 提取连接线后面部分作为主要行业
        industry_name = full_industry_name.split('-')[-1] if '-' in full_industry_name else full_industry_name
        
        if industry_tech_info and industry_name in industry_tech_info:
            industry_info = industry_tech_info[industry_name]
    
    # 新增：添加行业共振信息
    resonance_info = ""
    resonance_count = 0
    if stock_resonance_info and stock_code in stock_resonance_info:
        resonance_data = stock_resonance_info[stock_code]
        industry = resonance_data.get('industry', full_industry_name)
        resonance_count = resonance_data.get('count', 0)
        
        if industry:
            resonance_info = f"\n【行业共振趋势】\n在 {industry} 行业共有 {resonance_count} 支个股在近期实现放量突破\n"
    elif full_industry_name:
        # 没有行业共振信息但有行业信息时也输出
        resonance_info = f"\n【行业共振趋势】\n在 {full_industry_name} 行业共有 0 支个股在近期实现放量突破\n"
    
    # === 新增：收集所有用于打分的原始行情数据 ===
    raw_market_data = {
        'basic_info': {
            'code': stock_code,
            'name': stock_name,
            'industry_full': full_industry_name,
            'industry_short': industry_name
        },
        'current_stock_data': dict(stock),  # 当前股票的所有原始数据
        'fund_flow_data': history_info,  # 资金面历史数据
        'technical_indicators': enhanced_tech_info,  # 技术指标数据（增强后的）
        'original_technical': tech_info,  # 原始技术指标数据
        'raw_historical_data': raw_history_info,  # 原始历史数据
        'industry_technical': industry_info,  # 行业技术指标
        'resonance_data': {
            'info': resonance_info,
            'count': resonance_count,
            'industry': resonance_data.get('industry', '') if stock_resonance_info and stock_code in stock_resonance_info else ''
        }
    }
    
    # 构建prompt，去除对股票基本信息的引用
    prompt = f"""以下是某股票最新及最近几个交易日的行情信息和技术指标，请分别判断以下6个方面是否能够支撑未来1~5天的股价上涨，在每个方面都需尽量寻找"看多信号"和"看空信号"（多个信号之间相对独立，避免相关性过强），最终依据两种信号的数量决定评分——当"看多信号"数量多于"看空信号"数量时，该方面判定得1分，反之不得分：

0. 多周期共振判断：分别参考"超短线趋势行情"、"超短线技术指标"、"中线趋势行情"和"中线技术指标"项目数据，判断超短线(30分钟级)与中线(周线级)是否形成共振。当超短线趋势判断和中线趋势判断至少有一个为"看多信号"多于"看空信号"时，该方面判定得1分。超短线"看多信号"包括并不限于："DIF值拐头向上""RSI6上穿50""MA5拐头向上""DIF值持续上涨""MACD红柱持续变长""MACD金叉""MA5上穿MA10""股价持续运行在MA5之上""RSI6始终运行在50以上""放量超过MAVOL5两倍以上""4根及以上K线的MAVOL5上涨""4根及以上K线的VOL大于MAVOL5""MACD弱势无顶背离""MACD强势无顶背离""价量齐升""量价齐升""MACD底背离""(30分钟级)距压力位空间大于距支撑位空间的2倍""(30分钟级)距压力位空间大于2%"等。中线"看多信号"可参考超短线部分描述，只是级别需改为周线级别，将"距压力位空间大于2%"改为"距压力位空间大于10%"，将"距压力位空间小于1%"改为"距压力位空间小于5%"。
1. 短线趋势判断：参考"短线趋势行情"和"短线技术指标"项目数据，"看多信号"与"看空信号"可参考"0.多周期共振判断"部分描述，只是级别需改为日线级别，将"距压力位空间大于2%"改为"距压力位空间大于10%"，将"距压力位空间小于1%"改为"距压力位空间小于5%"。
2. 资金面判断：参考"资金面情况"项目，"看多信号"包括并不限于：当近5个交易日'主力净额为正'与'主买净额为正'的天数合计大于等于6天等。"看空信号"包括并不限于：近5个交易日'主力净额为正'与'主买净额为正'的天数合计小于6天等
3. 消息面判断：参考"最近相关新闻"项目，"看多信号"包括并不限于：有直接利好股价的消息、有建议"买入"或"持有"的研报等。"看空信号"包括并不限于：有直接利空股价的消息、有建议"卖出"的研报等，如只是中性消息或对行情的客观描述，不视为看多或看空信号。
4. 行业趋势及共振判断：参考所属行业的"短线趋势行情"、"短线技术指标"、"中线趋势行情"和"中线技术指标"以及"行业共振趋势"项目，"看多信号"包括：行业技术面看多信号(可参考"0.多周期共振判断"部分描述，注意级别需改为覆盖日线级别和周线级别)以及在该行业有大于等于2支个股近期实现放量突破。"看空信号"包括：行业技术面看空信号以及该行业共振股票数量少于2支。
5. 盈亏比判断：参考"距压力位"和"距支撑位"相关数据，"看多信号"为距压力位空间大、距支撑位空间小的情况，"看空信号"为距压力位空间小、距支撑位空间大的情况。
以上每个方面的"看多信号"多于"看空信号"，即可记做1分，"看多信号"少于等于"看空信号"时不计分(0分)，没有发现信号时也不计分。

【输出要求】
1. 请不要给出总分，只需逐项列出6个方面的单项评分和判断依据
2. 按0到5的顺序，依次评价每个方面
3. 每项评分必须使用严格统一的格式："X. 项目名称：Y分。",其中Y只能是0或1
4. 评分格式严格示例："1. 短线趋势判断：1分。理由：..." 或 "4. 行业趋势及共振判断：0分。原因：..."
5. 请注意，评分数字必须是0或1两个值之一，不能是其他数字
6. 在评分"原因"处，按照 "看多信号"：xxx, "看空信号":xxx 的格式写明原因

【股票代码】
{stock_code}
【股票名称】
{stock_name}
"""
    
    # 添加资金面情况
    if history_info:
        prompt += f"【资金面情况】\n{history_info}\n"
    
    # 添加技术指标信息 - 使用增强后的技术指标
    prompt += f"【技术指标】\n{enhanced_tech_info}\n\n"
    
    # 添加行业技术指标信息
    if industry_info:
        prompt += f"【所属行业技术指标】\n{industry_info}\n"
        
        # 添加行业共振信息（直接附加到行业技术指标后面）
        if resonance_info:
            prompt += resonance_info
    
    # 添加重试机制
    retries = 0
    last_error = None
    
    while retries < max_retries:
        try:
            # 记录请求开始时间
            start_time = time.time()
            
            # 记录到大模型日志 - 增加完整的原始数据记录（如果model_log_file不为None）
            if model_log_file:
                with output_file_lock:
                    with open(model_log_file, "a", encoding="utf-8") as logfile:
                        logfile.write(f"="*50 + "\n")
                        logfile.write(f"大模型分析 - 股票: {stock_code}({stock_name})\n")
                        logfile.write(f"="*50 + "\n")
                        
                        # === 新增：记录完整的原始行情数据 ===
                        logfile.write(f"【完整原始行情数据记录】\n")
                        logfile.write(f"股票基本信息:\n")
                        logfile.write(f"  代码: {raw_market_data['basic_info']['code']}\n")
                        logfile.write(f"  名称: {raw_market_data['basic_info']['name']}\n")
                        logfile.write(f"  所属行业(完整): {raw_market_data['basic_info']['industry_full']}\n")
                        logfile.write(f"  所属行业(简称): {raw_market_data['basic_info']['industry_short']}\n")
                        
                        logfile.write(f"\n当前股票数据(所有字段):\n")
                        for key, value in raw_market_data['current_stock_data'].items():
                            logfile.write(f"  {key}: {value}\n")
                        
                        logfile.write(f"\n资金面历史数据:\n")
                        if raw_market_data['fund_flow_data']:
                            logfile.write(f"{raw_market_data['fund_flow_data']}\n")
                        else:
                            logfile.write("无资金面数据\n")
                        
                        logfile.write(f"\n原始历史数据:\n")
                        if raw_market_data['raw_historical_data']:
                            logfile.write(f"{raw_market_data['raw_historical_data']}\n")
                        else:
                            logfile.write("无原始历史数据\n")
                        
                        logfile.write(f"\n技术指标完整数据(原始):\n")
                        if raw_market_data['original_technical']:
                            logfile.write(f"{raw_market_data['original_technical']}\n")
                        else:
                            logfile.write("无原始技术指标数据\n")
                        
                        logfile.write(f"\n技术指标完整数据(增强后):\n")
                        if raw_market_data['technical_indicators']:
                            logfile.write(f"{raw_market_data['technical_indicators']}\n")
                        else:
                            logfile.write("无增强技术指标数据\n")
                        
                        logfile.write(f"\n行业技术指标数据:\n")
                        if raw_market_data['industry_technical']:
                            logfile.write(f"{raw_market_data['industry_technical']}\n")
                        else:
                            logfile.write("无行业技术指标数据\n")
                        
                        logfile.write(f"\n行业共振数据:\n")
                        logfile.write(f"  共振信息: {raw_market_data['resonance_data']['info']}\n")
                        logfile.write(f"  共振数量: {raw_market_data['resonance_data']['count']}\n")
                        logfile.write(f"  所属行业: {raw_market_data['resonance_data']['industry']}\n")
                        
                        logfile.write(f"\n【发送给大模型的提示词内容】\n")
                        logfile.write(f"{prompt}\n")
                        logfile.write(f"-"*50 + "\n")
            
            # 使用模型客户端发送请求 - 使用非流式API
            # 直接使用 client.send 而不考虑流式 API
            result = client.send(prompt)
            
            # 记录请求结束时间
            end_time = time.time()
            elapsed = end_time - start_time
            
            # 记录大模型响应到日志
            with output_file_lock:
                with open(model_log_file, "a", encoding="utf-8") as logfile:
                    logfile.write(f"响应时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} (耗时: {elapsed:.2f}秒)\n")
                    logfile.write(f"模型响应:\n{result}\n")
                    logfile.write(f"="*50 + "\n\n")
            
            # 检查是否有有效响应
            if not validate_model_response(result):
                retries += 1
                log_message = f"第{stock_index+1}只股票分析响应无效 (尝试 {retries}/{max_retries}): {stock_code}({stock_name})"
                print(log_message)
                
                with output_file_lock:
                    with open(output_file, "a", encoding="utf-8") as fout:
                        fout.write(f"{log_message}\n响应内容: {result}\n")
                
                if retries < max_retries:
                    time.sleep(retry_delay)
                    continue
                else:
                    with output_file_lock:
                        with open(output_file, "a", encoding="utf-8") as fout:
                            fout.write(f"第{stock_index+1}只股票重试{max_retries}次后仍然失败: {stock_code}({stock_name})\n\n")
                    return None, None, None
            
            # 处理有效响应
            print(f"第{stock_index+1}只股票分析请求耗时: {elapsed:.2f}秒")
            rationale = result.strip()
            
            # 解析分项评分和理由
            print(f"开始解析股票 {stock_code} 的评分...")
            item_scores = parse_item_scores(rationale)
            
            # 从分项评分计算总分
            score = sum(item_scores[item]["score"] for item in item_scores)
            
            # 确保所有项的分数都在合法范围内
            for item in item_scores:
                if item_scores[item]["score"] not in [0, 1]:
                    print(f"警告：项目 {item} 的分数 {item_scores[item]['score']} 超出范围，重置为0")
                    item_scores[item]["score"] = 0  # 重置为0
            
            # 重新计算总分
            score = sum(item_scores[item]["score"] for item in item_scores)
            print(f"股票 {stock_code} 总评分: {score}")
            
            # 将结果写入输出文件（使用锁确保线程安全）
            with output_file_lock:
                with open(output_file, "a", encoding="utf-8") as fout:
                    fout.write(f"股票分析: {stock_code}({stock_name})\n")
                    fout.write(f"="*60 + "\n")
                    
                    # === 修改：在输出文件中记录完整的原始行情数据，而不仅仅是摘要 ===
                    fout.write(f"【完整原始行情数据】\n")
                    fout.write(f"股票基本信息:\n")
                    fout.write(f"  代码: {raw_market_data['basic_info']['code']}\n")
                    fout.write(f"  名称: {raw_market_data['basic_info']['name']}\n")
                    fout.write(f"  所属行业(完整): {raw_market_data['basic_info']['industry_full']}\n")
                    fout.write(f"  所属行业(简称): {raw_market_data['basic_info']['industry_short']}\n")
                    
                    fout.write(f"\n当前股票数据(所有字段):\n")
                    for key, value in raw_market_data['current_stock_data'].items():
                        fout.write(f"  {key}: {value}\n")
                    
                    fout.write(f"\n资金面历史数据:\n")
                    if raw_market_data['fund_flow_data']:
                        fout.write(f"{raw_market_data['fund_flow_data']}\n")
                    else:
                        fout.write("无资金面数据\n")
                    
                    fout.write(f"\n原始历史数据:\n")
                    if raw_market_data['raw_historical_data']:
                        # 限制原始历史数据长度
                        raw_data = raw_market_data['raw_historical_data']
                        if len(raw_data) > 2000:
                            fout.write(f"{raw_data[:2000]}...\n[原始历史数据过长，已截断，完整数据请查看: {model_log_file}]\n")
                        else:
                            fout.write(f"{raw_data}\n")
                    else:
                        fout.write("无原始历史数据\n")
                    
                    fout.write(f"\n技术指标完整数据(增强后):\n")
                    if raw_market_data['technical_indicators']:
                        # 限制技术指标长度，避免文件过大
                        tech_data = raw_market_data['technical_indicators']
                        if len(tech_data) > 5000:
                            fout.write(f"{tech_data[:5000]}...\n[技术指标数据过长，已截断，完整数据请查看: {model_log_file}]\n")
                        else:
                            fout.write(f"{tech_data}\n")
                    else:
                        fout.write("无增强技术指标数据\n")
                    
                    fout.write(f"\n行业技术指标数据:\n")
                    if raw_market_data['industry_technical']:
                        # 限制行业技术指标长度
                        industry_data = raw_market_data['industry_technical']
                        if len(industry_data) > 3000:
                            fout.write(f"{industry_data[:3000]}...\n[行业技术指标数据过长，已截断，完整数据请查看: {model_log_file}]\n")
                        else:
                            fout.write(f"{industry_data}\n")
                    else:
                        fout.write("无行业技术指标数据\n")
                    
                    fout.write(f"\n行业共振数据:\n")
                    fout.write(f"  共振信息: {raw_market_data['resonance_data']['info']}\n")
                    fout.write(f"  共振数量: {raw_market_data['resonance_data']['count']}\n")
                    fout.write(f"  所属行业: {raw_market_data['resonance_data']['industry']}\n")
                    
                    fout.write(f"\n【发送给大模型的提示词内容】\n")
                    if len(prompt) > 2000:
                        fout.write(f"{prompt[:2000]}...\n[提示词过长，已截断，完整数据请查看: {model_log_file}]\n")
                    else:
                        fout.write(f"{prompt}\n")
                    
                    fout.write(f"\n【大模型返回结果】\n{result}\n")
                    fout.write(f"\n计算总评分：{score}\n")
                    scores_str = ", ".join([f"{item}：{item_scores[item]['score']}分" for item in item_scores])
                    fout.write(f"分项评分：{scores_str}\n")
                    fout.write(f"="*60 + "\n\n")
            
            # 将结果放入队列，包括分项评分和理由
            output_queue.put((stock_index, stock_code, stock_name, score, rationale, item_scores))
            return score, rationale, item_scores
            
        except Exception as e:
            last_error = e
            retries += 1
            error_msg = f"第{stock_index+1}只股票处理出错 (尝试 {retries}/{max_retries}): {e}"
            print(error_msg)
            
            # 记录详细的错误信息
            with output_file_lock:
                with open(output_file, "a", encoding="utf-8") as fout:
                    fout.write(f"{error_msg}\n")
                    fout.write(f"错误详情: {traceback.format_exc()}\n\n")
                
                # 同时记录到大模型日志
                with open(model_log_file, "a", encoding="utf-8") as logfile:
                    logfile.write(f"错误时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    logfile.write(f"错误信息: {e}\n")
                    logfile.write(f"错误详情: {traceback.format_exc()}\n")
                    logfile.write(f"="*50 + "\n\n")
            
            if retries < max_retries:
                # 在重试前等待一段时间
                time.sleep(retry_delay)
                # 第二次重试之后增加等待时间
                retry_delay = retry_delay * 1.5
            else:
                with output_file_lock:
                    with open(output_file, "a", encoding="utf-8") as fout:
                        fout.write(f"第{stock_index+1}只股票重试{max_retries}次后仍然失败: {stock_code}({stock_name})\n\n")
                return None, None, None
    
    # 所有重试都失败
    print(f"第{stock_index+1}只股票所有重试均失败: {last_error}")
    return None, None, None

def process_results_thread(output_queue, total_stocks, stock_scores, stock_rationales, stock_item_scores):
    """处理结果队列的线程函数"""
    processed_count = 0
    while processed_count < total_stocks:
        try:
            stock_index, stock_code, stock_name, score, rationale, item_scores = output_queue.get(timeout=1)
            
            if score is not None:
                stock_scores[stock_code] = score
                # 只有当有完整的结果时才保存分析结果
                if rationale is not None:
                    stock_rationales[stock_code] = rationale
                if item_scores is not None:
                    stock_item_scores[stock_code] = item_scores
                print(f"第{stock_index+1}只股票评分：{stock_code}({stock_name}) = {score}")
            
            processed_count += 1
            output_queue.task_done()
        except Exception:
            # 队列可能暂时为空，继续等待
            continue

def export_to_excel(stocks, code2name, stock_scores, stock_item_scores):
    """将评分结果导出到Excel文件"""
    # 创建结果数据
    data = []
    
    # 按照评分排序股票
    sorted_stocks = sorted(stock_scores.items(), key=lambda x: x[1], reverse=True)
          
    # 定义项目列表，必须与parse_item_scores函数中完全一致
    items = ["多周期共振判断", "短线趋势判断", "资金面判断", "消息面判断", "行业趋势及共振判断", "基本面判断", "盈亏比判断"]
    
    # 使用固定的诊断日志名
    excel_debug_file = "excel_export_debug.txt"
    with open(excel_debug_file, "w", encoding="utf-8") as debug_file:
        debug_file.write("Excel导出调试信息\n")
        debug_file.write("=" * 50 + "\n\n")
    
    for rank, (code, score) in enumerate(sorted_stocks, 1):
        name = code2name.get(code, "未知")
        
        # 获取分项评分和理由，如果没有则使用默认值
        item_scores = stock_item_scores.get(code, {})
        
        # 记录调试信息
        with open(excel_debug_file, "a", encoding="utf-8") as debug_file:
            debug_file.write(f"股票: {code}({name}), 总分: {score}\n")
            if code in stock_item_scores:
                debug_file.write(f"包含的项目键: {', '.join(stock_item_scores[code].keys())}\n")
                # 检查每个项目是否存在
                for item in items:
                    if item in stock_item_scores[code]:
                        debug_file.write(f"  √ 找到项目: {item}, 分数={stock_item_scores[code][item]['score']}\n")
                    else:
                        debug_file.write(f"  × 未找到项目: {item}\n")
            else:
                debug_file.write(f"股票代码不在item_scores中\n")
            debug_file.write("-" * 50 + "\n")
        
        row = {
            "排名": rank,
            "代码": code,
            "名称": name,
            "总分": score
        }
        
        # 添加分项评分和理由
        for item in items:
            if code in stock_item_scores and item in stock_item_scores[code]:
                row[f"{item}分数"] = stock_item_scores[code][item]["score"]
                row[f"{item}理由"] = stock_item_scores[code][item]["reason"]
            else:
                row[f"{item}分数"] = 0
                row[f"{item}理由"] = ""
        
        data.append(row)
    
    # 记录最终结果
    with open(excel_debug_file, "a", encoding="utf-8") as debug_file:
        debug_file.write("\n生成的数据行数: " + str(len(data)) + "\n")
        if data:
            debug_file.write("数据样例 (第一条):\n")
            for k, v in data[0].items():
                if not k.endswith("理由"):  # 理由可能太长，不打印
                    debug_file.write(f"  {k}: {v}\n")
        debug_file.write("=" * 50 + "\n")
    
    # 创建DataFrame
    df = pd.DataFrame(data)
    
    # 保存为Excel文件
    excel_file = "股票评分结果.xlsx"
    df.to_excel(excel_file, index=False, engine="openpyxl")
    print(f"\n分析结果已导出到Excel文件: {excel_file}")
    print(f"Excel导出调试信息已保存到: {excel_debug_file}")
    return excel_file

def check_and_reconnect(client, max_attempts=3):
    """检查大模型连接状态并尝试重新连接"""
    for attempt in range(max_attempts):
        try:
            # 发送一个简单的测试请求
            test_result = client.send("测试连接")
            if test_result:
                return True
        except Exception as e:
            print(f"连接测试失败 (尝试 {attempt+1}/{max_attempts}): {e}")
        
        print(f"尝试重新连接大模型服务... (尝试 {attempt+1}/{max_attempts})")
        client.close()
        time.sleep(2)  # 等待一段时间再重连
        
        if client.connect():
            print("成功重新连接到大模型服务")
            return True
    
    print("无法连接到大模型服务，请检查网络和服务状态")
    return False

def main():
    print("=== 股票评分分析程序 ===")
    
    # 让用户输入股票数据文件路径
    file_path = input("请输入股票数据文件路径（如 D:/stock/tdxdata/个股决选20250416.txt ）: ").strip()
    if not file_path:
        print("未输入文件路径，程序退出")
        return
    
    # 使用固定的日志文件名
    model_log_file = "local_score_log.txt"
    
    try:
        header, stocks, name_col = read_stock_data(file_path)
        if not stocks:
            print("没有读取到有效的股票数据")
            return
        print(f"成功读取 {len(stocks)} 只股票信息")
        
        # 当股票数量超过1000时，不记录日志
        if len(stocks) > 1000:
            print(f"股票数量({len(stocks)})超过1000，不记录日志")
            model_log_file = None
        else:
            # 创建模型日志文件
            with open(model_log_file, "w", encoding="utf-8") as logfile:
                logfile.write(f"股票评分分析完整日志\n")
                logfile.write("本日志包含：\n")
                logfile.write("1. 每只股票的完整原始行情数据（当前股票数据、资金面历史数据、技术指标数据等）\n")
                logfile.write("2. 大模型/本地计算的输入数据（Prompt）\n")
                logfile.write("3. 分析结果和评分过程\n")
                logfile.write("4. 错误信息和重试记录\n")
                logfile.write("="*50 + "\n\n")
            
            print(f"已创建完整分析日志文件: {model_log_file}")
            print("日志将记录每只股票的所有原始行情数据和评分过程")
    except Exception as e:
        print(f"读取股票数据失败: {e}")
        return
    
    # 让用户选择大模型或本地计算
    print("请选择要使用的评分方法:")
    print("1. DeepSeek (本地)")
    print("2. 豆包思考大模型 (在线API)")
    print("3. 豆包标准大模型 (在线API)")
    print("4. 本地程序计算")
    model_choice = input("请输入选择 (1、2、3 或 4，默认为1): ").strip()
    
    # 默认使用DeepSeek
    if not model_choice or model_choice == "1":
        model_type = "deepseek"
        print("正在连接DeepSeek大模型服务...")
        client = DeepSeekClient()  # 使用非流式API
        if not client.connect():
            print("连接DeepSeek大模型服务失败")
            return
        print("连接DeepSeek大模型服务成功")
    elif model_choice == "2":
        model_type = "doubao-thinking"
        print("正在连接豆包思考大模型服务...")
        # 使用与Wstock_compare.py完全相同的API密钥和模型名称
        client = VolcesClient(api_key="a8eefb00-e55d-4ac5-9fb9-04669e5c6385", model="doubao-seed-1-6-thinking-250615")
        if not client.connect():
            print("连接豆包思考大模型服务失败")
            return
        print("连接豆包思考大模型服务成功")
    elif model_choice == "3":
        model_type = "doubao-seed"
        print("正在连接豆包标准大模型服务...")
        # 使用Wstock_score copy.py中的API密钥和模型名称
        client = VolcesClient(api_key="e1283034-c29c-41bc-a10f-14e32fbe828b", model="doubao-seed-1-6-250615")
        if not client.connect():
            print("连接豆包标准大模型服务失败")
            return
        print("连接豆包标准大模型服务成功")
    else:
        model_type = "local-calculation"
        print("使用本地程序计算方法")
        client = None  # 本地计算不需要客户端
    
    # 记录模型选择到日志
    if model_log_file:
        with open(model_log_file, "a", encoding="utf-8") as logfile:
            logfile.write(f"使用的评分方法: {model_type}\n")
            logfile.write("="*50 + "\n\n")
    
    # 新增：让用户输入行业共振数据文件路径（可选）
    resonance_file_path = input("请输入行业共振数据路径（可选，如 D:/stock/tdxdata/ggjs.blk ）: ").strip()
    resonance_data = {}
    if resonance_file_path:
        try:
            print(f"正在读取行业共振数据文件: {resonance_file_path}")
            # 读取行业共振数据文件
            resonance_header, resonance_stocks, resonance_name_col = read_stock_data(resonance_file_path)
            
            # 将共振股票按照一二级行业分组
            for stock in resonance_stocks:
                if '一二级行业' in stock and stock['一二级行业'] and '代码' in stock:
                    industry = stock['一二级行业']
                    code = stock['代码']
                    if industry not in resonance_data:
                        resonance_data[industry] = []
                    resonance_data[industry].append(code)
            
            print(f"成功读取行业共振数据文件，共 {len(resonance_stocks)} 只股票，{len(resonance_data)} 个行业")
        except Exception as e:
            print(f"读取行业共振数据文件失败: {e}")
            print("将继续执行但不使用行业共振数据")
            resonance_data = {}
    else:
        print("未提供行业共振数据文件，将继续执行但不使用行业共振数据")
    
    # 在开始时先获取所有股票的技术面信息
    print("开始获取全部股票的技术面信息...")
    stock_tech_info = {}
    
    # 新增: 存储行业技术指标信息
    industry_tech_info = {}
    # 新增: 存储股票到行业的映射
    stock_to_industry = {}
    # 新增: 存储股票的行业共振信息
    stock_resonance_info = {}
    
    for stock in stocks:
        stock_code = stock.get('代码', '')
        if stock_code:
            # 添加重试机制
            max_tech_retries = 3
            tech_retry_count = 0
            tech_success = False
            
            while tech_retry_count < max_tech_retries and not tech_success:
                try:
                    if tech_retry_count > 0:
                        print(f"重试获取股票 {stock_code} 的技术指标 (第{tech_retry_count}次)")
                    
                    # 首先下载所有所需数据，以确保30分钟MACD的计算有足够的数据支持
                    print(f"正在下载股票 {stock_code} 的数据...")
                    stock_tech.download_all_data_for_stock(stock_code)
                    
                    # 然后调用analyze_stock获取技术指标
                    tech_info = stock_tech.analyze_stock(stock_code)
                    stock_tech_info[stock_code] = tech_info
                    print(f"成功获取股票 {stock_code} 的技术指标")
                    tech_success = True
                    
                    # 新增: 提取一二级行业信息
                    if '一二级行业' in stock and stock['一二级行业']:
                        industry_full_name = stock['一二级行业']
                        # 记录股票所属行业（完整的一二级行业名称）
                        stock_to_industry[stock_code] = industry_full_name
                        
                        # 计算行业共振信息
                        if industry_full_name in resonance_data:
                            # 获取该行业中所有共振股票
                            industry_resonance_stocks = resonance_data[industry_full_name]
                            # 统计行业共振股票数量（排除当前股票自身）
                            resonance_count = sum(1 for code in industry_resonance_stocks if code != stock_code)
                            # 存储行业共振信息
                            stock_resonance_info[stock_code] = {
                                'industry': industry_full_name,
                                'count': resonance_count
                            }
                            print(f"股票 {stock_code} 在 {industry_full_name} 行业中有 {resonance_count} 只共振股票")
                        
                        # 提取连接线后面部分作为主要行业（仅用于获取行业技术指标）
                        industry_name = industry_full_name.split('-')[-1] if '-' in industry_full_name else industry_full_name
                        
                        # 如果该行业的技术指标尚未获取，则获取
                        if industry_name and industry_name not in industry_tech_info:
                            print(f"正在获取行业 '{industry_name}' 的技术指标...")
                            try:
                                # 使用新的行业分析函数
                                industry_result = analyze_industry_properly(industry_name)
                                
                                # 将结果保存到行业技术指标信息字典中
                                industry_tech_info[industry_name] = industry_result
                                
                                # 根据结果判断是否成功
                                if isinstance(industry_result, str) and "error" in industry_result:
                                    print(f"注意: 行业 '{industry_name}' 的技术指标获取失败: {industry_result}")
                                else:
                                    print(f"成功获取行业 '{industry_name}' 的技术指标")
                            except Exception as ie:
                                print(f"获取行业 '{industry_name}' 技术指标失败: {ie}")
                                industry_tech_info[industry_name] = f"获取行业技术指标失败: {str(ie)}"
                    
                except Exception as e:
                    tech_retry_count += 1
                    if tech_retry_count < max_tech_retries:
                        print(f"获取股票 {stock_code} 技术指标失败: {e}，将在2秒后重试...")
                        time.sleep(2)  # 失败后等待时间
                    else:
                        # 最后一次重试失败，设为空字符串
                        stock_tech_info[stock_code] = ""
                        print(f"获取股票 {stock_code} 技术指标失败: {e}，已达到最大重试次数")
            
    print(f"完成获取 {len(stock_tech_info)} 只股票的技术面信息")
    print(f"完成获取 {len(industry_tech_info)} 个行业的技术面信息")
    print(f"完成获取 {len(stock_resonance_info)} 只股票的行业共振信息")
    
    # 构建股票代码到名称的映射
    code2name = {stock['代码']: stock['名称'] for stock in stocks}
    
    # 多模型分析循环
    continue_analysis = True
    while continue_analysis:
        # 初始化每只股票的评分
        stock_scores = {stock['代码']: 0 for stock in stocks}
        
        # 保存分析结果
        stock_rationales = {stock['代码']: "" for stock in stocks}
        
        # 保存分项评分和理由
        stock_item_scores = {stock['代码']: {} for stock in stocks}
        
        # 创建输出文件（使用固定名称，初始为空）
        output_file = f"stock_scores_{model_type}.txt"
        with open(output_file, "w", encoding="utf-8") as fout:
            pass  # 创建空文件
        
        # 创建线程安全的输出队列和文件锁
        output_queue = Queue()
        output_file_lock = threading.Lock()
        
        # 启动结果处理线程
        result_thread = threading.Thread(
            target=process_results_thread, 
            args=(output_queue, len(stocks), stock_scores, stock_rationales, stock_item_scores)
        )
        result_thread.daemon = True
        result_thread.start()
        
        # 获取CPU核心数，决定线程数量
        cpu_count = os.cpu_count()
        print(f"检测到CPU核心数: {cpu_count}")
        max_workers = min(cpu_count or 16, 32)  # 将最大线程数扩展为32，提高并发请求数
        print(f"使用 {max_workers} 个并行线程处理分析任务")
        
        # 使用线程池并行处理股票分析
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            futures = []
            for i, stock in enumerate(stocks):
                if model_type == "local-calculation":
                    # 使用本地计算函数
                    future = executor.submit(
                        analyze_stock_locally, i, stock, stock_tech_info,
                        output_queue, output_file_lock, output_file, header,
                        model_log_file,
                        industry_tech_info=industry_tech_info, stock_to_industry=stock_to_industry, stock_resonance_info=stock_resonance_info
                    )
                else:
                    # 使用大模型分析函数
                    future = executor.submit(
                        analyze_stock, i, stock, stock_tech_info, 
                        client, output_queue, output_file_lock, output_file, header,
                        model_log_file, max_retries=3, retry_delay=5,  # 配置重试参数
                        industry_tech_info=industry_tech_info, stock_to_industry=stock_to_industry, stock_resonance_info=stock_resonance_info  # 传递行业信息
                    )
                futures.append(future)
            
            # 等待所有任务完成并显示进度
            total = len(futures)
            completed = 0
            failed = 0
            
            for future in concurrent.futures.as_completed(futures):
                completed += 1
                try:
                    result = future.result()
                    if result[0] is None:  # 如果分析失败
                        failed += 1
                        
                        # 每5个失败的请求，检查并尝试重新连接 (仅适用于大模型)
                        if failed > 0 and failed % 5 == 0 and model_type != "local-calculation":
                            print(f"检测到连续失败的请求，尝试检查连接...")
                            if not check_and_reconnect(client):
                                print("无法恢复连接，程序将继续尝试处理剩余任务")
                except Exception as e:
                    print(f"处理任务结果时出错: {e}")
                    failed += 1
                
                # 显示进度
                if completed % 5 == 0 or completed == total:
                    success_rate = ((completed - failed) / completed * 100) if completed > 0 else 0
                    print(f"进度: {completed}/{total} ({(completed/total*100):.1f}%)，成功率: {success_rate:.1f}%")
        
        # 等待结果处理线程完成
        output_queue.join()
        
        # 按评分排序并输出结果
        print("\n=== 股票评分排名 ===")
        sorted_stocks = sorted(stock_scores.items(), key=lambda x: x[1], reverse=True)
        valid_results = sum(1 for _, score in sorted_stocks if score != 0)
        print(f"有效分析结果: {valid_results}/{len(stocks)} ({valid_results/len(stocks)*100:.1f}%)")
        
        for rank, (code, score) in enumerate(sorted_stocks, 1):
            name = code2name.get(code, "未知")
            print(f"第{rank}名: {code}({name})，评分: {score}")
        
        # 导出到Excel文件
        excel_file = export_to_excel(stocks, code2name, stock_scores, stock_item_scores)
        
        # 记录程序完成到日志
        if model_log_file:
            with open(model_log_file, "a", encoding="utf-8") as logfile:
                logfile.write(f"\n评分方法 {model_type} 执行完成\n")
                logfile.write(f"总共处理 {len(stocks)} 只股票，成功 {valid_results} 只，失败 {len(stocks) - valid_results} 只\n")
                logfile.write(f"本次分析已完整记录每只股票的：\n")
                logfile.write(f"- 股票基本信息（代码、名称、行业等）\n")
                logfile.write(f"- 当前股票数据（所有原始字段）\n")
                logfile.write(f"- 资金面历史数据（主力净额、主买净额、内外盘等）\n")
                logfile.write(f"- 技术指标数据（原始和增强后的完整技术分析）\n")
                logfile.write(f"- 行业技术指标数据\n")
                logfile.write(f"- 行业共振数据\n")
                logfile.write(f"- 发送给大模型的完整Prompt或本地计算的输入数据\n")
                logfile.write(f"- 详细的评分计算过程和最终结果\n")
                logfile.write("="*50 + "\n")
        
        print(f"\n分析完成，所有评分结果已保存到 {output_file}")
        print(f"注意：{output_file} 现在包含每只股票的完整原始行情数据和打分过程")
        print(f"详细评分信息已导出到Excel文件: {excel_file}")
        print(f"完整分析日志（含所有原始数据）已记录到: {model_log_file}")
        if model_type == "local-calculation":
            print(f"本地计算详细日志已记录到: local_calculation_detailed_log.txt")
        
        # 询问是否使用另一个模型再次分析
        switch_model = input("\n是否要使用另一个评分方法进行分析？(y/n): ").strip().lower()
        if switch_model == 'y' or switch_model == 'yes':
            # 关闭当前客户端连接（如果存在）
            if client:
                client.close()
            
            # 切换评分方法
            if model_type == "deepseek":
                model_type = "doubao-thinking"
                print("正在连接豆包思考大模型服务...")
                client = VolcesClient(api_key="a8eefb00-e55d-4ac5-9fb9-04669e5c6385", model="doubao-seed-1-6-thinking-250615")
                if not client.connect():
                    print("连接豆包思考大模型服务失败，程序退出")
                    return
                print("连接豆包思考大模型服务成功")
            elif model_type == "doubao-thinking":
                model_type = "doubao-seed"
                print("正在连接豆包标准大模型服务...")
                client = VolcesClient(api_key="e1283034-c29c-41bc-a10f-14e32fbe828b", model="doubao-seed-1-6-250615")
                if not client.connect():
                    print("连接豆包标准大模型服务失败，程序退出")
                    return
                print("连接豆包标准大模型服务成功")
            elif model_type == "doubao-seed":
                model_type = "local-calculation"
                print("切换到本地程序计算方法")
                client = None
            else:  # local-calculation
                model_type = "deepseek"
                print("正在连接DeepSeek大模型服务...")
                client = DeepSeekClient()
                if not client.connect():
                    print("连接DeepSeek大模型服务失败，程序退出")
                    return
                print("连接DeepSeek大模型服务成功")
            
            # 记录模型切换到日志
            if model_log_file:
                with open(model_log_file, "a", encoding="utf-8") as logfile:
                    logfile.write(f"\n切换到模型: {model_type}\n")
                    logfile.write("="*50 + "\n\n")
        else:
            continue_analysis = False
    
    # 关闭连接（如果存在）
    if client:
        client.close()
    print(f"完整分析日志（含所有原始行情数据）已保存到: {model_log_file}")
    print("程序已完成所有分析任务")

def test_enhanced_functions():
    """测试函数，验证代码修改后的功能"""
    try:
        # 测试股票代码
        test_stock_code = "000001"
        print(f"开始测试股票 {test_stock_code} 的相关函数...")
        
        # 先下载数据
        print("1. 下载技术分析所需数据...")
        stock_tech.download_all_data_for_stock(test_stock_code)
        
        # 获取技术指标
        print("2. 获取技术指标...")
        tech_info = stock_tech.analyze_stock(test_stock_code)
        if not tech_info:
            print("获取技术指标失败")
            return
        
        # 打印原始技术指标中的K线部分
        print("\n原始技术指标格式验证:")
        # 查找K线部分，支持三种可能的格式
        k_line_format_found = False
        
        # 检查第一种格式
        section_start = tech_info.find("1. 最近20个交易日K线：")
        if section_start != -1:
            print("找到了格式: '1. 最近20个交易日K线：'")
            k_line_format_found = True
        
        # 检查第二种格式
        if not k_line_format_found:
            section_start = tech_info.find("1. 最近10个交易日的K线/成交量行情：")
            if section_start != -1:
                print("找到了格式: '1. 最近10个交易日的K线/成交量行情：'")
                k_line_format_found = True
        
        # 检查第三种格式 (Zstock_tech_qmt.py格式)
        if not k_line_format_found:
            section_start = tech_info.find("1. 最近5个交易日的K线/成交量行情：")
            if section_start != -1:
                print("找到了格式: '1. 最近5个交易日的K线/成交量行情：'")
                k_line_format_found = True
        
        if not k_line_format_found:
            print("没有找到任何支持的K线格式")
            return
            
        section_end = tech_info.find("\n\n2.", section_start)
        if section_end == -1:
            section_end = len(tech_info)
        
        kline_section = tech_info[section_start:section_end]
        lines = kline_section.split('\n')
        
        print("K线部分的前5行:")
        for i in range(min(5, len(lines))):
            print(f"  {lines[i]}")
        
        # 模拟历史数据
        print("\n3. 获取历史数据...")
        # 临时创建一个股票信息字典，包含量比和换手Z
        mock_stock = {
            "代码": test_stock_code,
            "名称": "平安银行",
            "量比": "1.35",
            "换手Z": "0.86%",
            "主力净额": "10000000",
            "主买净额": "8000000",
            "内盘": "50000000",
            "外盘": "40000000"
        }
        
        # 获取历史交易数据
        raw_history_info = ""
        # 尝试直接构造一些原始历史数据，以便测试
        raw_history_info = """【2025-05-15交易数据】
代码: 000001
名称: 平安银行
量比: 1.28
换手Z: 0.79%
主力净额: 8500000
主买净额: 7000000
内盘: 48000000
外盘: 42000000

【2025-05-14交易数据】
代码: 000001
名称: 平安银行
量比: 1.15
换手Z: 0.92%
主力净额: 9200000
主买净额: 7800000
内盘: 45000000
外盘: 38000000
"""
        
        history_info = get_historical_data(test_stock_code, current_stock=mock_stock)
        print(f"获取到资金面信息长度: {len(history_info)}")
        if history_info:
            print("资金面信息示例:")
            print(history_info[:300] + "..." if len(history_info) > 300 else history_info)
            
            # 验证资金面信息格式
            if "资金面情况" in history_info and "内盘" in history_info and "外盘" in history_info and "内外比" in history_info:
                print("? 资金面情况表格格式正确，包含内盘、外盘和内外比信息")
            else:
                print("? 资金面情况表格格式不正确，缺少内盘、外盘或内外比信息")
        
        # 测试增强技术指标函数
        print("\n4. 测试增强技术指标...")
        enhanced_tech_info = enhance_tech_info(tech_info, mock_stock, raw_history_info)
        
        # 打印增强后的技术指标中的K线部分
        k_line_format_found = False
        
        # 检查第一种格式
        section_start = enhanced_tech_info.find("1. 最近20个交易日K线：")
        if section_start != -1:
            print("增强后找到了格式: '1. 最近20个交易日K线：'")
            k_line_format_found = True
        
        # 检查第二种格式
        if not k_line_format_found:
            section_start = enhanced_tech_info.find("1. 最近10个交易日的K线/成交量行情：")
            if section_start != -1:
                print("增强后找到了格式: '1. 最近10个交易日的K线/成交量行情：'")
                k_line_format_found = True
        
        # 检查第三种格式 (Zstock_tech_qmt.py格式)
        if not k_line_format_found:
            section_start = enhanced_tech_info.find("1. 最近5个交易日的K线/成交量行情：")
            if section_start != -1:
                print("增强后找到了格式: '1. 最近5个交易日的K线/成交量行情：'")
                k_line_format_found = True
        
        if not k_line_format_found:
            print("增强后没有找到任何支持的K线格式")
            return
        
        section_end = enhanced_tech_info.find("\n\n2.", section_start)
        if section_end == -1:
            section_end = len(enhanced_tech_info)
        
        kline_section = enhanced_tech_info[section_start:section_end]
        lines = kline_section.split('\n')
        
        print("增强后K线部分的前5行:")
        for i in range(min(5, len(lines))):
            print(f"  {lines[i]}")
        
        # 验证是否成功添加了量比和换手Z
        has_volume_ratio = False
        has_turnover_z = False
        for line in lines:
            if "量比=" in line:
                has_volume_ratio = True
                print(f"  找到量比信息: {line}")
            if "换手Z=" in line:
                has_turnover_z = True
                print(f"  找到换手Z信息: {line}")
        
        if has_volume_ratio and has_turnover_z:
            print("? 成功添加了量比和换手Z信息")
        else:
            print("? 未能成功添加量比和换手Z信息")
        
        # 构建完整的prompt
        print("\n5. 构建完整提示词...")
        prompt = (
            f"【股票代码】\n{test_stock_code}\n【股票名称】\n平安银行\n\n"
        )
        
        # 添加资金面情况
        if history_info:
            prompt += f"【资金面情况】\n{history_info}\n"
        
        # 添加技术指标信息
        prompt += f"【技术指标】\n{enhanced_tech_info}\n\n"
        
        print(f"完整提示词前300个字符:")
        print(prompt[:300] + "...")
        
        # 测试行业板块技术指标获取
        print("\n6. 测试行业板块技术指标获取...")
        test_industry_name = "银行"
        print(f"获取行业 '{test_industry_name}' 的技术指标...")
        
        try:
            # 使用新的行业分析函数代替直接调用
            industry_result = analyze_industry_properly(test_industry_name)
            
            # 验证结果
            if isinstance(industry_result, str) and "error" in industry_result:
                print(f"获取行业技术指标出错: {industry_result}")
                
                # 尝试一些常见行业板块
                alternative_names = ["证券", "保险", "软件服务"]
                for alt_name in alternative_names:
                    print(f"\n尝试其他行业: '{alt_name}'")
                    alt_result = analyze_industry_properly(alt_name)
                    if not (isinstance(alt_result, str) and "error" in alt_result):
                        industry_result = alt_result
                        print(f"成功获取行业 '{alt_name}' 的技术指标")
                        break
                    else:
                        print(f"获取行业 '{alt_name}' 技术指标也失败")
            
            # 检查结果是否包含预期部分
            has_kline = False
            has_macd = False
            has_ma = False
            
            if isinstance(industry_result, str):
                if "最近10个交易日的K线" in industry_result:
                    has_kline = True
                if "日线MACD" in industry_result:
                    has_macd = True
                if "最近10个交易日均线" in industry_result:
                    has_ma = True
                    
                if has_kline and has_macd and has_ma:
                    print("? 行业技术指标格式正确，包含K线、MACD和均线信息")
                else:
                    print("× 行业技术指标格式不完整，缺少部分信息")
                    print(f"  K线信息: {'存在' if has_kline else '缺失'}")
                    print(f"  MACD信息: {'存在' if has_macd else '缺失'}")
                    print(f"  均线信息: {'存在' if has_ma else '缺失'}")
                
                # 检查标题是否正确修改
                if "所属行业" in industry_result:
                    print("? 行业名称标题正确修改为 '所属行业 xxx 的技术指标分析'")
                else:
                    print("× 行业名称标题未修改")
                
                # 检查是否处理了模拟历史数据说明
                if "使用模拟历史数据" in industry_result:
                    if "注: '使用模拟历史数据'" in industry_result:
                        print("? 添加了模拟历史数据的说明")
                    else:
                        print("× 缺少模拟历史数据的说明")
            else:
                print("× 行业技术指标返回值不是字符串，无法检查内容")
                
        except Exception as e:
            print(f"测试行业板块技术指标获取时出错: {e}")
            print(traceback.format_exc())
        
        print("\n测试完成！")
        return True
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        print(traceback.format_exc())
        return False

def analyze_industry_properly(industry_name):
    """
    正确地分析行业板块，模拟Zstock_industry_tdx.py的main函数流程
    
    Args:
        industry_name: 行业名称，如'软件服务'
        
    Returns:
        str: 行业技术指标分析结果
    """
    # 导入板块映射相关工具（仍使用 Zstock_industry_tdx 提供的映射与工具函数）
    from Zstock_industry_tdx import (
        BLOCK_NAME_CODE_MAP,
        build_block_code_map,
        find_latest_block_file
    )
    # 统一改为通过 Zstock_tech_qmttdx 获取行情与技术分析
    import Zstock_tech_qmttdx_bak as qmttdx
    
    # 记录原始行业名称，用于显示
    original_industry_name = industry_name
    
    # 先使用预定义的板块映射
    block_code_map = BLOCK_NAME_CODE_MAP
    
    # 尝试查找最新的板块指数文件，特别是今日的文件
    latest_block_file = find_latest_block_file()
    current_date = datetime.now().strftime('%Y%m%d')
    today_block_file = None
    
    # 检查是否有当日的板块指数文件
    tdxdata_dir = "tdxdata"  # 默认目录
    today_file_name = f"板块指数{current_date}.xls"
    candidate_paths = [
        os.path.join("D:\\stock", tdxdata_dir, today_file_name),
        os.path.join(tdxdata_dir, today_file_name),
        os.path.join(".", tdxdata_dir, today_file_name)
    ]
    
    for path in candidate_paths:
        if os.path.exists(path):
            today_block_file = path
            print(f"找到当日板块指数文件: {path}")
            break
    
    # 优先使用当日文件，其次使用最新文件
    block_file_to_use = today_block_file or latest_block_file
    
    if block_file_to_use:
        # 如果找到文件，使用文件中的映射更新预定义映射
        print(f"使用板块指数文件: {block_file_to_use}")
        file_block_map = build_block_code_map(block_file_to_use)
        if file_block_map:
            # 合并两个映射，文件中的映射优先
            block_code_map.update(file_block_map)
    
    # 初始化input_code为None
    input_code = None
    input_code_type = None  # 用于记录代码类型
    matched_block_name = None  # 用于记录匹配到的板块名称
    
    # 检查行业名称是否直接在映射中
    if industry_name in block_code_map:
        input_code = block_code_map[industry_name]
        input_code_type = "板块"
        matched_block_name = industry_name
        print(f"板块名称 '{industry_name}' 对应的代码是: {input_code}")
    else:
        # 尝试模糊匹配板块名称
        matched_blocks = []
        for block_name, block_code in block_code_map.items():
            if industry_name in block_name or block_name in industry_name:
                matched_blocks.append((block_name, block_code))
        
        if matched_blocks:
            # 只使用第一个匹配结果
            matched_block_name = matched_blocks[0][0]
            input_code = matched_blocks[0][1]
            input_code_type = "板块"
            print(f"找到匹配的板块: {matched_block_name}，代码: {input_code}")
        
        # 如果仍然没有找到对应代码，尝试直接作为股票代码
        if not input_code and len(industry_name) == 6 and industry_name.isdigit():
            input_code = industry_name
            input_code_type = "股票"
            print(f"使用输入的代码: {input_code}")
    
    if not input_code:
        # 尝试直接生成板块代码
        # 常见的行业板块代码格式
        if len(industry_name) <= 4:  # 短名称可能是行业分类
            # 尝试常见的板块代码前缀
            for prefix in ['880', '881', '399']:
                # 创建一个候选代码
                candidate_code = f"{prefix}{industry_name[:3].zfill(3)}"
                print(f"尝试生成板块代码: {candidate_code}")
                
                # 这里应该有更复杂的验证逻辑，但简化为直接返回
                input_code = candidate_code
                input_code_type = "板块"
                matched_block_name = industry_name  # 使用原始行业名称
                break
    
    if not input_code:
        error_msg = f"未能找到行业 '{industry_name}' 对应的板块代码"
        print(error_msg)
        return {"error": error_msg}
    
    # 调用 Zstock_tech_qmttdx.analyze_stock 进行分析
    try:
        # 根据代码类型决定数据源：板块使用 'tdx'，个股使用 'miniqmt'
        data_source = 'tdx' if input_code_type == '板块' else 'miniqmt'

        # 对于个股也可以直接调用，同一接口保持兼容
        result = qmttdx.analyze_stock(input_code, data_source=data_source)
        
        # 如果result是字符串且包含标题行，修改标题
        if isinstance(result, str):
            # 统一将标题中的 "{code} 的技术指标分析" 替换为 "所属行业 xxx 的技术指标分析"
            display_name = matched_block_name or original_industry_name
            old_header = f"{input_code} 的技术指标分析："
            # 也兼容旧格式 "板块代码 {code} 的技术指标分析："
            old_header_alt = f"板块代码 {input_code} 的技术指标分析："
            new_header = f"所属行业 {display_name} 的技术指标分析："
            result = result.replace(old_header_alt, new_header)
            result = result.replace(old_header, new_header)
            
            # 如果包含"使用模拟历史数据"的注释，添加解释
            if "使用模拟历史数据" in result:
                explanation = "\n注: '使用模拟历史数据' 表示系统无法找到真实历史交易数据，使用了基于当前数据的算法模拟生成的历史数据。"
                # 在结果末尾添加解释
                result += explanation
                
        return result
    except Exception as e:
        error_msg = f"分析行业 '{industry_name}' (代码: {input_code}) 时出错: {str(e)}"
        print(error_msg)
        return {"error": error_msg}

def _extract_industry_signals(industry_info):
    """专门针对行业技术分析的信号提取，确保不同行业能被正确区分"""
    if not industry_info:
        return {}, ""
    
    # 提取行业名称 - 增强识别逻辑
    industry_name = "未知行业"
    import re
    
    # 增强的行业名称提取逻辑，适配Zstock_tech_qmttdx.py输出格式
    def extract_industry_name_from_content(data):
        patterns = [
            # 格式1: "所属行业 电池 的技术指标分析"（修改后的QMT格式）
            r'所属行业\s+([^\s的]+)\s*的技术指标',
            # 格式2: "电池 的技术指标分析："（原始QMT格式）
            r'^([^\s]+)\s+的技术指标分析：',
            # 格式3: "【所属行业技术指标】\n所属行业 电池 的技术指标分析"
            r'【所属行业技术指标】[^\n]*\n[^\n]*所属行业\s+([^\s的]+)',
            # 格式4: "行业板块 '电池' (代码: 881128) 的技术指标分析："（旧格式兼容）
            r"行业板块\s*'([^']+)'\s*\(代码:\s*\d+\)",
            # 格式5: "行业板块 '电池' 的技术指标分析："（旧格式兼容）
            r"行业板块\s*'([^']+)'\s*的技术指标",
            # 格式6: "行业板块 电池 (代码: 881128)"（无引号，旧格式兼容）
            r"行业板块\s+([^\s\(]+)\s*\(代码:",
            # 格式7: 简化匹配，只要包含板块和单引号（旧格式兼容）
            r"板块\s*'([^']+)'",
            # 格式8: 任何以单引号包围的内容，在"代码"之前（旧格式兼容）
            r"'([^']+)'\s*\(代码:",
        ]
        
        for pattern in patterns:
            match = re.search(pattern, data, re.MULTILINE)
            if match:
                name = match.group(1).strip()
                # 跳过数字代码（如881128）
                if name.isdigit():
                    continue
                if name and name != "":
                    return name
        return "未知行业"
    
    # 提取行业名称
    industry_name = extract_industry_name_from_content(industry_info)
    
    # 提取短线技术指标分析结果部分
    signal_section = ""
    start_marker = "短线技术指标分析结果："
    start_idx = industry_info.find(start_marker)
    if start_idx != -1:
        # 从"短线技术指标分析结果："开始提取到文末
        signal_section = industry_info[start_idx:]
    else:
        # 如果没找到，查找"短线技术指标："
        start_marker = "短线技术指标："
        start_idx = industry_info.find(start_marker)
        if start_idx != -1:
            signal_section = industry_info[start_idx:]
        else:
            # 使用整个文档
            signal_section = industry_info
    
    # 提取具体的数值信息来区分不同行业
    unique_markers = []
    
    # 提取MACD相关的具体数值（改进正则表达式）
    macd_values = re.findall(r'DIF[值=:\s]*([-]?[0-9]+\.?[0-9]*)', signal_section)
    if macd_values:
        unique_markers.append(f"DIF值:{','.join(macd_values[:3])}")  # 只取前3个避免过长
    
    # 提取均线具体数值
    ma_values = re.findall(r'MA5[=:\s]*([0-9]+\.?[0-9]*)', signal_section)
    if ma_values:
        unique_markers.append(f"MA5值:{','.join(ma_values[:3])}")
    
    # 提取RSI具体数值
    rsi_values = re.findall(r'RSI6[=:\s]*([0-9]+\.?[0-9]*)', signal_section)
    if rsi_values:
        unique_markers.append(f"RSI6值:{','.join(rsi_values[:3])}")
    
    # 提取成交量数值
    vol_values = re.findall(r'VOL[=:\s]*([0-9]+)', signal_section)
    if vol_values:
        unique_markers.append(f"成交量:{','.join(vol_values[:2])}")
    
    # 构建唯一标识
    unique_signature = f"行业:{industry_name};" + ";".join(unique_markers)
    
    # ---------- 信号统计函数（避免长短信号重复计数） ----------
    def _count_signals(sig_list, text):
        """按照长度从长到短匹配，同一次出现只记最长信号，避免子串重复。"""
        # 按长度从长到短排序，确保长信号优先匹配
        sorted_signals = sorted(set(sig_list), key=len, reverse=True)  # 使用set去重
        
        # 记录已匹配的信号，避免重复
        matched_signals = []
        temp = text  # 临时文本，命中后将该片段替换，防止后续短词再命中
        
        for s in sorted_signals:
            # 查找所有出现的位置
            import re
            matches = list(re.finditer(re.escape(s), temp))
            for match in matches:
                start, end = match.span()
                # 检查这个位置是否已经被更长的信号占用
                overlapped = False
                for matched_start, matched_end, matched_signal in matched_signals:
                    if not (end <= matched_start or start >= matched_end):  # 有重叠
                        overlapped = True
                        break
                
                if not overlapped:
                    matched_signals.append((start, end, s))
        
        return len(matched_signals)
    # ----------------------------------------------------------
    
    signal_counts = {}
    
    # 趋势开始信号
    start_signals = [
        "DIF拐头向上", "RSI6上穿50", "MA5拐头向上", "MACD金叉", "MA5上穿MA10",
    ]
    signal_counts['start'] = _count_signals(start_signals, signal_section)
    
    # 趋势持续信号
    continue_signals = [
        "DIF持续上涨", "MACD红柱持续变长", "股价持续运行在MA5之上",
        "RSI6始终运行在50以上", "DIF持续在0轴上方"
    ]
    signal_counts['continue'] = _count_signals(continue_signals, signal_section)
    
    # 背离信号（优先匹配，因为更具体）
    divergence_signals = [
        "MACD强势无顶背离", "量价齐升无背离", "MACD底背离",
        "MACD弱势无顶背离"
    ]
    signal_counts['divergence'] = _count_signals(divergence_signals, signal_section)
    
    # 强势信号（去除已在背离信号中的项目）
    strong_signals = [
        "4根及以上K线的MAVOL5上涨", "4根及以上K线的VOL大于MAVOL5",
        "放量超过MAVOL5两倍以上",
        "量价齐升无背离"  # 保留通用的量价齐升，但排除背离相关的
    ]
    signal_counts['strong'] = _count_signals(strong_signals, signal_section)
    
    # 弱势信号
    weak_signals = [
        "DIF拐头向下", "MACD红柱开始缩短", "MACD死叉", "MA5下穿MA10",
        "RSI6下穿50", "DIF值持续下降", "3根及以上K线的MAVOL5下跌",
        "4根及以上K线的VOL小于MAVOL5", "量价顶背离", "MACD顶背离"
    ]
    signal_counts['weak'] = _count_signals(weak_signals, signal_section)
    
    # 添加调试信息
    print(f"[调试] 行业识别: {industry_name}")
    print(f"[调试] 信号统计详情: {signal_counts}")
    print(f"[调试] 找到的信号总数: {sum(signal_counts.values())}")
    
    return signal_counts, unique_signature

# 新增：行业趋势及共振判断综合评分函数
def calculate_industry_trend_resonance_score_detailed(industry_info, resonance_count):
    """
    计算行业趋势及共振判断的综合得分
    
    Args:
        industry_info: 行业技术指标信息
        resonance_count: 行业共振股票数量
        
    Returns:
        tuple: (得分, 理由文本, 详细信息字典)
    """
    # 如果行业共振小于2，直接判为0分
    if resonance_count < 2:
        reason = f"行业共振股票数量({resonance_count})小于2，直接判为0分"
        details = {
            'resonance_count': resonance_count,
            'industry_score': 0,
            'final_score': 0,
            'reason': 'resonance_too_low'
        }
        return 0, reason, details
    
    # 使用原行业趋势判断的打分逻辑
    industry_score, industry_reason, industry_details = calculate_industry_trend_score_detailed(industry_info)
    
    # 构建综合理由
    reason = f"行业趋势得分:{industry_score}分({industry_reason})，行业共振:{resonance_count}支股票(≥2支，满足条件)"
    
    # 构建详细信息字典
    details = {
        'resonance_count': resonance_count,
        'industry_score': industry_score,
        'final_score': industry_score,
        'industry_details': industry_details if isinstance(industry_details, dict) else {},
        'reason': 'combined_score'
    }
    
    # 如果有行业技术指标的详细信息，合并到details中
    if isinstance(industry_details, dict):
        for key, value in industry_details.items():
            if key not in details:  # 避免覆盖已有字段
                details[key] = value
    
    return industry_score, reason, details

def calculate_industry_trend_resonance_score(industry_info, resonance_count):
    """
    计算行业趋势及共振判断得分的简化版本
    
    Args:
        industry_info: 行业技术指标信息
        resonance_count: 行业共振股票数量
        
    Returns:
        int: 得分
    """
    score, _, _ = calculate_industry_trend_resonance_score_detailed(industry_info, resonance_count)
    return score

def calculate_multi_period_resonance_score_detailed(tech_info):
    """计算多周期共振判断评分（详细版本）
    
    规则：当超短趋势判断和中线趋势判断至少有一项为1分时，本项为1分，否则为0分
    
    Args:
        tech_info: 技术指标信息
    
    Returns:
        tuple: (评分, 理由, 详细信息字典)
    """
    try:
        # 计算超短趋势判断（30分钟级）
        ultra_short_score, ultra_short_reason, ultra_short_signals = calculate_trend_score_detailed(
            tech_info, "超短线", 2, 1
        )
        
        # 计算中线趋势判断（周线级）  
        medium_score, medium_reason, medium_signals = calculate_trend_score_detailed(
            tech_info, "中线", 15, 8
        )
        
        # 应用共振规则：至少有一项为1分时，本项为1分
        final_score = 1 if (ultra_short_score == 1 or medium_score == 1) else 0
        
        # 生成详细日志
        decision_process = f"检查多周期共振情况：\n"
        decision_process += f"超短线趋势(30分钟级)：{ultra_short_score}分。{ultra_short_reason}\n"
        decision_process += f"中线趋势(周线级)：{medium_score}分。{medium_reason}\n"
        
        if final_score == 1:
            winning_periods = []
            if ultra_short_score == 1:
                winning_periods.append("超短线趋势")
            if medium_score == 1:
                winning_periods.append("中线趋势")
            decision_process += f"共振判断：{' + '.join(winning_periods)}至少有一个为1分，因此多周期共振判断为1分。"
        else:
            decision_process += "共振判断：超短线趋势和中线趋势均为0分，因此多周期共振判断为0分。"
        
        # 构建详细信息字典
        details = {
            'ultra_short': {
                'score': ultra_short_score, 
                'reason': ultra_short_reason, 
                'signals': ultra_short_signals
            },
            'medium': {
                'score': medium_score, 
                'reason': medium_reason, 
                'signals': medium_signals
            },
            'final_score': final_score
        }
        
        return final_score, decision_process, details
        
    except Exception as e:
        error_process = f"计算多周期共振评分时出现错误：{str(e)}"
        return 0, error_process, {}

def calculate_multi_period_resonance_score(tech_info):
    """计算多周期共振判断评分（简化版本，保持兼容性）"""
    score, reason, _ = calculate_multi_period_resonance_score_detailed(tech_info)
    return score, reason

if __name__ == "__main__":
    try:
        # 如果直接运行程序，使用普通模式
        if len(sys.argv) > 1 and sys.argv[1] == "--test":
            # 测试模式
            test_enhanced_functions()
        else:
            # 普通模式
            main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
        print(f"错误详情: {traceback.format_exc()}")
    finally:
        print("程序已退出")



