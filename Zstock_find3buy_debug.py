# -*- coding: gbk -*-
import pandas as pd
import numpy as np
import sys
import os
import locale
from datetime import datetime, timedelta
import time
from typing import List, Tuple, Optional, Dict, Any
import logging
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
import warnings
warnings.filterwarnings('ignore')
import copy

# 添加缠论库路径
try:
    current_dir = os.path.dirname(os.path.abspath(__file__))
except NameError:
    current_dir = os.getcwd()
chan_path = os.path.join(current_dir, 'libs', 'chan')
if chan_path not in sys.path:
    sys.path.insert(0, chan_path)

# 配置日志系统
LOG_DIR = r"d:\stock"
LOG_FILE = os.path.join(LOG_DIR, "3buy_log.txt")

def setup_logging():
    """设置日志系统"""
    # 确保日志目录存在
    if not os.path.exists(LOG_DIR):
        os.makedirs(LOG_DIR, exist_ok=True)
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S',
        handlers=[
            # 文件处理器 - 覆盖模式
            logging.FileHandler(LOG_FILE, mode='w', encoding='gbk'),
            # 控制台处理器
            logging.StreamHandler(sys.stdout)
        ]
    )

    logger = logging.getLogger()
    logger.info("=" * 80)
    logger.info("缠论3买点检测程序启动")
    logger.info(f"日志文件: {LOG_FILE}")
    logger.info("=" * 80)
    return logger

# 导入缠论相关模块
CHAN_AVAILABLE = False
try:
    from Chan import CChan
    from ChanConfig import CChanConfig
    from Common.CEnum import AUTYPE, DATA_FIELD, KL_TYPE, DATA_SRC, BSP_TYPE
    from Common.CTime import CTime
    from KLine.KLine_Unit import CKLine_Unit
    try:
        from DataAPI.CommonStockAPI import CCommonStockApi
    except ImportError:
        # 如果没有CCommonStockApi，创建一个简单的基类
        class CCommonStockApi:
            def __init__(self, code, k_type=None, begin_date=None, end_date=None, autype=None):
                self.code = code
                self.k_type = k_type
                self.begin_date = begin_date
                self.end_date = end_date
                self.autype = autype
    CHAN_AVAILABLE = True
except ImportError:
    CHAN_AVAILABLE = False

# 导入配置排列组合相关模块
from itertools import product

# 导入QMT相关函数
try:
    from Zstock_tech_qmttdx import connect_miniqmt, get_miniqmt_data
    import xtquant.xtdata as xtdata
except ImportError as e:
    print(f"QMT模块导入失败: {e}")
    print("请确保Zstock_tech_qmttdx.py文件存在")
    sys.exit(1)

# 通达信目录路径 - 参考stock_diff.py的定义
TDX_PATH = r"D:\new_tdx"
BLOCKNEW_DIR = os.path.join(TDX_PATH, "T0002", "blocknew")


def add_tdx_market_prefix(code: str) -> str:
    """
    为6位股票代码添加通达信市场前缀
    
    参数:
        code: 6位股票代码字符串
        
    返回:
        带市场前缀的股票代码
        
    规则:
        - 上海交易所(60、68开头)加前缀1
        - 深圳交易所(00、30、02开头)加前缀0
        - 其他格式保持原样
    """
    if isinstance(code, str) and len(code) == 6 and code.isdigit():
        # 上海交易所：主板600xxx，科创板688xxx
        if code.startswith(('60', '68')):
            return '1' + code
        # 深圳交易所：主板000xxx，中小板002xxx，创业板300xxx
        elif code.startswith(('00', '30', '02')):
            return '0' + code
    
    # 如果格式不符，返回原样
    return code


def filter_stock_codes(stock_codes):
    """过滤掉创业板等股票代码

    参考stock_diff.py的实现，支持处理带市场前缀的代码
    只保留6位数字且不以4/8/9开头的股票代码，或带前缀的对应格式
    """
    import re

    exclude_prefixes = {"4", "8", "9"}

    filtered_codes = []
    excluded_codes = []

    for raw_code in stock_codes:
        # 提取连续数字的第一个6~8位连续数字，一般情况下就是股票代码部分
        digits = re.findall(r"(\d{6,8})", raw_code)
        numeric_code = digits[-1] if digits else ""  # 取最后一个匹配

        # 处理带市场前缀的代码（7位）
        if len(numeric_code) == 7:
            # 去掉第一位市场前缀，取后6位进行判断
            actual_code = numeric_code[1:]
        elif len(numeric_code) >= 6:
            # 进一步提取末6位，通常板块文件的都是6位代码为准则
            actual_code = numeric_code[-6:]
        else:
            actual_code = numeric_code

        # 判断是否需要排除（基于实际的6位代码）
        if len(actual_code) == 6 and actual_code[0] in exclude_prefixes:
            excluded_codes.append(raw_code)
            continue

        filtered_codes.append(raw_code)

    if excluded_codes:
        logger.info(f"过滤掉{len(excluded_codes)}只创业板等股票: {', '.join(excluded_codes[:5])}{'...' if len(excluded_codes) > 5 else ''}")

    return filtered_codes


def write_block_file(stock_codes, block_file, append=False):
    """
    将股票代码写入通达信自定义板块文件

    参考stock_diff.py的实现，使用更健壮的文件读写方式

    参数:
        stock_codes: 股票代码列表
        block_file: 板块文件路径
        append: 是否追加模式，True为追加，False为覆盖

    返回:
        bool: 是否成功
    """
    try:
        # 确保目录存在
        dir_path = os.path.dirname(block_file)
        if not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)

        # 读取现有代码（使用更健壮的方式，参考stock_diff.py）
        existing_codes = []
        if os.path.exists(block_file) and append:
            try:
                # 使用二进制模式读取，然后解码（更健壮）
                with open(block_file, 'rb') as f:
                    content = f.read()
                
                # 解码文本
                text = content.decode('gbk', errors='ignore')
                
                # 分割行并去掉空行
                existing_codes = [line.strip() for line in text.replace('\r\n', '\n').split('\n') if line.strip()]
            except Exception as e:
                logger.error(f"读取现有代码失败: {e}")

        # 合并新旧代码
        existing_codes_set = set(existing_codes)
        new_codes = [code for code in stock_codes if code not in existing_codes_set]

        if append:
            # 新代码放在原有代码前面
            all_codes = new_codes + existing_codes
        else:
            # 覆盖模式，只使用新代码
            all_codes = stock_codes

        # 写入文件，使用GBK编码（完全模仿stock_diff.py的方式）
        with open(block_file, 'w', encoding='gbk') as f:
            for code in all_codes:
                f.write(f"{code}\n")

        # 输出详细日志
        file_basename = os.path.basename(block_file)
        if append:
            logger.info(f"写入{file_basename}: 原有{len(existing_codes)}只, 新增{len(new_codes)}只, 共{len(all_codes)}只")
        else:
            logger.info(f"覆盖写入{file_basename}: {len(stock_codes)}只")
        return True

    except Exception as e:
        logger.error(f"写入失败: {block_file} - {str(e)}")
        return False


class QMTDataAPI(CCommonStockApi):
    """
    自定义QMT数据接口，继承自CCommonStockApi
    """

    def __init__(self, code, k_type=KL_TYPE.K_DAY, begin_date=None, end_date=None, autype=AUTYPE.QFQ):
        super(QMTDataAPI, self).__init__(code, k_type, begin_date, end_date, autype)

    def get_kl_data(self):
        """
        从QMT获取K线数据并转换为缠论子项目所需格式
        """
        try:
            # 根据级别确定period参数
            period_map = {
                KL_TYPE.K_DAY: '1d',
                KL_TYPE.K_WEEK: '1w',
                KL_TYPE.K_60M: '60m',
                KL_TYPE.K_30M: '30m',
                KL_TYPE.K_15M: '15m',
                KL_TYPE.K_5M: '5m',
                KL_TYPE.K_1M: '1m'
            }

            period = period_map.get(self.k_type, '1d')

            # 特殊处理30分钟K线：根据QMT机制，需要先获取5分钟K线
            if self.k_type == KL_TYPE.K_30M:
                df_5m = get_miniqmt_data(self.code, period='5m', count=3000)  # 获取足够多的5分钟数据

                if df_5m.empty or 'error' in df_5m.columns:
                    df = get_miniqmt_data(self.code, period=period, count=500)
                else:
                    # 将5分钟数据转换为30分钟数据
                    df = self._convert_5min_to_30min(df_5m)
            else:
                # 其他级别直接获取
                df = get_miniqmt_data(self.code, period=period, count=500)

            if df.empty:
                return

            if 'error' in df.columns:
                return

            # 转换数据格式
            for _, row in df.iterrows():
                try:
                    # 解析时间
                    time_str = str(row['time'])
                    if '-' in time_str:  # 格式: 2024-01-01
                        parts = time_str.split(' ')[0].split('-')
                        year, month, day = int(parts[0]), int(parts[1]), int(parts[2])
                    else:  # 格式: 20240101
                        year = int(time_str[:4])
                        month = int(time_str[4:6])
                        day = int(time_str[6:8])

                    time_obj = CTime(year, month, day, 0, 0)

                    # 获取成交量数据
                    volume_raw = row.get('成交量', 0)
                    amount_raw = row.get('成交额', 0)

                    # 确保成交量数据不为空
                    if volume_raw is None:
                        volume_raw = 0
                    if amount_raw is None:
                        amount_raw = 0

                    # 创建K线数据
                    item_dict = {
                        DATA_FIELD.FIELD_TIME: time_obj,
                        DATA_FIELD.FIELD_OPEN: float(row['开盘']),
                        DATA_FIELD.FIELD_HIGH: float(row['最高']),
                        DATA_FIELD.FIELD_LOW: float(row['最低']),
                        DATA_FIELD.FIELD_CLOSE: float(row['收盘']),
                        DATA_FIELD.FIELD_VOLUME: float(volume_raw),
                        DATA_FIELD.FIELD_TURNOVER: float(amount_raw),
                        DATA_FIELD.FIELD_TURNRATE: float(row.get('换手率', 0))
                    }

                    yield CKLine_Unit(item_dict)

                except Exception:
                    continue

        except Exception:
            return

    def _convert_5min_to_30min(self, df_5m):
        """
        将5分钟K线数据转换为30分钟K线数据
        """
        try:
            if df_5m.empty:
                return pd.DataFrame()

            # 确保时间列是datetime类型
            if 'time' in df_5m.columns:
                df_5m['time'] = pd.to_datetime(df_5m['time'])
            elif 'date' in df_5m.columns:
                df_5m['date'] = pd.to_datetime(df_5m['date'])
                df_5m.rename(columns={'date': 'time'}, inplace=True)

            # 设置时间为索引
            df_5m.set_index('time', inplace=True)

            # 按30分钟重采样
            df_30m = df_5m.resample('30T').agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum',
                'amount': 'sum'
            }).dropna()

            # 重置索引，将时间列恢复
            df_30m.reset_index(inplace=True)

            return df_30m

        except Exception:
            return pd.DataFrame()

    @classmethod
    def do_init(cls):
        """初始化QMT连接"""
        return connect_miniqmt()

    @classmethod
    def do_close(cls):
        """关闭QMT连接"""
        pass


def clean_stock_code(code):
    """清理股票代码，去除非数字字符并确保6位格式"""
    if not code:
        return ""

    # 转为字符串
    code_str = str(code)

    # 去除所有非数字字符（如'='等）
    digits_only = ''.join(c for c in code_str if c.isdigit())

    # 确保6位格式，不足前面补0
    if digits_only:
        return digits_only.zfill(6)

    return ""


def find_name_column(columns):
    """查找股票名称列，支持'名称'和'名称(x)'格式"""
    # 首先检查是否有精确匹配'名称(x)'格式的列
    for col in columns:
        if isinstance(col, str) and col.startswith('名称(') and col.endswith(')'):
            return col

    # 其次检查是否有精确匹配'名称'的列
    for col in columns:
        if isinstance(col, str) and col == '名称':
            return col

    # 最后检查是否有以'名称'开头的列
    for col in columns:
        if isinstance(col, str) and col.startswith('名称'):
            return col

    return None


def read_tdx_xls(file_path: str) -> List[Tuple[str, str]]:
    """
    从TDX xls文件读取股票代码和名称
    返回: (代码, 名称) 元组列表
    """
    try:
        # 尝试不同的引擎和方法
        df = None

        # 方法1: 对于.xls文件尝试xlrd引擎
        if file_path.lower().endswith('.xls'):
            try:
                df = pd.read_excel(file_path, engine='xlrd')
            except Exception:
                pass

        # 方法2: 对于.xlsx文件尝试openpyxl引擎
        if df is None and file_path.lower().endswith('.xlsx'):
            try:
                df = pd.read_excel(file_path, engine='openpyxl')
            except Exception:
                pass

        # 方法3: 尝试默认pandas read_excel
        if df is None:
            try:
                df = pd.read_excel(file_path)
            except Exception:
                pass

        # 方法4: 尝试作为CSV读取（某些.xls文件实际是制表符分隔的）
        if df is None:
            try:
                df = pd.read_csv(file_path, sep='\t', encoding='gbk')
            except Exception:
                pass

        if df is None:
            raise Exception("所有读取方法都失败了")

        # 查找代码和名称列
        code_col = None
        name_col = None

        # 查找代码列
        for col in df.columns:
            col_str = str(col)
            col_lower = col_str.lower()
            if any(keyword in col_str for keyword in ['代码', 'code', '交易代码']) or any(keyword in col_lower for keyword in ['code', '股票代码']):
                code_col = col
                break

        # 查找名称列
        name_col = find_name_column(df.columns)

        if not code_col:
            raise Exception("未找到代码列")

        stock_list = []
        for _, row in df.iterrows():
            # 获取代码和名称
            code = row[code_col] if pd.notna(row[code_col]) else ""
            name = row[name_col] if name_col and pd.notna(row[name_col]) else ""

            # 如果名称是'nan'字符串则跳过
            if str(name).lower() == 'nan':
                continue

            # 清理Excel格式代码，如="000009"
            code_str = str(code)
            if code_str.startswith('="') and code_str.endswith('"'):
                code_str = code_str[2:-1]
            elif code_str.startswith('='):
                code_str = code_str[1:]

            # 移除引号和浮点转换的.0后缀
            code_str = code_str.strip('"').strip("'")
            if code_str.endswith('.0'):
                code_str = code_str[:-2]

            # 使用clean_stock_code函数清理代码
            cleaned_code = clean_stock_code(code_str)

            # 只处理6位数字代码
            if len(cleaned_code) == 6 and cleaned_code.isdigit():
                stock_list.append((cleaned_code, str(name)))

        return stock_list

    except Exception:
        return []


def get_chan_default_config():
    """
    获取ChanConfig.py的实际默认配置
    """
    try:
        # 创建一个空配置对象，获取默认值
        default_chan_config = CChanConfig({})

        # 提取关键默认配置参数
        default_config = {
            "bi_strict": default_chan_config.bi_conf.is_strict,
            "bi_fx_check": str(default_chan_config.bi_conf.bi_fx_check).split('.')[-1].lower(),  # 转换枚举为字符串
            "bi_end_is_peak": default_chan_config.bi_conf.bi_end_is_peak,
            "zs_algo": default_chan_config.zs_conf.zs_algo,
            "divergence_rate": default_chan_config.bs_point_conf.b_conf.divergence_rate,
            "min_zs_cnt": default_chan_config.bs_point_conf.b_conf.min_zs_cnt,
            "max_bs2_rate": default_chan_config.bs_point_conf.b_conf.max_bs2_rate,
        }

        return default_config
    except Exception as e:
        # 如果获取失败，使用硬编码的备用默认配置
        logger = logging.getLogger()
        logger.warning(f"无法获取ChanConfig默认配置，使用备用配置: {e}")
        return {
            "bi_strict": True,
            "bi_fx_check": "strict",
            "bi_end_is_peak": True,
            "zs_algo": "auto",
            "divergence_rate": 0.9,
            "min_zs_cnt": 1,
            "max_bs2_rate": 0.9999,
        }


def get_config_combinations():
    """
    获取缠论配置的排列组合
    基于ChanConfig.py的实际默认配置进行变化
    """
    # 获取ChanConfig.py的实际默认配置
    default_config = get_chan_default_config()

    # 关键配置参数的排列组合
    config_variants = {
        # 笔相关配置
        "bi_strict": [True, False],
        "bi_fx_check": ["strict", "totally", "loss", "half"],
        "bi_end_is_peak": [True, False],

        # 中枢相关配置
        "zs_algo": ["normal", "over_seg", "auto"],

        # 买卖点相关配置
        "divergence_rate": [0.5, 0.7, 0.9],
        "min_zs_cnt": [1, 2],
        "max_bs2_rate": [0.618, 1.0],
    }

    # 生成所有组合
    keys = list(config_variants.keys())
    values = list(config_variants.values())
    combinations = list(product(*values))

    all_configs = []

    # 首先添加默认配置（确保默认配置总是第一个）
    default_config_copy = default_config.copy()
    default_config_copy.update({
        "trigger_step": False,
        "bs_type": '1,2,3a,1p,2s,3b',
        "print_warning": False,
    })

    all_configs.append({
        "name": "默认配置",
        "config": default_config_copy,
        "is_default": True
    })

    # 然后添加所有变化配置
    for combination in combinations:
        config = dict(zip(keys, combination))

        # 检查是否与默认配置完全相同
        is_same_as_default = True
        for key in config_variants.keys():
            if config[key] != default_config[key]:
                is_same_as_default = False
                break

        # 如果与默认配置相同，跳过（因为已经添加了默认配置）
        if is_same_as_default:
            continue

        # 添加固定的基础配置
        config.update({
            "trigger_step": False,
            "bs_type": '1,2,3a,1p,2s,3b',
            "print_warning": False,
        })

        # 创建配置名称（基于默认配置的差异）
        config_name = create_config_name(config, default_config)

        all_configs.append({
            "name": config_name,
            "config": config,
            "is_default": False
        })

    return all_configs


def create_config_name(config, default_config=None):
    """
    为配置创建简洁的名称
    基于与默认配置的差异
    """
    if config is None:
        return "默认配置"

    # 如果没有提供默认配置，获取一个
    if default_config is None:
        default_config = get_chan_default_config()

    key_parts = []

    # 只显示与默认配置的关键差异
    if config.get("bi_strict") != default_config.get("bi_strict"):
        if config.get("bi_strict") == False:
            key_parts.append("非严格笔")
        else:
            key_parts.append("严格笔")

    if config.get("zs_algo") != default_config.get("zs_algo"):
        zs_algo = config.get("zs_algo")
        if zs_algo == "over_seg":
            key_parts.append("跨段中枢")
        elif zs_algo == "normal":
            key_parts.append("普通中枢")
        elif zs_algo == "auto":
            key_parts.append("自动中枢")

    if config.get("divergence_rate") != default_config.get("divergence_rate"):
        key_parts.append(f"分歧率{config.get('divergence_rate')}")

    if config.get("min_zs_cnt") != default_config.get("min_zs_cnt"):
        key_parts.append(f"最少{config.get('min_zs_cnt')}中枢")

    if config.get("bi_fx_check") != default_config.get("bi_fx_check"):
        key_parts.append(f"笔检查{config.get('bi_fx_check')}")

    if config.get("max_bs2_rate") != default_config.get("max_bs2_rate"):
        key_parts.append(f"2买回撤{config.get('max_bs2_rate')}")

    if config.get("bi_end_is_peak") != default_config.get("bi_end_is_peak"):
        if config.get("bi_end_is_peak") == False:
            key_parts.append("笔尾非峰")
        else:
            key_parts.append("笔尾是峰")

    if not key_parts:
        return "默认配置"

    return "+".join(key_parts)


def convert_5min_to_30min(df_5m):
    """
    将5分钟K线数据转换为30分钟K线数据
    """
    try:
        if df_5m.empty:
            return pd.DataFrame()

        # 统一列名映射
        column_mapping = {
            '日期': 'time',
            '时间': 'time',
            'date': 'time',
            '开盘': 'open',
            '最高': 'high',
            '最低': 'low',
            '收盘': 'close',
            '成交量': 'volume',
            '成交额': 'amount'
        }

        df_5m_renamed = df_5m.rename(columns=column_mapping)

        # 如果还没有time列，尝试其他可能的时间列名
        if 'time' not in df_5m_renamed.columns:
            for col in df_5m_renamed.columns:
                if any(keyword in str(col).lower() for keyword in ['time', 'date', '时间', '日期']):
                    df_5m_renamed.rename(columns={col: 'time'}, inplace=True)
                    break

        # 确保时间列是datetime类型
        if 'time' in df_5m_renamed.columns:
            df_5m_renamed['time'] = pd.to_datetime(df_5m_renamed['time'])
        else:
            return pd.DataFrame()

        # 设置时间为索引
        df_5m_copy = df_5m_renamed.copy()
        df_5m_copy.set_index('time', inplace=True)

        # 按30分钟重采样
        df_30m = df_5m_copy.resample('30min').agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum',
            'amount': 'sum'
        }).dropna()

        # 重置索引，将时间列恢复
        df_30m.reset_index(inplace=True)

        # 去除重复时间的行，保留最后一个
        df_30m = df_30m.drop_duplicates(subset=['time'], keep='last')

        # 确保时间是唯一且递增的
        df_30m = df_30m.sort_values('time').reset_index(drop=True)

        # 额外检查：确保没有重复时间戳
        duplicate_times = df_30m[df_30m.duplicated(subset=['time'], keep=False)]
        if not duplicate_times.empty:
            # 按时间分组，每组只保留最后一个
            df_30m = df_30m.groupby('time').last().reset_index()

        return df_30m

    except Exception:
        return pd.DataFrame()


def get_weekly_data_for_3buy(stock_code, count=500):
    """
    获取周K线数据 - 专门用于3买判断
    参考Zstock_tech_qmttdx.py的实现
    """
    try:
        # 格式化股票代码
        if stock_code.lower().startswith(('sh', 'sz')):
            code_only = stock_code[2:]
        else:
            code_only = stock_code

        if code_only.startswith('6') or code_only.startswith(('880', '881')):
            formatted_code = f"{code_only}.SH"
        elif code_only.startswith(('0', '3', '399')):
            formatted_code = f"{code_only}.SZ"
        else:
            print(f"无法识别的股票代码格式: {stock_code}")
            return pd.DataFrame()

        # 直接获取周K线
        kline_data = xtdata.get_market_data(
            field_list=['time', 'open', 'high', 'low', 'close', 'volume', 'amount'],
            stock_list=[formatted_code],
            period='1w',  # 关键：直接指定周线周期
            count=count,
            dividend_type='front',  # 关键：除权指定前复权
            fill_data=True
        )

        # 检查是否成功获取数据
        if kline_data is None or 'time' not in kline_data:
            print(f"无法获取{formatted_code}的周线数据")
            return pd.DataFrame()

        # 构建结果DataFrame
        result_df = pd.DataFrame()
        dates = pd.to_datetime(kline_data.get('time').loc[formatted_code], unit='ms', utc=True).dt.tz_convert('Asia/Shanghai').dt.tz_localize(None)

        result_df['日期'] = dates

        # 添加其他字段
        for field in ['open', 'high', 'low', 'close', 'volume', 'amount']:
            if field in kline_data and isinstance(kline_data[field], pd.DataFrame):
                if formatted_code in kline_data[field].index:
                    result_df[field] = kline_data[field].loc[formatted_code].values
                else:
                    print(f"警告: {field}数据中没有{formatted_code}")
                    result_df[field] = 0
            else:
                print(f"警告: {field}字段不存在或格式不正确")
                result_df[field] = 0

        # 移除时区信息(如果有)
        if hasattr(result_df['日期'].dt, 'tz') and result_df['日期'].dt.tz is not None:
            result_df['日期'] = result_df['日期'].dt.tz_convert(None)

        # 按日期排序 (旧->新)
        result_df = result_df.sort_values('日期', ascending=True).reset_index(drop=True)

        # 统一列名
        result_df.rename(columns={
            'open': '开盘',
            'high': '最高',
            'low': '最低',
            'close': '收盘',
            'volume': '成交量',
            'amount': '成交额'
        }, inplace=True)

        return result_df
    except Exception as e:
        print(f"获取周K线数据出错: {e}")
        return pd.DataFrame()


def get_latest_zs_upper_price_with_configs(stock_code: str, csv_lines: List[str], k_type: KL_TYPE, debug_mode: bool = False, stock_name: str = "", df_raw: pd.DataFrame = None) -> Tuple[Optional[float], Optional[object], str, Optional[dict], Optional[dict], Optional[str], Optional[object]]:
    """
    使用配置排列组合获取最大的中枢上沿价格

    参数:
        stock_code: 股票代码
        csv_lines: CSV格式的K线数据
        k_type: K线级别
        debug_mode: 是否为调试模式，如果是则为每个不同上沿取值生成图表
        stock_name: 股票名称（调试模式需要）
        df_raw: 原始K线数据（调试模式需要）

    返回:
        (最大上沿价格, 最新中枢对象, 信息描述, 最佳配置, 中枢详细信息, 临时CSV文件路径, 最佳缠论对象)
    """
    try:
        # 获取所有配置组合
        all_configs = get_config_combinations()

        # 存储所有有效结果
        valid_results = []

        # 创建临时CSV文件目录
        chan_dir = os.path.join(os.path.dirname(__file__), 'libs', 'chan')
        if not os.path.exists(chan_dir):
            chan_dir = os.path.dirname(__file__)

        # 使用chan库期望的文件命名格式
        period_suffix = {
            KL_TYPE.K_DAY: 'day',
            KL_TYPE.K_WEEK: 'week',
            KL_TYPE.K_30M: '30m'
        }.get(k_type, 'day')

        temp_csv_file = os.path.join(chan_dir, f"{stock_code}_{period_suffix}.csv")

        # 写入临时CSV文件
        with open(temp_csv_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(csv_lines))

        # 测试每个配置
        for _, config_item in enumerate(all_configs):
            try:
                # 创建缠论配置（使用深拷贝避免原配置被修改）
                config_copy = copy.deepcopy(config_item["config"])
                config = CChanConfig(config_copy)

                # 创建缠论对象
                chan = CChan(
                    code=stock_code,
                    begin_time=None,
                    end_time=None,
                    data_src=DATA_SRC.CSV,
                    lv_list=[k_type],
                    config=config,
                    autype=AUTYPE.QFQ
                )

                # 获取K线数据
                kl_data = chan[0]
                if not kl_data or len(kl_data.lst) == 0:
                    continue

                # 获取中枢列表
                zs_list = kl_data.zs_list.zs_lst

                if zs_list:
                    latest_zs = zs_list[-1]

                    # 检查中枢的有效性
                    is_valid_zs = True
                    if hasattr(latest_zs, 'begin') and hasattr(latest_zs, 'end'):
                        if hasattr(latest_zs.begin, 'time') and hasattr(latest_zs.end, 'time'):
                            # 检查时间跨度是否合理
                            if latest_zs.begin.time == latest_zs.end.time:
                                is_valid_zs = False

                    if is_valid_zs:
                        # 计算中枢范围内的成交量峰值
                        volume_info = get_zs_volume_info(kl_data, latest_zs)

                        # 如果成交量信息获取失败，也认为是无效中枢
                        if volume_info is None:
                            is_valid_zs = False

                    if is_valid_zs:
                        # 计算中枢持续周期
                        duration = 0
                        if hasattr(latest_zs, 'begin') and hasattr(latest_zs, 'end') and hasattr(latest_zs.begin, 'idx') and hasattr(latest_zs.end, 'idx'):
                            duration = latest_zs.end.idx - latest_zs.begin.idx + 1
                        elif volume_info:
                            duration = volume_info['end_idx'] - volume_info['start_idx'] + 1

                        result = {
                            'config_name': config_item["name"],
                            'config': config_item["config"],
                            'upper': latest_zs.high,
                            'lower': latest_zs.low,
                            'mid': latest_zs.mid,
                            'zs_obj': latest_zs,
                            'zs_count': len(zs_list),
                            'volume_info': volume_info,
                            'chan_obj': chan,
                            'duration': duration
                        }
                        valid_results.append(result)

            except Exception as e:
                # 单个配置失败不影响整体
                continue

        if not valid_results:
            # 清理临时CSV文件
            try:
                if os.path.exists(temp_csv_file):
                    os.remove(temp_csv_file)
            except:
                pass
            return None, None, "所有配置都未找到中枢", None, None, None, None

        # 在调试模式下，为每个不同的上沿取值生成图表
        if debug_mode and stock_name and df_raw is not None:
            generate_charts_for_all_configs(stock_code, stock_name, df_raw, valid_results, k_type)

        # 新的中枢选择逻辑：
        # 1. 从所有中枢中找出上沿最高的中枢a，以及其它上沿略低于a，但低的幅度不超过15%的中枢
        # 2. 在上述符合条件的中枢中，找到持续周期最长的作为最终符合条件的中枢

        # 第一步：找到上沿最高的中枢
        max_upper = max(valid_results, key=lambda x: x['upper'])['upper']

        # 第二步：筛选出上沿价格在15%范围内的中枢
        upper_threshold = max_upper * 0.85  # 85%作为下限，即不超过15%的降幅
        qualified_results = []

        for result in valid_results:
            if result['upper'] >= upper_threshold:
                qualified_results.append(result)

        # 第三步：在符合条件的中枢中选择持续周期最长的
        if not qualified_results:
            # 如果没有符合条件的，回退到原逻辑
            best_result = max(valid_results, key=lambda x: x['upper'])
        else:
            # 选择持续周期最长的中枢
            best_result = max(qualified_results, key=lambda x: x['duration'])

        # 构建详细信息
        duration_info = f", 持续{best_result.get('duration', 0)}根K线" if 'duration' in best_result else ""
        zs_info = f"最佳配置: {best_result['config_name']}, 上沿={best_result['upper']:.2f}, 下沿={best_result['lower']:.2f}, 中值={best_result['mid']:.2f}{duration_info}"

        # 保存配置名称用于输出
        best_config_name = best_result['config_name']

        # 注意：不要在这里删除临时CSV文件，因为后续分析还需要使用
        # 返回临时文件路径，让调用者负责清理
        # 同时返回最佳配置的缠论对象，确保后续分析使用正确的中枢
        # 返回配置名称而不是配置字典，因为配置字典可能被缠论库修改
        return best_result['upper'], best_result['zs_obj'], zs_info, best_config_name, best_result['volume_info'], temp_csv_file, best_result['chan_obj']

    except Exception as e:
        # 清理临时CSV文件
        try:
            if 'temp_csv_file' in locals() and temp_csv_file and os.path.exists(temp_csv_file):
                os.remove(temp_csv_file)
        except:
            pass
        return None, None, f"配置测试出错: {e}", None, None, None, None


def get_zs_volume_info(kl_data, zs_obj):
    """
    获取中枢范围内的成交量信息

    参数:
        kl_data: K线数据对象
        zs_obj: 中枢对象

    返回:
        包含成交量峰值信息的字典
    """
    try:
        # 检查中枢的有效性
        if hasattr(zs_obj, 'begin') and hasattr(zs_obj, 'end'):
            # 检查中枢时间跨度是否合理
            if hasattr(zs_obj.begin, 'time') and hasattr(zs_obj.end, 'time'):
                start_time = zs_obj.begin.time
                end_time = zs_obj.end.time

                # 检查时间跨度是否合理（至少应该有一定的时间差）
                if start_time == end_time:
                    return None  # 跳过无效中枢

        # 获取中枢起止位置
        zs_start_idx = zs_obj.begin.idx if hasattr(zs_obj, 'begin') and zs_obj.begin else 0
        zs_end_idx = zs_obj.end.idx if hasattr(zs_obj, 'end') and zs_obj.end else len(kl_data.lst) - 1

        # 特殊处理：如果中枢有出笔且未确定，成交量计算范围应该到出笔开始前
        if hasattr(zs_obj, 'bi_out') and zs_obj.bi_out and not zs_obj.is_sure:
            # 出笔开始的索引就是出笔的起始位置，成交量计算应该到这之前
            bi_out_start_idx = zs_obj.bi_out.idx
            # 成交量计算范围：从中枢起点到出笔开始前
            zs_end_idx = min(zs_end_idx, bi_out_start_idx - 1)

        # 确保索引在有效范围内
        zs_start_idx = max(0, min(zs_start_idx, len(kl_data.lst) - 1))
        zs_end_idx = max(0, min(zs_end_idx, len(kl_data.lst) - 1))

        # 查找中枢范围内的成交量峰值
        max_volume = 0
        max_volume_time = None
        max_volume_price = 0

        for i in range(zs_start_idx, zs_end_idx + 1):
            kline = kl_data.lst[i]
            if hasattr(kline, 'lst') and kline.lst:
                klu = kline.lst[-1]  # 获取K线单元
                # 从 trade_info.metric 中获取成交量
                volume = klu.trade_info.metric.get('volume', 0)
                if volume and volume > max_volume:
                    max_volume = volume
                    max_volume_time = klu.time
                    max_volume_price = klu.close

        # 获取中枢起止时间（直接从中枢对象获取）
        start_time = None
        end_time = None

        if hasattr(zs_obj, 'begin') and hasattr(zs_obj.begin, 'time'):
            start_time = zs_obj.begin.time

        if hasattr(zs_obj, 'end') and hasattr(zs_obj.end, 'time'):
            end_time = zs_obj.end.time

        # 如果直接获取失败，尝试从K线数据获取
        if start_time is None and zs_start_idx < len(kl_data.lst):
            start_kline = kl_data.lst[zs_start_idx]
            if hasattr(start_kline, 'lst') and start_kline.lst:
                start_time = start_kline.lst[-1].time

        if end_time is None and zs_end_idx < len(kl_data.lst):
            end_kline = kl_data.lst[zs_end_idx]
            if hasattr(end_kline, 'lst') and end_kline.lst:
                end_time = end_kline.lst[-1].time

        return {
            'start_time': start_time,
            'end_time': end_time,
            'max_volume': max_volume,
            'max_volume_time': max_volume_time,
            'max_volume_price': max_volume_price,
            'start_idx': zs_start_idx,
            'end_idx': zs_end_idx
        }

    except Exception:
        return {
            'start_time': None,
            'end_time': None,
            'max_volume': 0,
            'max_volume_time': None,
            'max_volume_price': 0,
            'start_idx': 0,
            'end_idx': 0
        }


def get_latest_zs_upper_price(chan_obj):
    """
    获取最新中枢的上沿价格（保留原函数用于兼容性）
    """
    try:
        # 获取第一级别的数据
        kl_data = chan_obj[0]

        # 获取中枢列表
        zs_list = kl_data.zs_list.zs_lst

        if not zs_list:
            return None, None, "未找到中枢"

        # 获取最后一个中枢
        latest_zs = zs_list[-1]

        # 返回中枢上沿价格和中枢信息
        return latest_zs.high, latest_zs, f"最新中枢: 下沿={latest_zs.low:.2f}, 上沿={latest_zs.high:.2f}, 中值={latest_zs.mid:.2f}"

    except Exception as e:
        return None, None, f"获取中枢信息出错: {e}"


def check_3buy_conditions(stock_code: str, stock_name: str, k_type: KL_TYPE = KL_TYPE.K_DAY) -> Optional[dict]:
    """
    检查单只股票的3买条件

    参数:
        stock_code: 股票代码
        stock_name: 股票名称
        k_type: K线级别

    返回:
        如果符合3买条件，返回包含详细信息的字典，否则返回None
    """
    try:
        # 从QMT获取数据
        period_map = {
            KL_TYPE.K_DAY: '1d',
            KL_TYPE.K_WEEK: '1w',
            KL_TYPE.K_60M: '60m',
            KL_TYPE.K_30M: '30m',
            KL_TYPE.K_15M: '15m',
            KL_TYPE.K_5M: '5m',
            KL_TYPE.K_1M: '1m'
        }



        period = period_map.get(k_type, '1d')

        # 首先下载最新数据（批量分析模式）
        try:
            from Zstock_tech_qmttdx import download_all_data_for_stock
            download_all_data_for_stock(stock_code)
        except Exception:
            pass

        # 特殊处理30分钟K线
        if k_type == KL_TYPE.K_30M:
            df_5m = get_miniqmt_data(stock_code, period='5m', count=3000)

            if df_5m.empty or 'error' in df_5m.columns:
                df = get_miniqmt_data(stock_code, period=period, count=1000)
            else:
                # 将5分钟数据转换为30分钟数据
                df = convert_5min_to_30min(df_5m)
        else:
            df = get_miniqmt_data(stock_code, period=period, count=1000)

        if df.empty or 'error' in df.columns:
            return None

        # 统一列名映射
        column_mapping = {
            '日期': 'time',
            '时间': 'time',
            'date': 'time',
            '开盘': 'open',
            '最高': 'high',
            '最低': 'low',
            '收盘': 'close',
            '成交量': 'volume',
            '成交额': 'amount'
        }

        # 重命名列
        df_renamed = df.rename(columns=column_mapping)

        # 转换数据格式
        csv_lines = []
        seen_timestamps = set()  # 用于检测重复时间戳

        for i, (_, row) in enumerate(df_renamed.iterrows()):
            time_str = str(row['time'])

            # 对于30分钟数据，需要保留完整的时间信息
            if k_type == KL_TYPE.K_30M:
                # 30分钟数据需要包含时间信息
                if '-' in time_str and ' ' in time_str:
                    # 格式: 2024-01-01 09:30:00
                    datetime_part = time_str.split('.')[0]  # 去掉毫秒部分
                    # 缠论库期望的格式是 YYYY/MM/DD HH:MM:SS
                    try:
                        dt = datetime.strptime(datetime_part, '%Y-%m-%d %H:%M:%S')
                        date_part = dt.strftime('%Y/%m/%d %H:%M:%S')
                    except:
                        # 如果解析失败，使用原始格式
                        date_part = datetime_part.replace('-', '/').replace('T', ' ')
                elif '-' in time_str:
                    # 只有日期，这不应该发生在30分钟数据中
                    date_part = time_str.split(' ')[0].replace('-', '/') + ' 09:30:00'
                else:
                    # 格式: 20240101，这也不应该发生在30分钟数据中
                    date_part = f"{time_str[:4]}/{time_str[4:6]}/{time_str[6:8]} 09:30:00"
            else:
                # 日线数据只需要日期
                if '-' in time_str:
                    date_part = time_str.split(' ')[0].replace('-', '/')
                else:
                    date_part = f"{time_str[:4]}/{time_str[4:6]}/{time_str[6:8]}"

            # 检测重复时间戳
            if date_part in seen_timestamps:
                continue
            seen_timestamps.add(date_part)

            # 获取成交量和成交额数据
            volume = row.get('volume', 0)
            amount = row.get('amount', 0)
            turnover_rate = row.get('turnover_rate', 0)

            # 确保数据不为空
            if volume is None or pd.isna(volume):
                volume = 0
            if amount is None or pd.isna(amount):
                amount = 0
            if turnover_rate is None or pd.isna(turnover_rate):
                turnover_rate = 0

            # 现在缠论库支持8列数据：时间+OHLC+成交量+成交额+换手率
            line = f"{date_part},{row['open']},{row['high']},{row['low']},{row['close']},{volume},{amount},{turnover_rate}"
            csv_lines.append(line)

        # 创建临时CSV文件供缠论使用（参考chan_helloworld_en.py的成功做法）
        # 创建临时CSV文件在chan目录下（关键修正）
        chan_dir = os.path.join(os.path.dirname(__file__), 'libs', 'chan')
        if not os.path.exists(chan_dir):
            chan_dir = os.path.dirname(__file__)  # 如果chan目录不存在，使用当前目录

        # 使用chan库期望的文件命名格式
        if CHAN_AVAILABLE:
            period_suffix = {
                KL_TYPE.K_DAY: 'day',
                KL_TYPE.K_WEEK: 'week',
                KL_TYPE.K_30M: '30m'
            }.get(k_type, 'day')
        else:
            period_suffix = period.replace('d', 'day').replace('w', 'week').replace('m', 'm')

        temp_csv_file = os.path.join(chan_dir, f"{stock_code}_{period_suffix}.csv")

        # 写入临时CSV文件
        with open(temp_csv_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(csv_lines))

        # 使用配置排列组合获取最大的中枢上沿
        upper_price, latest_zs, zs_info, best_config, volume_info, temp_csv_file, best_chan_obj = get_latest_zs_upper_price_with_configs(
            stock_code, csv_lines, k_type
        )

        if upper_price is None:
            return None

        # 输出中枢选择的详细信息（与调试模式保持一致）
        print(f"  {zs_info}")

        # 输出最佳配置信息
        if best_config:
            if isinstance(best_config, str):
                print(f"  使用的最佳配置: {best_config}")
            elif isinstance(best_config, dict) and best_config:
                config_str = f"bi_algo={best_config.get('bi_algo', 'N/A')}, seg_algo={best_config.get('seg_algo', 'N/A')}, zs_algo={best_config.get('zs_algo', 'N/A')}"
                print(f"  使用的最佳配置: {config_str}")
            else:
                print(f"  使用的最佳配置: {best_config}")
        else:
            print("  使用的最佳配置: 未获取到配置信息")

        # 使用最佳配置的缠论对象（避免重新创建导致的不一致）
        try:
            if best_chan_obj is None:
                return None

            chan = best_chan_obj

            # 获取K线数据
            kl_data = chan[0]
            if not kl_data or len(kl_data.lst) == 0:
                return None

            # 获取最新K线（CKLine对象包含多个K线单元）
            latest_kline = kl_data.lst[-1]

            # CKLine对象包含多个K线单元，获取最后一个
            if hasattr(latest_kline, 'lst') and latest_kline.lst:
                latest_klu = latest_kline.lst[-1]  # 获取最后一个K线单元
                latest_close = latest_klu.close
                latest_time = latest_klu.time
            else:
                return None

            # 获取最新K线（CKLine对象包含多个K线单元）
            latest_kline = kl_data.lst[-1]

            # CKLine对象包含多个K线单元，获取最后一个
            if hasattr(latest_kline, 'lst') and latest_kline.lst:
                latest_klu = latest_kline.lst[-1]  # 获取最后一个K线单元
                latest_time = latest_klu.time
            else:
                return None

            # 判断中枢是否已确定
            zs_status = "已确定" if latest_zs.is_sure else "未确定"
            print(f"  中枢状态: {zs_status}")

            # 检查是否有出中枢的笔
            if hasattr(latest_zs, 'bi_out') and latest_zs.bi_out:
                print(f"  该中枢有出笔，索引为: {latest_zs.bi_out.idx}")
            else:
                print(f"  该中枢尚未有笔离开")

            # 条件a: 最新周期的收盘价位于中枢上沿之上
            condition_a = latest_close > upper_price
            print(f"  条件a - 收盘价{latest_close:.2f} > 上沿{upper_price:.2f}: {'通过' if condition_a else '不通过'}")

            # 条件b: 最新周期的收盘价不超过中枢上沿的115%
            upper_limit = upper_price * 1.15
            condition_b = latest_close <= upper_limit
            print(f"  条件b - 收盘价{latest_close:.2f} <= 上沿115%({upper_limit:.2f}): {'通过' if condition_b else '不通过'}")

            # 条件c: 在最新价与最近一个收盘价小于中枢上沿的K线之间，最高的K线需小于中枢上沿的1.25倍
            condition_c = True
            max_high_in_range = 0
            upper_125_limit = upper_price * 1.25

            # 找到最近一个收盘价小于中枢上沿的K线位置
            last_below_upper_idx = -1
            for i in range(len(kl_data.lst) - 1, -1, -1):
                kline = kl_data.lst[i]
                if hasattr(kline, 'lst') and kline.lst:
                    klu = kline.lst[-1]  # 获取K线单元
                    if klu.close < upper_price:
                        last_below_upper_idx = i
                        break

            if last_below_upper_idx >= 0:
                # 检查从该位置到最新K线之间的最高价
                for i in range(last_below_upper_idx, len(kl_data.lst)):
                    kline = kl_data.lst[i]
                    if hasattr(kline, 'lst') and kline.lst:
                        klu = kline.lst[-1]  # 获取K线单元
                        max_high_in_range = max(max_high_in_range, klu.high)

                condition_c = max_high_in_range < upper_125_limit
            print(f"  条件c - 区间最高价{max_high_in_range:.2f} < 上沿125%({upper_125_limit:.2f}): {'通过' if condition_c else '不通过'}")

            # 条件d: 最近15个交易日至少有1个交易日的成交量高过最近一个中枢范围内成交量最大的那一天
            condition_d = True
            max_volume_in_zs = 0
            max_volume_in_recent15 = 0

            # 获取中枢范围内的成交量最大值
            zs_start_idx = latest_zs.begin.idx if hasattr(latest_zs, 'begin') and latest_zs.begin else 0
            zs_end_idx = latest_zs.end.idx if hasattr(latest_zs, 'end') and latest_zs.end else len(kl_data.lst) - 1

            # 特殊处理：如果中枢有出笔且未确定，成交量计算范围应该到出笔开始前
            if hasattr(latest_zs, 'bi_out') and latest_zs.bi_out and not latest_zs.is_sure:
                # 出笔开始的索引就是出笔的起始位置，成交量计算应该到这之前
                bi_out_start_idx = latest_zs.bi_out.idx
                # 成交量计算范围：从中枢起点到出笔开始前
                zs_end_idx = min(zs_end_idx, bi_out_start_idx - 1)

            # 确保索引在有效范围内
            zs_start_idx = max(0, min(zs_start_idx, len(kl_data.lst) - 1))
            zs_end_idx = max(0, min(zs_end_idx, len(kl_data.lst) - 1))

            # 计算中枢范围内的最大成交量 - 使用原始数据修正缠论库的数据缺失
            max_volume_in_zs_date = None

            # 先用缠论库数据计算
            for i in range(zs_start_idx, zs_end_idx + 1):
                kline = kl_data.lst[i]
                if hasattr(kline, 'lst') and kline.lst:
                    klu = kline.lst[-1]  # 获取K线单元
                    # 从 trade_info.metric 中获取成交量
                    volume = klu.trade_info.metric.get('volume', 0)
                    if volume and volume > 0:
                        if volume > max_volume_in_zs:
                            max_volume_in_zs = volume
                            max_volume_in_zs_date = klu.time

            # 获取原始数据进行修正检查（与调试模式保持一致）
            df_raw = None
            try:
                # 获取原始数据来修正中枢内最大成交量
                formatted_code = f"{stock_code}.SH" if stock_code.startswith(('60', '68', '11', '50', '51', '52')) else f"{stock_code}.SZ"

                # 获取原始K线数据
                kline_data_raw = xtdata.get_market_data(
                    stock_list=[formatted_code],
                    period='1d',
                    start_time='',
                    end_time='',
                    count=500,
                    dividend_type='none',
                    fill_data=True
                )

                if kline_data_raw and 'time' in kline_data_raw:
                    dates = pd.to_datetime(kline_data_raw.get('time').loc[formatted_code], unit='ms', utc=True).dt.tz_convert('Asia/Shanghai').dt.tz_localize(None)
                    df_raw = pd.DataFrame({'日期': dates})

                    for field in ['open', 'high', 'low', 'close', 'volume', 'amount']:
                        if field in kline_data_raw:
                            field_data = kline_data_raw[field]
                            if isinstance(field_data, pd.DataFrame) and formatted_code in field_data.index:
                                df_raw[field] = field_data.loc[formatted_code].values

                    # 使用正确的中枢时间范围（从volume_info获取，而不是从调整后的索引重新计算）
                    zs_start_time = volume_info['start_time'] if volume_info else None
                    zs_end_time = volume_info['end_time'] if volume_info else None

                    # 如果volume_info中没有时间信息，才从原始中枢索引获取（使用未调整的索引）
                    if not zs_start_time or not zs_end_time:
                        original_zs_start_idx = latest_zs.begin.idx if hasattr(latest_zs, 'begin') and latest_zs.begin else 0
                        original_zs_end_idx = latest_zs.end.idx if hasattr(latest_zs, 'end') and latest_zs.end else len(kl_data.lst) - 1

                        if not zs_start_time and original_zs_start_idx < len(kl_data.lst):
                            start_kline = kl_data.lst[original_zs_start_idx]
                            if hasattr(start_kline, 'lst') and start_kline.lst:
                                zs_start_time = start_kline.lst[-1].time

                        if not zs_end_time and original_zs_end_idx < len(kl_data.lst):
                            end_kline = kl_data.lst[original_zs_end_idx]
                            if hasattr(end_kline, 'lst') and end_kline.lst:
                                zs_end_time = end_kline.lst[-1].time

                    # 在原始数据中查找这个时间范围内的最大成交量
                    for _, row in df_raw.iterrows():
                        date_str = str(row['日期'])
                        volume = row.get('volume', 0)

                        # 精确的日期范围检查：只处理中枢时间范围内的数据
                        if volume and volume > 0:
                            # 使用pandas的datetime进行精确比较，避免字符串比较的问题
                            try:
                                row_datetime = pd.to_datetime(row['日期'])

                                # 转换中枢起止时间为datetime对象
                                if zs_start_time and zs_end_time:
                                    # 处理CTime对象
                                    if hasattr(zs_start_time, 'to_str'):
                                        start_str = zs_start_time.to_str().replace('/', '-')
                                        zs_start_dt = pd.to_datetime(start_str)
                                    else:
                                        zs_start_dt = pd.to_datetime(str(zs_start_time))

                                    if hasattr(zs_end_time, 'to_str'):
                                        end_str = zs_end_time.to_str().replace('/', '-')
                                        zs_end_dt = pd.to_datetime(end_str)
                                    else:
                                        zs_end_dt = pd.to_datetime(str(zs_end_time))

                                    # 精确的日期范围检查
                                    is_in_range = zs_start_dt <= row_datetime <= zs_end_dt
                                else:
                                    is_in_range = False

                                if is_in_range:
                                    # 如果这个成交量比当前记录的中枢内最大成交量还大，更新它
                                    if volume > max_volume_in_zs:
                                        max_volume_in_zs = volume
                                        max_volume_in_zs_date = date_str
                            except Exception:
                                # 如果日期转换失败，跳过这条记录
                                continue

            except Exception:
                pass

            # 计算最近15个交易日的最大成交量（但不得进入中枢范围）
            max_volume_in_recent15_date = None

            # 获取中枢结束索引
            zs_end_idx = latest_zs.end.idx if hasattr(latest_zs, 'end') and latest_zs.end else len(kl_data.lst) - 1

            # 使用已获取的原始数据计算最近15日成交量（与调试模式保持一致）
            print(f"  调试: 开始原始数据获取")
            try:
                # 重用已获取的原始数据
                if df_raw is not None:
                    # 确定考察起始时间：如果中枢在最近15日内结束，则从中枢结束后开始考察
                    recent_15_days = datetime.now() - timedelta(days=15)
                    zs_end_time = None

                    # 优先从volume_info获取，如果没有则从中枢对象获取
                    if volume_info and volume_info.get('end_time'):
                        zs_end_time = volume_info.get('end_time')
                        print(f"  调试: 从volume_info获取时间, type={type(zs_end_time)}, value={zs_end_time}")
                    elif hasattr(latest_zs, 'end') and hasattr(latest_zs.end, 'time'):
                        zs_end_time = latest_zs.end.time
                        print(f"  调试: 从中枢对象获取时间, type={type(zs_end_time)}, value={zs_end_time}")

                    if zs_end_time:
                        # 将CTime对象转换为datetime对象
                        if hasattr(zs_end_time, 'to_str'):
                            # CTime对象，转换为datetime
                            zs_end_time_str = zs_end_time.to_str().replace('/', '-')
                            zs_end_time_dt = pd.to_datetime(zs_end_time_str)
                        else:
                            # 已经是datetime对象
                            zs_end_time_dt = zs_end_time

                        if zs_end_time_dt >= recent_15_days:
                            # 中枢在最近15日内结束，先尝试只考察中枢结束后的数据
                            start_time = zs_end_time_dt + timedelta(days=1)  # 中枢结束后的第一天
                            recent_data = df_raw[df_raw['日期'] >= start_time]
                            print(f"  调试: 中枢在最近15日内结束({zs_end_time_dt.strftime('%Y-%m-%d')})，只考察中枢结束后的成交量")

                            # 如果中枢结束后没有数据，则考察最近15日（中枢持续至今的情况）
                            if len(recent_data) == 0:
                                recent_data = df_raw.tail(15)
                                print(f"  调试: 中枢结束后无数据，改为考察最近15日成交量（中枢持续至今）")
                        else:
                            # 中枢不在最近15日内，考察整个最近15日
                            recent_data = df_raw.tail(15)
                            print(f"  调试: 中枢不在最近15日内，考察最近15日成交量")

                    # 使用筛选后的数据
                    print(f"  调试: 原始数据筛选后行数: {len(recent_data)}")
                    if len(recent_data) == 0:
                        print(f"  调试: 筛选后数据为空，回退到缠论库数据")
                        raise Exception("筛选后数据为空")

                    for i, row in recent_data.iterrows():
                        date_str = str(row['日期'])
                        volume = row.get('volume', 0)
                        if volume and volume > max_volume_in_recent15:
                            max_volume_in_recent15 = volume
                            max_volume_in_recent15_date = date_str
                else:
                    # 如果原始数据获取失败，回退到缠论库数据
                    raise Exception("原始数据获取失败")

            except Exception as e:
                # 如果没有原始数据，回退到缠论库数据
                print(f"  调试: 原始数据获取失败，回退到缠论库数据: {e}")
                recent_start_idx = max(0, len(kl_data.lst) - 15)
                if zs_end_idx >= recent_start_idx:
                    # 中枢在最近15个交易日内结束，只考察中枢结束后的数据
                    start_idx = zs_end_idx + 1
                    print(f"  调试: 中枢在最近15个交易日内结束，只考察中枢结束后的成交量")

                    # 如果中枢结束后没有数据，则考察最近15日（中枢持续至今的情况）
                    if start_idx >= len(kl_data.lst):
                        start_idx = recent_start_idx
                        print(f"  调试: 中枢结束后无数据，改为考察最近15个交易日成交量（中枢持续至今）")
                else:
                    # 中枢不在最近15个交易日内，考察整个最近15个交易日
                    start_idx = recent_start_idx
                    print(f"  调试: 中枢不在最近15个交易日内，考察最近15个交易日成交量")

                for i in range(start_idx, len(kl_data.lst)):
                    kline = kl_data.lst[i]
                    if hasattr(kline, 'lst') and kline.lst:
                        klu = kline.lst[-1]  # 获取K线单元
                        # 从 trade_info.metric 中获取成交量
                        volume = klu.trade_info.metric.get('volume', 0)
                        if volume and volume > 0:
                            if volume > max_volume_in_recent15:
                                max_volume_in_recent15 = volume
                                max_volume_in_recent15_date = klu.time

            # 原有条件：最近15日最大成交量 >= 中枢内最大成交量
            condition_d_daily = max_volume_in_recent15 >= max_volume_in_zs
            print(f"  调试: 日线条件判断完成, condition_d_daily={condition_d_daily}")

            # 新增条件：倒数第二根周K线成交量 > 中枢范围内最高周K线成交量
            condition_d_weekly = False
            print(f"  调试: 开始周K线成交量判断")
            try:
                # 获取周K线数据
                weekly_df = get_weekly_data_for_3buy(stock_code, count=200)
                if not weekly_df.empty and len(weekly_df) >= 2:
                    # 获取倒数第二根周K线的成交量
                    second_last_weekly_volume = weekly_df.iloc[-2]['成交量']

                    # 获取中枢起止时间
                    zs_start_time = volume_info.get('start_time')
                    zs_end_time = volume_info.get('end_time')

                    if zs_start_time and zs_end_time:
                        # 转换时间格式
                        if hasattr(zs_start_time, 'to_str'):
                            zs_start_str = zs_start_time.to_str()
                        else:
                            zs_start_str = str(zs_start_time)

                        if hasattr(zs_end_time, 'to_str'):
                            zs_end_str = zs_end_time.to_str()
                        else:
                            zs_end_str = str(zs_end_time)

                        # 处理日期格式（可能包含'/'）
                        if '/' in zs_start_str:
                            zs_start_str = zs_start_str.replace('/', '-')
                        if '/' in zs_end_str:
                            zs_end_str = zs_end_str.replace('/', '-')

                        zs_start_date = pd.to_datetime(zs_start_str)
                        zs_end_date = pd.to_datetime(zs_end_str)

                        # 找到中枢范围内的周K线数据
                        zs_weekly_data = weekly_df[
                            (weekly_df['日期'] >= zs_start_date) &
                            (weekly_df['日期'] <= zs_end_date)
                        ]

                        if not zs_weekly_data.empty:
                            # 获取中枢范围内最大周成交量
                            max_weekly_volume_in_zs = zs_weekly_data['成交量'].max()

                            # 判断条件
                            condition_d_weekly = second_last_weekly_volume > max_weekly_volume_in_zs

                            print(f"  调试: 周K线成交量判断:")
                            print(f"  调试:   倒数第二根周K线成交量: {second_last_weekly_volume:.0f}")
                            print(f"  调试:   中枢范围内最大周成交量: {max_weekly_volume_in_zs:.0f}")
                            print(f"  调试:   周K线条件: {'通过' if condition_d_weekly else '不通过'}")
                        else:
                            print(f"  调试: 中枢范围内未找到周K线数据")
                    else:
                        print(f"  调试: 无法获取中枢起止时间，跳过周K线判断")
                else:
                    print(f"  调试: 周K线数据不足，跳过周K线判断")
            except Exception as e:
                print(f"  调试: 周K线成交量判断出错: {e}")
                condition_d_weekly = False

            # 条件d：满足任一成交量条件即可
            condition_d = condition_d_daily or condition_d_weekly
            print(f"  条件d - 成交量条件: 日线{'通过' if condition_d_daily else '不通过'} 或 周线{'通过' if condition_d_weekly else '不通过'} = {'通过' if condition_d else '不通过'}")
            print(f"  调试信息: max_volume_in_recent15={max_volume_in_recent15}, max_volume_in_zs={max_volume_in_zs}")

            # 获取中枢内成交量最大的日期
            max_volume_in_zs_date = None
            for i in range(zs_start_idx, zs_end_idx + 1):
                kline = kl_data.lst[i]
                if hasattr(kline, 'lst') and kline.lst:
                    klu = kline.lst[-1]  # 获取K线单元
                    volume = klu.trade_info.metric.get('volume', 0)
                    if volume and volume == max_volume_in_zs:
                        max_volume_in_zs_date = klu.time
                        break

            # 重新计算中枢内成交量最大值和日期（只使用缠论库数据，避免原始数据修正的干扰）
            max_volume_in_zs_chan = 0
            max_volume_in_zs_date = None
            for i in range(zs_start_idx, zs_end_idx + 1):
                kline = kl_data.lst[i]
                if hasattr(kline, 'lst') and kline.lst:
                    klu = kline.lst[-1]  # 获取K线单元
                    volume = klu.trade_info.metric.get('volume', 0)
                    if volume and volume > max_volume_in_zs_chan:
                        max_volume_in_zs_chan = volume
                        max_volume_in_zs_date = klu.time

            # 使用缠论库的数据作为最终结果，确保日期范围的准确性
            max_volume_in_zs = max_volume_in_zs_chan

            # 输出关键信息（使用logger确保在批量模式下也能看到）
            logger = logging.getLogger()
            logger.info(f"最终确定中枢的上沿值: {upper_price:.2f}")
            logger.info(f"最终确定中枢的起止时间: {volume_info['start_time']} 到 {volume_info['end_time']}")
            logger.info(f"使用的最佳配置: {best_config}")

            # 判断中枢是否完结
            zs_status = "已确定" if latest_zs.is_sure else "未确定"
            if hasattr(latest_zs, 'bi_out') and latest_zs.bi_out:
                bi_out_start_time = None
                bi_out_end_time = None
                # 获取出笔的起始和终止时间
                if hasattr(latest_zs.bi_out, 'begin') and hasattr(latest_zs.bi_out.begin, 'time'):
                    bi_out_start_time = latest_zs.bi_out.begin.time
                if hasattr(latest_zs.bi_out, 'end') and hasattr(latest_zs.bi_out.end, 'time'):
                    bi_out_end_time = latest_zs.bi_out.end.time
                logger.info(f"最终确定中枢是否完结: {zs_status}，出笔的起始终止时间: {bi_out_start_time} 到 {bi_out_end_time}")
            else:
                logger.info(f"最终确定中枢是否完结: {zs_status}，尚未有笔离开")

            logger.info(f"中枢内成交量最大的日期及成交量: {max_volume_in_zs_date} - {max_volume_in_zs:.0f}")
            # 根据中枢位置决定日志输出文字
            zs_end_idx = latest_zs.end.idx if hasattr(latest_zs, 'end') and latest_zs.end else len(kl_data.lst) - 1
            recent_start_idx = max(0, len(kl_data.lst) - 15)
            if zs_end_idx >= recent_start_idx:
                logger.info(f"中枢结束后最大成交量日期及成交量: {max_volume_in_recent15_date} - {max_volume_in_recent15:.0f}")
            else:
                logger.info(f"最近15天最大的成交量日期及成交量: {max_volume_in_recent15_date} - {max_volume_in_recent15:.0f}")

            # 判断三买过程（使用logger确保在批量模式下也能看到）
            logger.info(f"判断三买过程:")
            logger.info(f"  当前价: {latest_close:.2f}")
            logger.info(f"  中枢上沿: {upper_price:.2f}")
            logger.info(f"  条件a - 收盘价{latest_close:.2f} > 上沿{upper_price:.2f}: {'通过' if condition_a else '不通过'}")
            logger.info(f"  条件b - 收盘价{latest_close:.2f} <= 上沿115%({upper_limit:.2f}): {'通过' if condition_b else '不通过'}")
            logger.info(f"  条件c - 区间最高价{max_high_in_range:.2f} < 上沿125%({upper_125_limit:.2f}): {'通过' if condition_c else '不通过'}")
            logger.info(f"  条件d - 成交量条件（满足任一即可）:")
            logger.info(f"    日线条件: 最近15日最大成交量{max_volume_in_recent15:.0f} >= 中枢内最大成交量{max_volume_in_zs:.0f}: {'通过' if condition_d_daily else '不通过'}")
            logger.info(f"    周线条件: {'通过' if condition_d_weekly else '不通过'}")
            logger.info(f"    条件d总体: {'通过' if condition_d else '不通过'}")

            # 判断是否符合3买
            is_3buy = condition_a and condition_b and condition_c and condition_d

            # 输出判断结论并返回结果
            if is_3buy:
                logger.info(f"判断结论: 符合3买条件")
                result = {
                    'stock_code': stock_code,
                    'stock_name': stock_name,
                    'latest_close': latest_close,
                    'zs_upper': upper_price,
                    'zs_lower': latest_zs.low,
                    'zs_mid': latest_zs.mid,
                    'upper_limit': upper_limit,
                    'upper_125_limit': upper_125_limit,
                    'max_high_in_range': max_high_in_range,
                    'max_volume_in_zs': max_volume_in_zs,
                    'max_volume_in_recent15': max_volume_in_recent15,
                    'latest_time': latest_time,
                    'k_type': k_type,
                    'kline_count': len(csv_lines),  # 添加K线数量
                    'best_config': None,  # 添加最佳配置
                    'volume_info': volume_info  # 添加成交量信息
                }
                return result
            else:
                logger.info(f"判断结论: 不符合3买条件")
                return None

        finally:
            # 清理临时CSV文件（如果存在的话）
            try:
                if 'temp_csv_file' in locals() and temp_csv_file and os.path.exists(temp_csv_file):
                    os.remove(temp_csv_file)
            except Exception:
                pass

    except Exception:
        return None


def read_existing_cljx_stocks():
    """
    读取cljx.blk中现有的股票代码
    
    返回:
        List[Tuple[str, str]]: (带前缀代码, 6位代码) 元组列表
    """
    cljx_file = os.path.join(BLOCKNEW_DIR, "cljx.blk")
    
    if not os.path.exists(cljx_file):
        logger.info(f"cljx.blk文件不存在，将创建新文件")
        return []
    
    try:
        # 使用健壮的读取方式
        with open(cljx_file, 'rb') as f:
            content = f.read()
        
        # 解码文本
        text = content.decode('gbk', errors='ignore')
        
        # 分割行并去掉空行
        lines = [line.strip() for line in text.replace('\r\n', '\n').split('\n') if line.strip()]
        
        # 转换为(带前缀代码, 6位代码)格式
        stock_pairs = []
        for line in lines:
            if len(line) == 7 and line.isdigit():
                # 带前缀的7位代码
                prefix_code = line
                six_digit_code = line[1:]  # 去掉前缀
                stock_pairs.append((prefix_code, six_digit_code))
            elif len(line) == 6 and line.isdigit():
                # 6位代码，需要添加前缀
                six_digit_code = line
                prefix_code = add_tdx_market_prefix(six_digit_code)
                stock_pairs.append((prefix_code, six_digit_code))
        
        
        logger.info(f"从cljx.blk读取到{len(stock_pairs)}只现有股票")
        return stock_pairs
        
    except Exception as e:
        logger.error(f"读取cljx.blk文件失败: {e}")
        return []


def clean_existing_stocks():
    """
    清理cljx.blk中不符合3买条件的存量股票
    
    返回:
        Tuple[List[str], List[str]]: (保留的股票代码列表, 清理的股票代码列表)
    """
    logger.info("\n" + "="*60)
    logger.info("开始清理cljx.blk中的存量股票")
    logger.info("="*60)
    
    # 如果缠论模块不可用，则跳过清理
    if not CHAN_AVAILABLE:
        logger.warning("缠论模块不可用，无法执行存量股票清理。跳过此步骤。")
        return [], []
    
    # 读取现有股票
    existing_stocks = read_existing_cljx_stocks()
    
    if not existing_stocks:
        logger.info("cljx.blk中没有现有股票，跳过清理步骤")
        return [], []
    
    logger.info(f"准备验证{len(existing_stocks)}只现有股票的3买条件")
    
    # 存储结果
    valid_stocks = []  # 仍符合条件的股票
    invalid_stocks = []  # 不符合条件需要清理的股票
    
    # 逐个验证现有股票
    for i, (prefix_code, six_digit_code) in enumerate(existing_stocks, 1):
        logger.info(f"\n验证进度: {i}/{len(existing_stocks)} - {six_digit_code}")
        
        # 分析日线级别
        day_qualified = False
        min30_qualified = False
        
        day_result = check_3buy_conditions(six_digit_code, f"存量股票{six_digit_code}", KL_TYPE.K_DAY)
        min30_result = check_3buy_conditions(six_digit_code, f"存量股票{six_digit_code}", KL_TYPE.K_30M)
        
        # 判断是否符合条件（任一级别符合即可）
        day_qualified = day_result is not None
        min30_qualified = min30_result is not None
        
        if day_qualified or min30_qualified:
            valid_stocks.append(prefix_code)
            logger.info(f"  ? 保留 {six_digit_code} - 日线:{'?' if day_qualified else '?'} 30分钟:{'?' if min30_qualified else '?'}")
        else:
            invalid_stocks.append(prefix_code)
            logger.info(f"  ? 清理 {six_digit_code} - 不符合3买条件")
        
        # 添加短暂延迟
        time.sleep(0.1)
    
    # 输出清理统计
    logger.info(f"\n清理统计:")
    logger.info(f"  原有股票: {len(existing_stocks)}只")
    logger.info(f"  保留股票: {len(valid_stocks)}只")
    logger.info(f"  清理股票: {len(invalid_stocks)}只")
    
    if invalid_stocks:
        # 将清理的股票代码转换为6位格式显示
        invalid_six_digit = [code[1:] if len(code) == 7 else code for code in invalid_stocks]
        logger.info(f"  清理的股票代码: {', '.join(invalid_six_digit)}")
    
    # 更新cljx.blk文件
    if valid_stocks:
        cljx_file = os.path.join(BLOCKNEW_DIR, "cljx.blk")
        success = write_block_file(valid_stocks, cljx_file, append=False)
        if success:
            logger.info(f"? 已更新cljx.blk，保留{len(valid_stocks)}只符合条件的股票")
        else:
            logger.error(f"? 更新cljx.blk失败")
    else:
        # 如果没有符合条件的股票，清空文件
        cljx_file = os.path.join(BLOCKNEW_DIR, "cljx.blk")
        try:
            with open(cljx_file, 'w', encoding='gbk'):
                pass  # 创建空文件
            logger.info("? cljx.blk已清空（没有符合条件的股票）")
        except Exception as e:
            logger.error(f"? 清空cljx.blk失败: {e}")
    
    return valid_stocks, invalid_stocks


def analyze_stocks_from_xls(xls_file: str, k_types = None):
    """
    从XLS文件分析股票的3买信号
    根据用户选择分析指定的时间级别

    参数:
        xls_file: XLS文件路径
        k_types: 要分析的K线级别列表
    """
    if k_types is None:
        k_types = [KL_TYPE.K_DAY, KL_TYPE.K_30M]

    # 显示分析级别
    level_names = {
        KL_TYPE.K_DAY: "日线",
        KL_TYPE.K_30M: "30分钟"
    }
    selected_levels = [level_names.get(k, str(k)) for k in k_types]

    logger.info("=" * 80)
    logger.info("缠论3买点检测程序")
    logger.info(f"分析文件: {xls_file}")
    logger.info(f"分析级别: {', '.join(selected_levels)}")
    if len(k_types) > 1:
        logger.info("只要任一级别满足条件即视为符合要求")
    logger.info("=" * 80)

    # 检查缠论模块是否可用
    if not CHAN_AVAILABLE:
        logger.error("缠论模块不可用，无法执行分析。请确保libs/chan目录完整且可用。")
        return

    # 检查文件是否存在
    if not os.path.exists(xls_file):
        logger.error(f"文件不存在: {xls_file}")
        return

    # 连接QMT
    logger.info("\n1. 连接QMT...")
    if not connect_miniqmt():
        logger.error("QMT连接失败，请检查QMT客户端是否运行")
        return
    logger.info("QMT连接成功")

    # 清理现有股票
    logger.info("\n2. 清理cljx.blk中的存量股票...")
    _, _ = clean_existing_stocks()  # 忽略返回值

    # 读取股票列表
    logger.info(f"\n3. 从Excel文件读取股票列表...")
    stock_list = read_tdx_xls(xls_file)

    if not stock_list:
        logger.error("文件中未找到有效股票")
        return

    logger.info(f"共找到{len(stock_list)}只股票")

    logger.info(f"\n4. 开始缠论3买信号分析...")
    logger.info("-" * 80)

    # 存储符合条件的股票
    qualified_stocks = []

    # 逐个分析股票
    for i, (stock_code, stock_name) in enumerate(stock_list, 1):
        logger.info(f"\n{'='*60}")
        logger.info(f"进度: {i}/{len(stock_list)} - 分析股票: {stock_code} {stock_name}")
        logger.info(f"{'='*60}")

        # 存储该股票在各级别的分析结果
        stock_results = {
            'stock_code': stock_code,
            'stock_name': stock_name,
            'day_result': None,
            '30m_result': None,
            'qualified': False
        }

        # 根据用户选择的级别进行分析
        if KL_TYPE.K_DAY in k_types:
            logger.info(f"\n【日线级别分析】")
            logger.info("-" * 40)
            day_result = check_3buy_conditions(stock_code, stock_name, KL_TYPE.K_DAY)
            stock_results['day_result'] = day_result
            if day_result:
                logger.info(f"  ? 日线级别符合3买条件!")
                stock_results['qualified'] = True
            else:
                logger.info(f"  ? 日线级别不符合3买条件")

        if KL_TYPE.K_30M in k_types:
            logger.info(f"\n【30分钟级别分析】")
            logger.info("-" * 40)
            min30_result = check_3buy_conditions(stock_code, stock_name, KL_TYPE.K_30M)
            stock_results['30m_result'] = min30_result
            if min30_result:
                logger.info(f"  ? 30分钟级别符合3买条件!")
                stock_results['qualified'] = True
            else:
                logger.info(f"  ? 30分钟级别不符合3买条件")

        # 综合判断
        logger.info(f"\n【综合判断】")
        logger.info("-" * 40)
        if stock_results['qualified']:
            logger.info(f"  ? 该股票符合3买要求（至少一个级别满足条件）")
            qualified_stocks.append(stock_results)
        else:
            logger.info(f"  ? 该股票不符合3买要求（两个级别都不满足条件）")

        # 添加短暂延迟避免请求过快
        time.sleep(0.1)

    # 输出最终汇总结果
    logger.info(f"\n{'='*80}")
    logger.info(f"最终汇总结果")
    logger.info(f"{'='*80}")

    if qualified_stocks:
        logger.info(f"共找到{len(qualified_stocks)}只符合3买条件的股票:")
        logger.info("")

        for stock_result in qualified_stocks:
            logger.info(f"股票: {stock_result['stock_code']} {stock_result['stock_name']}")

            # 根据分析的级别显示日线结果
            if KL_TYPE.K_DAY in k_types:
                day_result = stock_result['day_result']
                if day_result:
                    logger.info(f"  【日线级别】? 符合条件")
                    logger.info(f"    最新收盘价: {day_result['latest_close']:.2f}")
                    logger.info(f"    中枢上沿: {day_result['zs_upper']:.2f}, 中枢下沿: {day_result['zs_lower']:.2f}")
                    logger.info(f"    上沿115%限制: {day_result['upper_limit']:.2f}")
                    if 'upper_125_limit' in day_result and 'max_high_in_range' in day_result:
                        logger.info(f"    上沿125%限制: {day_result['upper_125_limit']:.2f}, 区间最高价: {day_result['max_high_in_range']:.2f}")
                    # 添加成交量信息
                    if 'max_volume_in_zs' in day_result and 'max_volume_in_recent10' in day_result:
                        logger.info(f"    中枢内最大成交量: {day_result['max_volume_in_zs']:.0f}, 最近10日最大成交量: {day_result['max_volume_in_recent10']:.0f}")
                    # 添加K线数量信息
                    if 'kline_count' in day_result:
                        logger.info(f"    使用K线数量: {day_result['kline_count']}根")
                    # 添加最佳配置信息
                    if 'best_config' in day_result:
                        config_name = create_config_name(day_result['best_config'])
                        logger.info(f"    最佳配置: {config_name}")
                    # 添加中枢详细信息
                    if 'volume_info' in day_result and day_result['volume_info']:
                        vol_info = day_result['volume_info']
                        logger.info(f"    中枢时间: {vol_info['start_time']} 到 {vol_info['end_time']}")
                        if vol_info['max_volume'] > 0:
                            logger.info(f"    成交量峰值: {vol_info['max_volume']:.0f} (时间: {vol_info['max_volume_time']}, 价格: {vol_info['max_volume_price']:.2f})")
                else:
                    logger.info(f"  【日线级别】? 不符合条件")

            # 根据分析的级别显示30分钟结果
            if KL_TYPE.K_30M in k_types:
                min30_result = stock_result['30m_result']
                if min30_result:
                    logger.info(f"  【30分钟级别】? 符合条件")
                    logger.info(f"    最新收盘价: {min30_result['latest_close']:.2f}")
                    logger.info(f"    中枢上沿: {min30_result['zs_upper']:.2f}, 中枢下沿: {min30_result['zs_lower']:.2f}")
                    logger.info(f"    上沿115%限制: {min30_result['upper_limit']:.2f}")
                    if 'upper_125_limit' in min30_result and 'max_high_in_range' in min30_result:
                        logger.info(f"    上沿125%限制: {min30_result['upper_125_limit']:.2f}, 区间最高价: {min30_result['max_high_in_range']:.2f}")
                    # 添加成交量信息
                    if 'max_volume_in_zs' in min30_result and 'max_volume_in_recent10' in min30_result:
                        logger.info(f"    中枢内最大成交量: {min30_result['max_volume_in_zs']:.0f}, 最近10日最大成交量: {min30_result['max_volume_in_recent10']:.0f}")
                    # 添加K线数量信息
                    if 'kline_count' in min30_result:
                        logger.info(f"    使用K线数量: {min30_result['kline_count']}根")
                    # 添加最佳配置信息
                    if 'best_config' in min30_result:
                        config_name = create_config_name(min30_result['best_config'])
                        logger.info(f"    最佳配置: {config_name}")
                    # 添加中枢详细信息
                    if 'volume_info' in min30_result and min30_result['volume_info']:
                        vol_info = min30_result['volume_info']
                        logger.info(f"    中枢时间: {vol_info['start_time']} 到 {vol_info['end_time']}")
                        if vol_info['max_volume'] > 0:
                            logger.info(f"    成交量峰值: {vol_info['max_volume']:.0f} (时间: {vol_info['max_volume_time']}, 价格: {vol_info['max_volume_price']:.2f})")
                else:
                    logger.info(f"  【30分钟级别】? 不符合条件")

            logger.info("")

        # 将符合条件的股票代码写入cljx.blk文件
        logger.info(f"\n{'='*80}")
        logger.info(f"写入cljx.blk文件")
        logger.info(f"{'='*80}")

        # 提取股票代码
        qualified_codes = [stock_result['stock_code'] for stock_result in qualified_stocks]

        # 【关键修改】添加通达信市场前缀
        logger.info(f"添加通达信市场前缀...")
        prefixed_codes = []
        for code in qualified_codes:
            prefixed_code = add_tdx_market_prefix(code)
            prefixed_codes.append(prefixed_code)
            if prefixed_code != code:
                logger.info(f"  {code} -> {prefixed_code}")
        
        logger.info(f"添加前缀后股票代码: {len(prefixed_codes)}只")

        # 过滤股票代码（去掉创业板等）
        logger.info(f"过滤前股票代码: {len(prefixed_codes)}只")
        filtered_codes = filter_stock_codes(prefixed_codes)
        logger.info(f"过滤后股票代码: {len(filtered_codes)}只")

        if filtered_codes:
            # 写入cljx.blk文件（追加模式）
            cljx_file = os.path.join(BLOCKNEW_DIR, "cljx.blk")
            logger.info(f"目标文件: {cljx_file}")

            success = write_block_file(filtered_codes, cljx_file, append=True)

            if success:
                logger.info(f"? 成功将{len(filtered_codes)}只股票增量写入cljx.blk文件")
                logger.info(f"写入的股票代码: {', '.join(filtered_codes)}")
                logger.info(f"注意：代码已添加通达信市场前缀，通达信中应该可以正常显示")
            else:
                logger.error(f"? 写入cljx.blk文件失败")
        else:
            logger.info("过滤后没有有效的股票代码，跳过写入")
    else:
        logger.info("未找到符合3买条件的股票")

    logger.info("-" * 80)


def generate_charts_for_all_configs(stock_code: str, stock_name: str, df_raw: pd.DataFrame,
                                   valid_results: List[dict], k_type: KL_TYPE):
    """
    为每个不同的上沿取值生成单独的图表
    确保默认配置的图表总是被生成

    参数:
        stock_code: 股票代码
        stock_name: 股票名称
        df_raw: 原始K线数据
        valid_results: 所有有效的中枢配置结果
        k_type: K线级别
    """
    try:
        logger = logging.getLogger()
        logger.info(f"开始为{len(valid_results)}个不同配置生成图表...")

        # 首先查找默认配置的结果
        default_result = None
        for result in valid_results:
            if result.get('config_name') == '默认配置':
                default_result = result
                break

        # 如果找到默认配置，优先生成默认配置的图表
        if default_result:
            logger.info("生成默认配置图表...")
            generate_single_config_chart(
                stock_code=stock_code,
                stock_name=stock_name,
                df_raw=df_raw,
                config_result=default_result,
                k_type=k_type,
                upper_key=round(default_result['upper'], 2),
                is_default=True
            )

        # 按上沿价格分组，相同上沿价格的配置只生成一张图表
        upper_groups = {}
        for result in valid_results:
            upper_price = result['upper']
            # 使用2位小数作为分组键，避免浮点数精度问题
            upper_key = round(upper_price, 2)
            if upper_key not in upper_groups:
                upper_groups[upper_key] = []
            upper_groups[upper_key].append(result)

        logger.info(f"共有{len(upper_groups)}个不同的上沿取值需要生成图表")

        # 为每个不同的上沿取值生成图表
        for upper_key, group_results in upper_groups.items():
            # 如果这个上沿价格组包含默认配置，跳过（已经生成过了）
            has_default = any(r.get('config_name') == '默认配置' for r in group_results)
            if has_default:
                continue

            # 选择该上沿价格组中持续时间最长的配置作为代表
            best_in_group = max(group_results, key=lambda x: x.get('duration', 0))

            # 生成图表
            generate_single_config_chart(
                stock_code=stock_code,
                stock_name=stock_name,
                df_raw=df_raw,
                config_result=best_in_group,
                k_type=k_type,
                upper_key=upper_key,
                is_default=False
            )

        logger.info(f"所有配置图表生成完成")

    except Exception as e:
        logger = logging.getLogger()
        logger.error(f"生成配置图表失败: {e}")


def generate_single_config_chart(stock_code: str, stock_name: str, df_raw: pd.DataFrame,
                                config_result: dict, k_type: KL_TYPE, upper_key: float, is_default: bool = False):
    """
    为单个配置生成图表

    参数:
        stock_code: 股票代码
        stock_name: 股票名称
        df_raw: 原始K线数据
        config_result: 配置结果字典
        k_type: K线级别
        upper_key: 上沿价格键值
        is_default: 是否为默认配置
    """
    try:
        # 导入缠论绘图模块
        from Plot.PlotDriver import CPlotDriver

        # 创建charts目录
        charts_dir = os.path.join(os.path.dirname(__file__), 'charts')
        if not os.path.exists(charts_dir):
            os.makedirs(charts_dir)

        # 确定级别名称
        level_name = "日线" if k_type == KL_TYPE.K_DAY else "30分钟"

        # 获取配置信息
        config_name = config_result['config_name']
        upper_price = config_result['upper']
        volume_info = config_result['volume_info']
        chan_obj = config_result['chan_obj']

        # 设置图表文件名，包含配置参数
        if is_default:
            # 默认配置使用特殊命名，确保容易识别
            chart_filename = f"{stock_code}_{level_name}_默认配置_上沿{upper_key:.2f}.png"
        else:
            safe_config_name = config_name.replace("+", "_").replace("/", "_").replace(":", "_")
            chart_filename = f"{stock_code}_{level_name}_上沿{upper_key:.2f}_{safe_config_name}.png"
        chart_path = os.path.join(charts_dir, chart_filename)

        # 如果文件已存在，先删除以确保能够覆盖
        if os.path.exists(chart_path):
            try:
                os.remove(chart_path)
                logger = logging.getLogger()
                logger.debug(f"删除旧图表文件: {chart_filename}")
            except Exception as e:
                logger = logging.getLogger()
                logger.warning(f"删除旧图表文件失败: {e}")

        if chan_obj is None:
            logger = logging.getLogger()
            logger.error(f"配置{config_name}的缠论对象为空，无法生成图表")
            return

        # 使用缠论模块生成主图
        plot_driver = CPlotDriver(
            chan_obj,
            plot_config={
                "plot_kline": True,
                "plot_bi": True,
                "plot_seg": True,
                "plot_zs": True,
                "plot_bs_point": True,
                "plot_macd": False,
            },
            plot_para={
                "figure": {
                    "x_range": 0,
                }
            }
        )

        # 生成缠论主图
        plot_driver.save2img(chart_path)

        logger = logging.getLogger()
        logger.info(f"配置图表已保存: {chart_filename}")

        # 直接在原图上添加配置信息，不再生成增强版
        add_config_info_to_chart(chart_path, config_result, stock_code, stock_name, level_name)

    except Exception as e:
        logger = logging.getLogger()
        logger.error(f"生成配置图表失败: {e}")


def add_config_info_to_chart(chart_path: str, config_result: dict,
                            stock_code: str, stock_name: str, level_name: str):
    """
    在配置图表上添加配置信息（简化版，不生成增强图表）

    参数:
        chart_path: 图表文件路径
        config_result: 配置结果字典
        stock_code: 股票代码
        stock_name: 股票名称
        level_name: 级别名称
    """
    try:
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False

        # 读取缠论生成的图片
        import matplotlib.image as mpimg
        img = mpimg.imread(chart_path)

        # 创建新的图表，只显示缠论主图和配置信息
        fig, ax = plt.subplots(1, 1, figsize=(15, 10))

        # 显示缠论主图
        ax.imshow(img)
        ax.axis('off')  # 隐藏坐标轴

        # 获取配置信息
        config_name = config_result['config_name']
        upper_price = config_result['upper']
        lower_price = config_result['lower']
        mid_price = config_result['mid']
        volume_info = config_result['volume_info']
        duration = config_result.get('duration', 0)

        # 设置标题
        title = f"{stock_code} {stock_name} - {level_name}级别\n配置: {config_name}\n上沿: {upper_price:.2f}, 下沿: {lower_price:.2f}, 持续: {duration}根K线"
        ax.set_title(title, fontsize=12, fontweight='bold', pad=20)

        # 添加关键信息文本框
        info_text = f"""配置详情:
? 配置名称: {config_name}
? 中枢上沿: {upper_price:.2f}
? 中枢下沿: {lower_price:.2f}
? 中枢中值: {mid_price:.2f}
? 持续周期: {duration}根K线
? 中枢起止: {volume_info.get('start_time', 'N/A')} 到 {volume_info.get('end_time', 'N/A')}
? 中枢内最大成交量: {volume_info.get('max_volume', 0):.0f}"""

        ax.text(0.02, 0.98, info_text, transform=ax.transAxes, fontsize=9,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))

        # 调整布局
        plt.tight_layout()

        # 直接覆盖原图表文件
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')

        # 关闭图表以释放内存
        plt.close()

        logger = logging.getLogger()
        logger.debug(f"配置信息已添加到图表: {os.path.basename(chart_path)}")

    except Exception as e:
        logger = logging.getLogger()
        logger.error(f"添加配置信息失败: {e}")


def generate_debug_chart(stock_code: str, stock_name: str, df_raw: pd.DataFrame,
                        upper_price: float, volume_info: dict,
                        max_high_in_range: float, max_high_date: str,
                        max_volume_in_recent15: float, max_volume_in_recent15_date: str,
                        k_type: KL_TYPE, result: dict = None, best_chan_obj = None):
    """
    生成调试模式的图表，使用缠论模块的作图功能

    参数:
        stock_code: 股票代码
        stock_name: 股票名称
        df_raw: 原始K线数据
        upper_price: 中枢上沿价格
        volume_info: 中枢成交量信息
        max_high_in_range: 中枢过后至今最高价
        max_high_date: 最高价日期
        max_volume_in_recent15: 最近15天最大成交量
        max_volume_in_recent15_date: 最近15天最大成交量日期
        k_type: K线级别
        result: 3买分析结果
        best_chan_obj: 最佳缠论对象
    """
    try:
        # 导入缠论绘图模块
        from Plot.PlotDriver import CPlotDriver

        # 创建charts目录
        charts_dir = os.path.join(os.path.dirname(__file__), 'charts')
        if not os.path.exists(charts_dir):
            os.makedirs(charts_dir)

        # 确定级别名称
        level_name = "日线" if k_type == KL_TYPE.K_DAY else "30分钟"

        # 设置图表文件名
        chart_filename = f"{stock_code}_{level_name}_3buy_analysis.png"
        chart_path = os.path.join(charts_dir, chart_filename)

        # 如果文件已存在，先删除以确保能够覆盖
        if os.path.exists(chart_path):
            try:
                os.remove(chart_path)
                logger = logging.getLogger()
                logger.debug(f"删除旧图表文件: {chart_filename}")
            except Exception as e:
                logger = logging.getLogger()
                logger.warning(f"删除旧图表文件失败: {e}")

        if best_chan_obj is None:
            logger = logging.getLogger()
            logger.error("缠论对象为空，无法生成图表")
            return

        # 使用缠论模块生成主图
        plot_driver = CPlotDriver(
            best_chan_obj,
            plot_config={
                "plot_kline": True,
                "plot_bi": True,
                "plot_seg": True,
                "plot_zs": True,
                "plot_bs_point": True,
                "plot_macd": False,
            },
            plot_para={
                "figure": {
                    "x_range": 0,
                }
            }
        )

        # 生成缠论主图
        plot_driver.save2img(chart_path)

        logger = logging.getLogger()
        logger.info(f"缠论主图已保存到: {chart_path}")

        # 在缠论主图基础上添加额外标记（不再生成增强版）
        add_debug_info_to_chart(chart_path, df_raw, upper_price, volume_info,
                               max_high_in_range, max_high_date,
                               max_volume_in_recent15, max_volume_in_recent15_date,
                               stock_code, stock_name, level_name, result)

    except Exception as e:
        logger = logging.getLogger()
        logger.error(f"生成图表失败: {e}")


def add_debug_info_to_chart(chart_path: str, df_raw: pd.DataFrame,
                           upper_price: float, volume_info: dict,
                           max_high_in_range: float, max_high_date: str,
                           max_volume_in_recent15: float, max_volume_in_recent15_date: str,
                           stock_code: str, stock_name: str, level_name: str, result: dict = None):
    """
    在调试图表上添加信息（简化版，不生成增强图表）
    """
    try:
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False

        # 读取缠论生成的图片
        import matplotlib.image as mpimg
        img = mpimg.imread(chart_path)

        # 创建新的图表，只显示缠论主图和调试信息
        fig, ax = plt.subplots(1, 1, figsize=(15, 10))

        # 显示缠论主图
        ax.imshow(img)
        ax.axis('off')  # 隐藏坐标轴

        # 设置标题
        title = f"{stock_code} {stock_name} - {level_name}级别 3买分析"
        if result:
            title += " ? 符合3买条件" if result else " ? 不符合3买条件"
        ax.set_title(title, fontsize=14, fontweight='bold', pad=20)

        # 简化版：只添加基本信息，不绘制成交量图

        # 添加关键信息文本框
        # 根据中枢位置决定成交量描述文字
        zs_end_time = None
        if volume_info and volume_info.get('end_time'):
            try:
                end_time_str = str(volume_info['end_time'])
                if hasattr(volume_info['end_time'], 'to_str'):
                    end_time_str = volume_info['end_time'].to_str()
                if '/' in end_time_str:
                    zs_end_time = pd.to_datetime(end_time_str.replace('/', '-'))
                else:
                    zs_end_time = pd.to_datetime(end_time_str)
            except:
                zs_end_time = None

        recent_15_days = datetime.now() - timedelta(days=15)
        if zs_end_time and zs_end_time >= recent_15_days:
            volume_desc = f"? 中枢结束后最大成交量: {max_volume_in_recent15:.0f}"
        else:
            volume_desc = f"? 最近15天最大成交量: {max_volume_in_recent15:.0f}"

        info_text = f"""关键信息:
? 中枢上沿: {upper_price:.2f}
? 中枢起止: {volume_info.get('start_time', 'N/A')} 到 {volume_info.get('end_time', 'N/A')}
? 中枢内最大成交量: {volume_info.get('max_volume', 0):.0f}
{volume_desc}
? 区间最高价: {max_high_in_range:.2f}"""

        ax.text(0.02, 0.98, info_text, transform=ax.transAxes, fontsize=10,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        # 调整布局
        plt.tight_layout()

        # 直接覆盖原图表文件
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')

        # 关闭图表以释放内存
        plt.close()

        logger = logging.getLogger()
        logger.debug(f"调试信息已添加到图表: {os.path.basename(chart_path)}")

    except Exception as e:
        logger = logging.getLogger()
        logger.error(f"添加调试信息失败: {e}")


def debug_single_stock(stock_code: str, k_types: List[KL_TYPE] = None):
    """
    调试模式：详细分析单个股票的3买条件

    参数:
        stock_code: 6位股票代码
        k_types: 要分析的K线级别列表
    """
    # 设置日志系统
    logger = setup_logging()

    if k_types is None:
        k_types = [KL_TYPE.K_DAY, KL_TYPE.K_30M]

    print("=" * 50)
    print("缠论3买点检测程序 - 调试模式")
    print(f"分析股票: {stock_code}")

    logger.info("=" * 80)
    logger.info("缠论3买点检测程序 - 调试模式")
    logger.info(f"分析股票: {stock_code}")

    # 显示分析级别
    level_names = {
        KL_TYPE.K_DAY: "日线",
        KL_TYPE.K_30M: "30分钟"
    }
    selected_levels = [level_names.get(k, str(k)) for k in k_types]
    print(f"分析级别: {', '.join(selected_levels)}")
    print("=" * 50)

    logger.info(f"分析级别: {', '.join(selected_levels)}")
    logger.info("=" * 80)

    # 检查缠论模块是否可用
    if not CHAN_AVAILABLE:
        logger.error("缠论模块不可用，无法执行分析。请确保libs/chan目录完整且可用。")
        return

    # 连接QMT
    logger.info("\n1. 连接QMT...")
    if not connect_miniqmt():
        logger.error("QMT连接失败，请检查QMT客户端是否运行")
        return
    logger.info("QMT连接成功")

    # 清理股票代码
    cleaned_code = clean_stock_code(stock_code)
    if len(cleaned_code) != 6 or not cleaned_code.isdigit():
        logger.error(f"无效的股票代码: {stock_code} -> {cleaned_code}")
        return

    logger.info(f"\n2. 开始详细分析股票: {cleaned_code}")
    logger.info("=" * 60)

    results = {}
    
    # 根据选择的级别进行分析
    if KL_TYPE.K_DAY in k_types:
        print("\n正在分析日线级别...")
        logger.info(f"\n【日线级别详细分析】")
        logger.info("=" * 40)
        day_result = check_3buy_conditions_debug(cleaned_code, f"股票{cleaned_code}", KL_TYPE.K_DAY)
        results['day'] = day_result
        print("日线级别分析完成")

    if KL_TYPE.K_30M in k_types:
        print("\n正在分析30分钟级别...")
        logger.info(f"\n【30分钟级别详细分析】")
        logger.info("=" * 40)
        min30_result = check_3buy_conditions_debug(cleaned_code, f"股票{cleaned_code}", KL_TYPE.K_30M)
        results['30m'] = min30_result
        print("30分钟级别分析完成")

    # 综合判断
    logger.info(f"\n【综合判断结果】")
    logger.info("=" * 40)

    qualified_levels = []
    for level_key, result in results.items():
        level_name = "日线级别" if level_key == 'day' else "30分钟级别"
        is_qualified = result is not None
        status = "? 符合3买条件" if is_qualified else "? 不符合3买条件"
        logger.info(f"{level_name}: {status}")
        if is_qualified:
            qualified_levels.append(level_name)

    overall_qualified = len(qualified_levels) > 0
    
    logger.info(f"")
    if overall_qualified:
        logger.info(f"最终结论: ? 该股票符合3买要求")
        logger.info(f"符合条件的级别: {', '.join(qualified_levels)}")
        logger.info(f"\n推荐操作: 可考虑买入")
    else:
        logger.info(f"最终结论: ? 该股票不符合3买要求")
        logger.info(f"所有分析级别都不满足条件")
        logger.info(f"\n推荐操作: 暂不建议买入")

    logger.info(f"\n调试分析完成!")
    logger.info(f"详细日志已保存到: {LOG_FILE}")


def calculate_profit_loss_ratio(latest_zs, current_price: float, best_chan_obj, logger) -> Tuple[float, Optional[dict]]:
    """
    计算盈亏比

    参数:
        latest_zs: 最新中枢对象
        current_price: 当前价格
        best_chan_obj: 最佳缠论对象
        logger: 日志对象

    返回:
        (盈亏比, 详细信息字典) 或 (错误信息, None)
    """
    try:
        # 获取中枢的上沿和下沿
        zs_upper = latest_zs.high
        zs_lower = latest_zs.low

        # 查找入笔的起点价格
        entry_price = None

        # 方法1: 从中枢对象获取入笔信息
        if hasattr(latest_zs, 'bi_in') and latest_zs.bi_in:
            # 获取入笔的起始价格
            if hasattr(latest_zs.bi_in, 'begin') and hasattr(latest_zs.bi_in.begin, 'val'):
                entry_price = latest_zs.bi_in.begin.val
                logger.info(f"  从中枢入笔获取起点价格: {entry_price:.2f}")
            elif hasattr(latest_zs.bi_in, 'begin') and hasattr(latest_zs.bi_in.begin, 'low'):
                entry_price = latest_zs.bi_in.begin.low
                logger.info(f"  从中枢入笔begin.low获取起点价格: {entry_price:.2f}")
            elif hasattr(latest_zs.bi_in, 'begin') and hasattr(latest_zs.bi_in.begin, 'high'):
                entry_price = latest_zs.bi_in.begin.high
                logger.info(f"  从中枢入笔begin.high获取起点价格: {entry_price:.2f}")

        # 方法2: 如果没有入笔信息，从缠论对象的笔列表中查找
        if entry_price is None and best_chan_obj:
            try:
                kl_data = best_chan_obj[0]
                if hasattr(kl_data, 'bi_list') and kl_data.bi_list:
                    bi_list = kl_data.bi_list.lst

                    # 查找中枢开始前的最后一笔
                    zs_start_idx = latest_zs.begin.idx if hasattr(latest_zs, 'begin') and latest_zs.begin else 0

                    for bi in reversed(bi_list):
                        if hasattr(bi, 'end') and hasattr(bi.end, 'idx') and bi.end.idx <= zs_start_idx:
                            # 找到中枢开始前的笔，取其起始价格
                            if hasattr(bi, 'begin') and hasattr(bi.begin, 'val'):
                                entry_price = bi.begin.val
                                logger.info(f"  从笔列表获取入笔起点价格: {entry_price:.2f}")
                                break
                            elif hasattr(bi, 'begin') and hasattr(bi.begin, 'low'):
                                entry_price = bi.begin.low
                                logger.info(f"  从笔列表begin.low获取入笔起点价格: {entry_price:.2f}")
                                break
                            elif hasattr(bi, 'begin') and hasattr(bi.begin, 'high'):
                                entry_price = bi.begin.high
                                logger.info(f"  从笔列表begin.high获取入笔起点价格: {entry_price:.2f}")
                                break
            except Exception as e:
                logger.info(f"  从笔列表获取入笔起点失败: {e}")

        # 方法3: 如果还是没有，使用中枢开始位置的价格
        if entry_price is None and best_chan_obj:
            try:
                kl_data = best_chan_obj[0]
                zs_start_idx = latest_zs.begin.idx if hasattr(latest_zs, 'begin') and latest_zs.begin else 0

                if zs_start_idx > 0 and zs_start_idx < len(kl_data.lst):
                    start_kline = kl_data.lst[zs_start_idx]
                    if hasattr(start_kline, 'lst') and start_kline.lst:
                        start_klu = start_kline.lst[0]  # 取第一个K线单元
                        entry_price = start_klu.low  # 使用最低价作为入笔起点
                        logger.info(f"  从中枢开始位置获取入笔起点价格: {entry_price:.2f}")
            except Exception as e:
                logger.info(f"  从中枢开始位置获取入笔起点失败: {e}")

        if entry_price is None:
            return "无法找到入笔起点价格", None

        # 计算盈亏比公式中的各个要素
        a = abs(entry_price - zs_lower)  # 入笔起点与中枢下沿价格的差的绝对值
        b = current_price - zs_lower     # 现价与中枢下沿的差值
        c = current_price - zs_upper     # 现价与中枢上沿的差值

        # 检查分母是否为0
        if b == 0:
            return "分母为0，无法计算盈亏比", None

        # 计算盈亏比
        profit_loss_ratio = (a - c) / b

        # 构建详细信息
        ratio_details = {
            'entry_price': entry_price,
            'zs_lower': zs_lower,
            'zs_upper': zs_upper,
            'current_price': current_price,
            'a': a,
            'b': b,
            'c': c
        }

        return profit_loss_ratio, ratio_details

    except Exception as e:
        logger.error(f"计算盈亏比时发生错误: {e}")
        return f"计算错误: {e}", None


def get_current_price(stock_code: str) -> Optional[float]:
    """
    获取股票的当前价格（实时价格）
    重构版本：直接从QMT下载最新日线数据，获取最新收盘价

    参数:
        stock_code: 6位股票代码

    返回:
        当前价格，如果获取失败返回None
    """
    # 获取logger
    logger = logging.getLogger()

    try:
        # 直接从QMT下载最新日线数据并获取最新收盘价
        try:
            from Zstock_tech_qmttdx import download_miniqmt_data
            from datetime import datetime, timedelta

            # 格式化股票代码
            if stock_code.startswith('6'):
                formatted_code = f"{stock_code}.SH"
            elif stock_code.startswith(('0', '3')):
                formatted_code = f"{stock_code}.SZ"
            else:
                formatted_code = f"{stock_code}.SH"  # 默认上海

            # 下载最新的日线数据
            today = datetime.now().strftime('%Y%m%d')
            yesterday = (datetime.now() - timedelta(days=3)).strftime('%Y%m%d')  # 多下载几天确保有数据

            logger.info(f"  下载最新日线数据...")
            download_miniqmt_data(formatted_code, '1d', yesterday, today)
            logger.info(f"  ? 日线数据下载完成")

            # 获取最新的日线数据
            df_daily = get_miniqmt_data(stock_code, period='1d', count=5)
            if not df_daily.empty and 'error' not in df_daily.columns:
                # 使用中文列名访问数据
                latest_close = df_daily.iloc[-1]['收盘']
                latest_time = df_daily.iloc[-1]['日期']
                logger.info(f"  ? 从日线K线获取最新收盘价: {latest_close:.2f} (日期: {latest_time})")
                return float(latest_close)
            else:
                logger.info(f"  日线数据获取失败或为空")

        except Exception as e:
            logger.info(f"  QMT日线数据获取失败: {e}")

        # 如果QMT日线数据获取失败，返回None
        logger.warning(f"  ? 无法获取当前价格，将使用历史K线最后价格")
        return None

    except Exception as e:
        logger.error(f"  ? 获取当前价格出错: {e}")
        return None


def check_3buy_conditions_debug(stock_code: str, stock_name: str, k_type: KL_TYPE = KL_TYPE.K_DAY) -> Optional[dict]:
    """
    检查单只股票的3买条件（调试版本，输出更详细的信息）

    参数:
        stock_code: 股票代码
        stock_name: 股票名称
        k_type: K线级别

    返回:
        如果符合3买条件，返回包含详细信息的字典，否则返回None
    """
    # 获取logger
    logger = logging.getLogger()

    try:
        # 从QMT获取数据
        period_map = {
            KL_TYPE.K_DAY: '1d',
            KL_TYPE.K_WEEK: '1w',
            KL_TYPE.K_60M: '60m',
            KL_TYPE.K_30M: '30m',
            KL_TYPE.K_15M: '15m',
            KL_TYPE.K_5M: '5m',
            KL_TYPE.K_1M: '1m'
        }

        period = period_map.get(k_type, '1d')

        # 首先下载最新数据
        try:
            from Zstock_tech_qmttdx import download_all_data_for_stock
            download_all_data_for_stock(stock_code)
        except Exception:
            pass

        # 直接使用xtdata.get_market_data获取数据，参考Zstock_tech_qmttdx_bak.py
        formatted_code = stock_code
        if len(stock_code) == 6:
            if stock_code.startswith(('60', '68', '90')):
                formatted_code = stock_code + '.SH'
            elif stock_code.startswith(('00', '30', '12', '20')):
                formatted_code = stock_code + '.SZ'

        # 使用get_miniqmt_data获取数据，增加数据量确保包含完整中枢
        df = get_miniqmt_data(stock_code, period=period, count=1000)

        if df.empty or 'error' in df.columns:
            return None

        # 获取当前实时价格
        current_price = get_current_price(stock_code)

        # 创建原始数据副本用于图表生成
        df_raw = df.copy()

        # 统一列名映射
        column_mapping = {
            '日期': 'time',
            '时间': 'time',
            'date': 'time',
            '开盘': 'open',
            '最高': 'high',
            '最低': 'low',
            '收盘': 'close',
            '成交量': 'volume',
            '成交额': 'amount'
        }

        # 重命名列
        df_renamed = df.rename(columns=column_mapping)

        # 转换数据格式
        csv_lines = []
        seen_timestamps = set()  # 用于检测重复时间戳

        for i, (_, row) in enumerate(df_renamed.iterrows()):
            time_str = str(row['time'])

            # 对于30分钟数据，需要保留完整的时间信息
            if k_type == KL_TYPE.K_30M:
                # 30分钟数据需要包含时间信息
                if '-' in time_str and ' ' in time_str:
                    # 格式: 2024-01-01 09:30:00
                    datetime_part = time_str.split('.')[0]  # 去掉毫秒部分
                    # 缠论库期望的格式是 YYYY/MM/DD HH:MM:SS
                    try:
                        dt = datetime.strptime(datetime_part, '%Y-%m-%d %H:%M:%S')
                        date_part = dt.strftime('%Y/%m/%d %H:%M:%S')
                    except:
                        # 如果解析失败，使用原始格式
                        date_part = datetime_part.replace('-', '/').replace('T', ' ')
                elif '-' in time_str:
                    # 只有日期，这不应该发生在30分钟数据中
                    date_part = time_str.split(' ')[0].replace('-', '/') + ' 09:30:00'
                else:
                    # 格式: 20240101，这也不应该发生在30分钟数据中
                    date_part = f"{time_str[:4]}/{time_str[4:6]}/{time_str[6:8]} 09:30:00"
            else:
                # 日线数据只需要日期
                if '-' in time_str:
                    date_part = time_str.split(' ')[0].replace('-', '/')
                else:
                    date_part = f"{time_str[:4]}/{time_str[4:6]}/{time_str[6:8]}"

            # 检测重复时间戳
            if date_part in seen_timestamps:
                continue
            seen_timestamps.add(date_part)

            # 获取成交量和成交额数据
            volume = row.get('volume', 0)
            amount = row.get('amount', 0)
            turnover_rate = row.get('turnover_rate', 0)

            # 确保数据不为空
            if volume is None or pd.isna(volume):
                volume = 0
            if amount is None or pd.isna(amount):
                amount = 0
            if turnover_rate is None or pd.isna(turnover_rate):
                turnover_rate = 0

            # 现在缠论库支持8列数据：时间+OHLC+成交量+成交额+换手率
            line = f"{date_part},{row['open']},{row['high']},{row['low']},{row['close']},{volume},{amount},{turnover_rate}"
            csv_lines.append(line)

        # 调试：检查时间戳的唯一性
        if k_type == KL_TYPE.K_30M and len(csv_lines) > 0:
            timestamps = [line.split(',')[0] for line in csv_lines]
            unique_timestamps = set(timestamps)
            if len(timestamps) != len(unique_timestamps):
                logger.error(f"  ? 发现重复时间戳！总数={len(timestamps)}, 唯一数={len(unique_timestamps)}")
                # 显示前几个和后几个时间戳
                logger.info(f"  前5个时间戳: {timestamps[:5]}")
                logger.info(f"  后5个时间戳: {timestamps[-5:]}")
                # 找出重复的时间戳
                from collections import Counter
                timestamp_counts = Counter(timestamps)
                duplicates = [ts for ts, count in timestamp_counts.items() if count > 1]
                logger.info(f"  重复的时间戳: {duplicates[:5]}")  # 只显示前5个
            else:
                logger.info(f"  ? 时间戳检查通过，无重复")

        # 创建临时CSV文件供缠论使用
        chan_dir = os.path.join(os.path.dirname(__file__), 'libs', 'chan')
        if not os.path.exists(chan_dir):
            chan_dir = os.path.dirname(__file__)  # 如果chan目录不存在，使用当前目录

        # 使用chan库期望的文件命名格式
        if CHAN_AVAILABLE:
            period_suffix = {
                KL_TYPE.K_DAY: 'day',
                KL_TYPE.K_WEEK: 'week',
                KL_TYPE.K_30M: '30m'
            }.get(k_type, 'day')
        else:
            period_suffix = period.replace('d', 'day').replace('w', 'week').replace('m', 'm')

        temp_csv_file = os.path.join(chan_dir, f"{stock_code}_{period_suffix}.csv")

        # 写入临时CSV文件
        with open(temp_csv_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(csv_lines))

        # 使用配置排列组合获取最大的中枢上沿（调试模式，生成所有配置图表）
        upper_price, latest_zs, _, best_config, volume_info, temp_csv_file_from_config, best_chan_obj = get_latest_zs_upper_price_with_configs(
            stock_code, csv_lines, k_type, debug_mode=True, stock_name=stock_name, df_raw=df_raw
        )

        if upper_price is None:
            logger.error("上沿价格获取失败，程序返回")
            return None

        # 使用最佳配置的缠论对象（避免重新创建导致的不一致）
        try:
            if best_chan_obj is None:
                logger.error("最佳缠论对象为空，程序返回")
                return None

            chan = best_chan_obj

            # 获取K线数据
            kl_data = chan[0]
            if not kl_data or len(kl_data.lst) == 0:
                logger.error("K线数据为空，程序返回")
                return None

            # 获取最新K线（CKLine对象包含多个K线单元）
            latest_kline = kl_data.lst[-1]

            # CKLine对象包含多个K线单元，获取最后一个
            if hasattr(latest_kline, 'lst') and latest_kline.lst:
                latest_klu = latest_kline.lst[-1]  # 获取最后一个K线单元
                latest_close = latest_klu.close
                latest_time = latest_klu.time
            else:
                return None

            # 确定用于判断的价格
            if current_price is not None:
                # 使用实时价格
                price_for_judgment = current_price
            else:
                # 使用历史K线最后价格
                price_for_judgment = latest_close

            # 判断中枢是否已确定
            zs_status = "已确定" if latest_zs.is_sure else "未确定"

            # 检查是否有出中枢的笔
            if hasattr(latest_zs, 'bi_out') and latest_zs.bi_out:
                logger.info(f"  该中枢有出笔，索引为: {latest_zs.bi_out.idx}")
            else:
                logger.info(f"  该中枢尚未有笔离开")

            # 条件a: 最新价格位于中枢上沿之上
            condition_a = price_for_judgment > upper_price

            # 条件b: 最新价格不超过中枢上沿的115%
            upper_limit = upper_price * 1.15
            condition_b = price_for_judgment <= upper_limit

            # 条件c: 使用原始K线数据检查最高价，避免缠论库数据缺失问题
            condition_c = True
            max_high_in_range = 0
            upper_125_limit = upper_price * 1.25
            max_high_date = None

            # 获取原始K线数据来检查最高价
            try:
                formatted_code = f"{stock_code}.SH" if stock_code.startswith('6') else f"{stock_code}.SZ"

                kline_data_raw = xtdata.get_market_data(
                    field_list=['time', 'open', 'high', 'low', 'close', 'volume', 'amount'],
                    stock_list=[formatted_code],
                    period='1d',
                    count=100,
                    dividend_type='front',
                    fill_data=True
                )

                if kline_data_raw and 'time' in kline_data_raw:
                    dates = pd.to_datetime(kline_data_raw.get('time').loc[formatted_code], unit='ms', utc=True).dt.tz_convert('Asia/Shanghai').dt.tz_localize(None)
                    df_raw = pd.DataFrame({'日期': dates})

                    for field in ['open', 'high', 'low', 'close', 'volume', 'amount']:
                        if field in kline_data_raw:
                            field_data = kline_data_raw[field]
                            if isinstance(field_data, pd.DataFrame) and formatted_code in field_data.index:
                                df_raw[field] = field_data.loc[formatted_code].values

                    # 找到最近一个收盘价小于中枢上沿的位置
                    last_below_upper_found = False
                    for i in range(len(df_raw) - 1, -1, -1):
                        row = df_raw.iloc[i]
                        if row['close'] < upper_price:
                            last_below_upper_found = True
                            # 从这个位置开始检查最高价
                            for j in range(i, len(df_raw)):
                                check_row = df_raw.iloc[j]
                                date_str = str(check_row['日期'])
                                high_price = check_row['high']
                                if high_price > max_high_in_range:
                                    max_high_in_range = high_price
                                    max_high_date = date_str
                            break

            except Exception:
                pass

            condition_c = max_high_in_range < upper_125_limit if max_high_in_range > 0 else True

            # 条件d: 最近15个交易日至少有1个交易日的成交量高过最近一个中枢范围内成交量最大的那一天
            condition_d = True
            max_volume_in_zs = 0
            max_volume_in_recent15 = 0

            # 获取中枢范围内的成交量最大值
            zs_start_idx = latest_zs.begin.idx if hasattr(latest_zs, 'begin') and latest_zs.begin else 0
            zs_end_idx = latest_zs.end.idx if hasattr(latest_zs, 'end') and latest_zs.end else len(kl_data.lst) - 1

            # 特殊处理：如果中枢有出笔且未确定，成交量计算范围应该到出笔开始前
            if hasattr(latest_zs, 'bi_out') and latest_zs.bi_out and not latest_zs.is_sure:
                # 出笔开始的索引就是出笔的起始位置，成交量计算应该到这之前
                bi_out_start_idx = latest_zs.bi_out.idx
                # 成交量计算范围：从中枢起点到出笔开始前
                zs_end_idx = min(zs_end_idx, bi_out_start_idx - 1)


            # 确保索引在有效范围内
            zs_start_idx = max(0, min(zs_start_idx, len(kl_data.lst) - 1))
            zs_end_idx = max(0, min(zs_end_idx, len(kl_data.lst) - 1))

            # 计算中枢范围内的最大成交量 - 使用原始数据修正缠论库的数据缺失
            max_volume_in_zs_date = None

            # 先用缠论库数据计算
            for i in range(zs_start_idx, zs_end_idx + 1):
                kline = kl_data.lst[i]
                if hasattr(kline, 'lst') and kline.lst:
                    klu = kline.lst[-1]  # 获取K线单元
                    # 从 trade_info.metric 中获取成交量
                    volume = klu.trade_info.metric.get('volume', 0)
                    if volume and volume > 0:
                        if volume > max_volume_in_zs:
                            max_volume_in_zs = volume
                            max_volume_in_zs_date = klu.time

            # 使用原始数据进行修正检查
            try:
                if 'df_raw' in locals():
                    # 使用正确的中枢时间范围（从volume_info获取，而不是从调整后的索引重新计算）
                    zs_start_time = volume_info['start_time'] if volume_info else None
                    zs_end_time = volume_info['end_time'] if volume_info else None

                    # 如果volume_info中没有时间信息，才从原始中枢索引获取（使用未调整的索引）
                    if not zs_start_time or not zs_end_time:
                        original_zs_start_idx = latest_zs.begin.idx if hasattr(latest_zs, 'begin') and latest_zs.begin else 0
                        original_zs_end_idx = latest_zs.end.idx if hasattr(latest_zs, 'end') and latest_zs.end else len(kl_data.lst) - 1

                        if not zs_start_time and original_zs_start_idx < len(kl_data.lst):
                            start_kline = kl_data.lst[original_zs_start_idx]
                            if hasattr(start_kline, 'lst') and start_kline.lst:
                                zs_start_time = start_kline.lst[-1].time

                        if not zs_end_time and original_zs_end_idx < len(kl_data.lst):
                            end_kline = kl_data.lst[original_zs_end_idx]
                            if hasattr(end_kline, 'lst') and end_kline.lst:
                                zs_end_time = end_kline.lst[-1].time

                    # 在原始数据中查找这个时间范围内的最大成交量
                    for _, row in df_raw.iterrows():
                        date_str = str(row['日期'])
                        volume = row.get('volume', 0)

                        # 精确的日期范围检查：只处理中枢时间范围内的数据
                        if volume and volume > 0:
                            # 使用pandas的datetime进行精确比较，避免字符串比较的问题
                            try:
                                row_datetime = pd.to_datetime(row['日期'])

                                # 转换中枢起止时间为datetime对象
                                if zs_start_time and zs_end_time:
                                    # 处理CTime对象
                                    if hasattr(zs_start_time, 'to_str'):
                                        start_str = zs_start_time.to_str().replace('/', '-')
                                        zs_start_dt = pd.to_datetime(start_str)
                                    else:
                                        zs_start_dt = pd.to_datetime(str(zs_start_time))

                                    if hasattr(zs_end_time, 'to_str'):
                                        end_str = zs_end_time.to_str().replace('/', '-')
                                        zs_end_dt = pd.to_datetime(end_str)
                                    else:
                                        zs_end_dt = pd.to_datetime(str(zs_end_time))

                                    # 精确的日期范围检查
                                    is_in_range = zs_start_dt <= row_datetime <= zs_end_dt
                                else:
                                    is_in_range = False

                                if is_in_range:
                                    # 如果这个成交量比当前记录的中枢内最大成交量还大，更新它
                                    if volume > max_volume_in_zs:
                                        max_volume_in_zs = volume
                                        max_volume_in_zs_date = date_str
                            except Exception:
                                # 如果日期转换失败，跳过这条记录
                                continue

            except Exception:
                pass

            # 计算最近15个交易日的最大成交量（但不得进入中枢范围）- 使用原始数据避免缺失
            max_volume_in_recent15_date = None

            try:
                # 获取中枢结束时间
                zs_end_time = None
                if volume_info and volume_info.get('end_time'):
                    try:
                        end_time_str = str(volume_info['end_time'])
                        if hasattr(volume_info['end_time'], 'to_str'):
                            end_time_str = volume_info['end_time'].to_str()

                        # 尝试解析中枢结束时间
                        if '/' in end_time_str:
                            # 格式: 2025/07/11
                            zs_end_time = pd.to_datetime(end_time_str.replace('/', '-'))
                        else:
                            zs_end_time = pd.to_datetime(end_time_str)
                    except:
                        zs_end_time = None

                # 重新获取原始数据来检查最近15日成交量
                if 'df_raw' in locals():
                    # 确定考察起始时间：如果中枢在最近15日内结束，则从中枢结束后开始考察
                    recent_15_days = datetime.now() - timedelta(days=15)

                    if zs_end_time and zs_end_time >= recent_15_days:
                        # 中枢在最近15日内结束，只考察中枢结束后的数据
                        start_time = zs_end_time + timedelta(days=1)  # 中枢结束后的第一天
                        recent_data = df_raw[df_raw['日期'] >= start_time]
                        logger.info(f"中枢在最近15日内结束({zs_end_time.strftime('%Y-%m-%d')})，只考察中枢结束后的成交量")
                    else:
                        # 中枢不在最近15日内，考察整个最近15日
                        recent_data = df_raw.tail(15)
                        logger.info(f"中枢不在最近15日内，考察最近15日成交量")

                    # 使用筛选后的数据
                    for i, row in recent_data.iterrows():
                        date_str = str(row['日期'])
                        volume = row.get('volume', 0)
                        if volume and volume > max_volume_in_recent15:
                            max_volume_in_recent15 = volume
                            max_volume_in_recent15_date = date_str
                else:
                    # 如果没有原始数据，回退到缠论库数据
                    zs_end_idx = latest_zs.end.idx if hasattr(latest_zs, 'end') and latest_zs.end else len(kl_data.lst) - 1
                    recent_start_idx = max(0, len(kl_data.lst) - 15)

                    if zs_end_idx >= recent_start_idx:
                        # 中枢在最近15个交易日内结束，只考察中枢结束后的数据
                        start_idx = zs_end_idx + 1
                        logger.info(f"中枢在最近15个交易日内结束，只考察中枢结束后的成交量")
                    else:
                        # 中枢不在最近15个交易日内，考察整个最近15个交易日
                        start_idx = recent_start_idx
                        logger.info(f"中枢不在最近15个交易日内，考察最近15个交易日成交量")

                    for i in range(start_idx, len(kl_data.lst)):
                        kline = kl_data.lst[i]
                        if hasattr(kline, 'lst') and kline.lst:
                            klu = kline.lst[-1]  # 获取K线单元
                            volume = klu.trade_info.metric.get('volume', 0)
                            if volume and volume > 0:
                                if volume > max_volume_in_recent15:
                                    max_volume_in_recent15 = volume
                                    max_volume_in_recent15_date = klu.time
            except Exception:
                pass

            # 原有条件：最近15日最大成交量 >= 中枢内最大成交量
            condition_d_daily = max_volume_in_recent15 >= max_volume_in_zs

            # 新增条件：倒数第二根周K线成交量 > 中枢范围内最高周K线成交量
            condition_d_weekly = False
            try:
                # 获取周K线数据
                weekly_df = get_weekly_data_for_3buy(stock_code, count=200)
                if not weekly_df.empty and len(weekly_df) >= 2:
                    # 获取倒数第二根周K线的成交量
                    second_last_weekly_volume = weekly_df.iloc[-2]['成交量']

                    # 获取中枢起止时间
                    zs_start_time = volume_info.get('start_time')
                    zs_end_time = volume_info.get('end_time')

                    if zs_start_time and zs_end_time:
                        # 转换时间格式
                        if hasattr(zs_start_time, 'to_str'):
                            zs_start_str = zs_start_time.to_str()
                        else:
                            zs_start_str = str(zs_start_time)

                        if hasattr(zs_end_time, 'to_str'):
                            zs_end_str = zs_end_time.to_str()
                        else:
                            zs_end_str = str(zs_end_time)

                        # 处理日期格式（可能包含'/'）
                        if '/' in zs_start_str:
                            zs_start_str = zs_start_str.replace('/', '-')
                        if '/' in zs_end_str:
                            zs_end_str = zs_end_str.replace('/', '-')

                        zs_start_date = pd.to_datetime(zs_start_str)
                        zs_end_date = pd.to_datetime(zs_end_str)

                        # 找到中枢范围内的周K线数据
                        zs_weekly_data = weekly_df[
                            (weekly_df['日期'] >= zs_start_date) &
                            (weekly_df['日期'] <= zs_end_date)
                        ]

                        if not zs_weekly_data.empty:
                            # 获取中枢范围内最大周成交量
                            max_weekly_volume_in_zs = zs_weekly_data['成交量'].max()

                            # 判断条件
                            condition_d_weekly = second_last_weekly_volume > max_weekly_volume_in_zs

                            logger.info(f"周K线成交量判断:")
                            logger.info(f"  倒数第二根周K线成交量: {second_last_weekly_volume:.0f}")
                            logger.info(f"  中枢范围内最大周成交量: {max_weekly_volume_in_zs:.0f}")
                            logger.info(f"  周K线条件: {'通过' if condition_d_weekly else '不通过'}")
                        else:
                            logger.info("中枢范围内未找到周K线数据")
                    else:
                        logger.info("无法获取中枢起止时间，跳过周K线判断")
                else:
                    logger.info("周K线数据不足，跳过周K线判断")
            except Exception as e:
                logger.info(f"周K线成交量判断出错: {e}")
                condition_d_weekly = False

            # 条件d：满足任一成交量条件即可
            condition_d = condition_d_daily or condition_d_weekly

            # 输出您要求的7类关键信息
            logger.info(f"最终确定中枢的上沿值: {upper_price:.2f}")
            logger.info(f"最终确定中枢的起止时间: {volume_info['start_time']} 到 {volume_info['end_time']}")

            # 输出最佳配置信息
            if best_config:
                # 如果best_config是字符串（配置名称），直接显示
                if isinstance(best_config, str):
                    logger.info(f"使用的最佳配置: {best_config}")
                # 如果best_config是字典，显示详细信息
                elif isinstance(best_config, dict) and best_config:
                    config_str = f"bi_algo={best_config.get('bi_algo', 'N/A')}, seg_algo={best_config.get('seg_algo', 'N/A')}, zs_algo={best_config.get('zs_algo', 'N/A')}"
                    logger.info(f"使用的最佳配置: {config_str}")
                elif isinstance(best_config, dict) and not best_config:
                    logger.info("使用的最佳配置: 配置字典为空")
                else:
                    logger.info(f"使用的最佳配置: {best_config}")
            else:
                logger.info("使用的最佳配置: 未获取到配置信息")

            # 判断中枢是否完结
            if hasattr(latest_zs, 'bi_out') and latest_zs.bi_out:
                bi_out_start_time = None
                bi_out_end_time = None
                # 获取出笔的起始和终止时间
                if hasattr(latest_zs.bi_out, 'begin') and hasattr(latest_zs.bi_out.begin, 'time'):
                    bi_out_start_time = latest_zs.bi_out.begin.time
                if hasattr(latest_zs.bi_out, 'end') and hasattr(latest_zs.bi_out.end, 'time'):
                    bi_out_end_time = latest_zs.bi_out.end.time
                logger.info(f"最终确定中枢是否完结: {zs_status}，出笔的起始终止时间: {bi_out_start_time} 到 {bi_out_end_time}")
            else:
                logger.info(f"最终确定中枢是否完结: {zs_status}，尚未有笔离开")

            logger.info(f"中枢内成交量最大的日期及成交量: {max_volume_in_zs_date} - {max_volume_in_zs:.0f}")
            # 根据中枢位置决定日志输出文字
            try:
                # 安全的时间比较
                is_recent_zs = False
                if zs_end_time:
                    # 将CTime对象转换为datetime进行比较
                    if hasattr(zs_end_time, 'to_datetime'):
                        zs_end_dt = zs_end_time.to_datetime()
                    elif hasattr(zs_end_time, 'ts'):
                        zs_end_dt = datetime.fromtimestamp(zs_end_time.ts)
                    else:
                        # 如果已经是datetime对象
                        zs_end_dt = zs_end_time

                    is_recent_zs = zs_end_dt >= (datetime.now() - timedelta(days=15))
            except Exception:
                is_recent_zs = False

            if is_recent_zs:
                logger.info(f"中枢结束后最大成交量日期及成交量: {max_volume_in_recent15_date} - {max_volume_in_recent15:.0f}")
            else:
                logger.info(f"最近15天最大的成交量日期及成交量: {max_volume_in_recent15_date} - {max_volume_in_recent15:.0f}")

            # 判断三买过程
            logger.info(f"判断三买过程:")
            logger.info(f"  当前价: {price_for_judgment:.2f}")
            logger.info(f"  中枢上沿: {upper_price:.2f}")
            logger.info(f"  条件a - 收盘价{price_for_judgment:.2f} > 上沿{upper_price:.2f}: {'通过' if condition_a else '不通过'}")
            logger.info(f"  条件b - 收盘价{price_for_judgment:.2f} <= 上沿115%({upper_limit:.2f}): {'通过' if condition_b else '不通过'}")
            logger.info(f"  条件c - 区间最高价{max_high_in_range:.2f} < 上沿125%({upper_125_limit:.2f}): {'通过' if condition_c else '不通过'}")
            logger.info(f"  条件d - 成交量条件（满足任一即可）:")
            logger.info(f"    日线条件: 最近15日最大成交量{max_volume_in_recent15:.0f} >= 中枢内最大成交量{max_volume_in_zs:.0f}: {'通过' if condition_d_daily else '不通过'}")
            logger.info(f"    周线条件: {'通过' if condition_d_weekly else '不通过'}")
            logger.info(f"    条件d总体: {'通过' if condition_d else '不通过'}")

            # 计算盈亏比
            profit_loss_ratio, ratio_details = calculate_profit_loss_ratio(
                latest_zs, price_for_judgment, best_chan_obj, logger
            )

            # 输出盈亏比信息
            logger.info(f"盈亏比计算:")
            if ratio_details:
                logger.info(f"  入笔起点价格: {ratio_details['entry_price']:.2f}")
                logger.info(f"  中枢下沿价格: {ratio_details['zs_lower']:.2f}")
                logger.info(f"  中枢上沿价格: {ratio_details['zs_upper']:.2f}")
                logger.info(f"  当前价格: {ratio_details['current_price']:.2f}")
                logger.info(f"  a = |入笔起点 - 中枢下沿| = |{ratio_details['entry_price']:.2f} - {ratio_details['zs_lower']:.2f}| = {ratio_details['a']:.2f}")
                logger.info(f"  b = 现价 - 中枢下沿 = {ratio_details['current_price']:.2f} - {ratio_details['zs_lower']:.2f} = {ratio_details['b']:.2f}")
                logger.info(f"  c = 现价 - 中枢上沿 = {ratio_details['current_price']:.2f} - {ratio_details['zs_upper']:.2f} = {ratio_details['c']:.2f}")
                logger.info(f"  盈亏比 = (a-c)/b = ({ratio_details['a']:.2f}-{ratio_details['c']:.2f})/{ratio_details['b']:.2f} = {profit_loss_ratio:.4f}")
            else:
                logger.info(f"  无法计算盈亏比: {profit_loss_ratio}")

            # 判断是否符合3买
            is_3buy = condition_a and condition_b and condition_c and condition_d

            # 输出判断结论
            if is_3buy:
                logger.info(f"判断结论: 符合3买条件!")
                result = {
                    'stock_code': stock_code,
                    'stock_name': stock_name,
                    'latest_close': latest_close,  # 历史K线最后价格
                    'current_price': current_price,  # 实时价格
                    'price_for_judgment': price_for_judgment,  # 用于判断的价格
                    'zs_upper': upper_price,
                    'zs_lower': latest_zs.low,
                    'zs_mid': latest_zs.mid,
                    'upper_limit': upper_limit,
                    'upper_125_limit': upper_125_limit,
                    'max_high_in_range': max_high_in_range,
                    'max_volume_in_zs': max_volume_in_zs,
                    'max_volume_in_recent15': max_volume_in_recent15,
                    'latest_time': latest_time,
                    'k_type': k_type,
                    'kline_count': len(csv_lines),  # 添加K线数量
                    'profit_loss_ratio': profit_loss_ratio,  # 添加盈亏比
                    'ratio_details': ratio_details  # 添加盈亏比详细信息
                }

                # 生成调试图表
                try:
                    generate_debug_chart(
                        stock_code=stock_code,
                        stock_name=stock_name,
                        df_raw=df_raw,
                        upper_price=upper_price,
                        volume_info=volume_info,
                        max_high_in_range=max_high_in_range,
                        max_high_date=max_high_date,
                        max_volume_in_recent15=max_volume_in_recent15,
                        max_volume_in_recent15_date=max_volume_in_recent15_date,
                        k_type=k_type,
                        result=result,
                        best_chan_obj=best_chan_obj
                    )
                except Exception as e:
                    logger.error(f"生成图表失败: {e}")

                return result
            else:
                logger.info(f"判断结论: 不符合3买条件")

                # 即使不符合3买条件，也生成图表用于分析
                try:
                    generate_debug_chart(
                        stock_code=stock_code,
                        stock_name=stock_name,
                        df_raw=df_raw,
                        upper_price=upper_price,
                        volume_info=volume_info,
                        max_high_in_range=max_high_in_range,
                        max_high_date=max_high_date,
                        max_volume_in_recent15=max_volume_in_recent15,
                        max_volume_in_recent15_date=max_volume_in_recent15_date,
                        k_type=k_type,
                        result=None,
                        best_chan_obj=best_chan_obj
                    )
                except Exception as e:
                    logger.error(f"生成图表失败: {e}")

                return None

        finally:
            # 清理临时CSV文件（包括配置测试生成的文件）
            try:
                # 清理原始临时文件
                if 'temp_csv_file' in locals() and temp_csv_file and os.path.exists(temp_csv_file):
                    os.remove(temp_csv_file)

                # 清理配置测试生成的临时文件
                if 'temp_csv_file_from_config' in locals() and temp_csv_file_from_config and os.path.exists(temp_csv_file_from_config):
                    os.remove(temp_csv_file_from_config)

            except Exception:
                pass

    except Exception as e:
        logger.error(f"check_3buy_conditions_debug发生异常: {e}")
        import traceback
        logger.error(f"异常堆栈: {traceback.format_exc()}")
        return None


def main():
    """
    主函数
    """
    # 初始化日志系统
    global logger
    logger = setup_logging()

    logger.info("缠论3买点检测程序")
    logger.info("基于QMT数据源和缠论分析")
    logger.info("=" * 50)

    # 选择运行模式
    print("\n请选择运行模式:")
    print("1. 批量分析模式（从XLS文件读取股票列表）")
    print("2. 调试模式（分析单个股票代码）")

    mode = input("请输入模式编号 (1 或 2): ").strip()

    if mode == "2":
        # 调试模式
        print("\n进入调试模式...")

        # 选择分析级别（只需要选择一次）
        print("\n请选择分析级别:")
        print("1. 仅日线级别")
        print("2. 仅30分钟级别")
        print("3. 全部级别（日线+30分钟）")

        level_choice = input("请输入级别编号 (1, 2 或 3): ").strip()
        print(f"选择的级别: {level_choice}")

        if level_choice == "1":
            k_types = [KL_TYPE.K_DAY]
            logger.info("将仅分析日线级别")
        elif level_choice == "2":
            k_types = [KL_TYPE.K_30M]
            logger.info("将仅分析30分钟级别")
        elif level_choice == "3":
            k_types = [KL_TYPE.K_DAY, KL_TYPE.K_30M]
            logger.info("将分析日线和30分钟级别")
        else:
            logger.error("无效的级别选择，使用默认设置（全部级别）")
            k_types = [KL_TYPE.K_DAY, KL_TYPE.K_30M]
            logger.info("将分析日线和30分钟级别")

        # 连续分析模式
        while True:
            print("\n" + "="*50)
            stock_code = input("请输入6位股票代码（输入'q'或'quit'退出）: ").strip()

            if stock_code.lower() in ['q', 'quit', '']:
                print("退出调试模式...")
                break

            if len(stock_code) != 6 or not stock_code.isdigit():
                print("请输入有效的6位股票代码！")
                continue

            print(f"开始分析股票: {stock_code}")
            print(f"\n开始调试分析股票 {stock_code}...")
            debug_single_stock(stock_code, k_types)
            print("调试分析完成！")

    elif mode == "1":
        # 批量分析模式
        # 获取用户输入的XLS文件路径
        xls_file = input("请输入XLS文件路径: ").strip()
        if not xls_file:
            logger.error("未提供文件路径，程序退出...")
            return

        # 移除引号（如果有）
        xls_file = xls_file.strip('"').strip("'")

        # 规范化路径
        try:
            xls_file = os.path.normpath(xls_file)
            logger.info(f"规范化路径: {xls_file}")
        except Exception as e:
            logger.error(f"路径规范化出错: {e}")

        # 检查文件是否存在
        if not os.path.exists(xls_file):
            logger.error(f"文件不存在: {xls_file}")
            # 尝试列出目录中的文件以帮助调试
            try:
                dir_path = os.path.dirname(xls_file)
                if os.path.exists(dir_path):
                    logger.info(f"目录 {dir_path} 中的文件:")
                    for f in os.listdir(dir_path):
                        if f.endswith(('.xls', '.xlsx')):
                            logger.info(f"  {f}")
            except Exception as e:
                logger.error(f"无法列出目录: {e}")
            return

        # 选择分析级别
        print("\n请选择分析级别:")
        print("1. 仅日线级别")
        print("2. 仅30分钟级别")
        print("3. 全部级别（日线+30分钟，任一满足即可）")
        
        level_choice = input("请输入级别编号 (1, 2 或 3): ").strip()
        
        if level_choice == "1":
            k_types = [KL_TYPE.K_DAY]
            logger.info("\n将仅分析日线级别")
        elif level_choice == "2":
            k_types = [KL_TYPE.K_30M]
            logger.info("\n将仅分析30分钟级别")
        elif level_choice == "3":
            k_types = [KL_TYPE.K_DAY, KL_TYPE.K_30M]
            logger.info("\n将同时分析日线和30分钟级别，只要任一级别满足条件即视为符合3买要求")
        else:
            logger.error("无效的级别选择，使用默认设置（全部级别）")
            k_types = [KL_TYPE.K_DAY, KL_TYPE.K_30M]
            logger.info("\n将同时分析日线和30分钟级别，只要任一级别满足条件即视为符合3买要求")

        # 开始分析
        analyze_stocks_from_xls(xls_file, k_types)

        logger.info("\n分析完成!")
        logger.info(f"详细日志已保存到: {LOG_FILE}")

    else:
        logger.error("无效的模式选择，程序退出...")
        return

    input("按回车键退出...")


if __name__ == "__main__":
    # 设置控制台编码
    try:
        locale.setlocale(locale.LC_ALL, 'Chinese')
    except:
        pass

    main()

